CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    uuid VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    nickname VA<PERSON>HA<PERSON>(255),
    avatar_url VARCHAR(255),
    locale VARCHAR(50),
    signin_type VARCHAR(50),
    signin_ip VARCHAR(255),
    signin_provider VARCHAR(50),
    signin_openid VARCHAR(255),
    UNIQUE (email, signin_provider)
);

CREATE TABLE orders (
    id SERIAL PRIMARY KEY,
    order_no VARCHAR(50) UNIQUE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    user_uuid VARCHAR(100) NOT NULL DEFAULT '',
    user_email VARCHAR(255) NOT NULL DEFAULT '',
    amount INTEGER NOT NULL,
    credits INTEGER NOT NULL,
    currency VARCHAR(10) DEFAULT 'USD',
    status VARCHAR(20) NOT NULL DEFAULT 'created',

    -- 产品信息
    product_id VARCHAR(100),
    product_name VARCHAR(200),
    valid_months INTEGER,
    expired_at TIMESTAMPTZ,

    -- 支付信息
    paid_at TIMESTAMPTZ,
    paid_email VARCHAR(255),
    paid_detail TEXT,

    -- PayPal专用字段
    paypal_order_id VARCHAR(100),
    paypal_subscription_id VARCHAR(100),
    paypal_plan_id VARCHAR(100),

    -- 订阅相关字段
    order_type VARCHAR(20) DEFAULT 'one_time',
    subscription_status VARCHAR(20),
    next_billing_time TIMESTAMPTZ,
    parent_order_no VARCHAR(50),
    billing_cycle_count INTEGER DEFAULT 0
);


CREATE TABLE apikeys (
    id SERIAL PRIMARY KEY,
    api_key VARCHAR(255) UNIQUE NOT NULL,
    title VARCHAR(100),
    user_uuid VARCHAR(255) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    status VARCHAR(50)
);

CREATE TABLE credits (
    id SERIAL PRIMARY KEY,
    trans_no VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    user_uuid VARCHAR(255) NOT NULL,
    trans_type VARCHAR(50) NOT NULL,
    credits INTEGER NOT NULL,
    order_no VARCHAR(255),
    expired_at TIMESTAMPTZ
);

CREATE TABLE posts (
    id SERIAL PRIMARY KEY,
    uuid VARCHAR(255) UNIQUE NOT NULL,
    slug VARCHAR(255),
    title VARCHAR(255),
    description TEXT,
    content TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    status VARCHAR(50),
    cover_url VARCHAR(255),
    author_name VARCHAR(255),
    author_avatar_url VARCHAR(255),
    locale VARCHAR(50)
);

CREATE TABLE photos (
    id SERIAL PRIMARY KEY,
    uuid VARCHAR(255) UNIQUE NOT NULL,
    user_uuid VARCHAR(255),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    img_description TEXT,
    img_url VARCHAR(255),
    status VARCHAR(50),
    generation_type VARCHAR(20) DEFAULT 'text' CHECK (generation_type IN ('text', 'image'))
);

CREATE TABLE webhook_events (
    id SERIAL PRIMARY KEY,
    event_id VARCHAR(255) UNIQUE NOT NULL,
    processed_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS async_tasks (
    uuid VARCHAR(36) PRIMARY KEY,
    user_uuid VARCHAR(36) NOT NULL,
    provider VARCHAR(20) NOT NULL, -- 'kie', 'replicate', 'apicore'
    task_id VARCHAR(100) NOT NULL UNIQUE, -- 第三方API返回的任务ID
    task_type VARCHAR(20) NOT NULL, -- 'text-to-image', 'image-to-image'
    status VARCHAR(20) NOT NULL DEFAULT 'pending', -- 'pending', 'generating', 'success', 'failed', 'expired'
    prompt TEXT NOT NULL,
    model VARCHAR(100),
    size VARCHAR(20),
    input_image TEXT, -- base64或URL
    result_images JSONB, -- 生成的图片URL数组
    error_message TEXT,
    callback_data JSONB, -- 存储回调数据
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    duration_seconds INTEGER -- 任务完成耗时（秒）
);

