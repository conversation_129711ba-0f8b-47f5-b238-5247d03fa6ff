# PayPal支付系统完整集成指南

> 基于Next.js + TypeScript的企业级PayPal支付解决方案
> 
> 适用于: SaaS订阅、数字商品、积分充值等场景

## 📋 目录

1. [系统架构概览](#系统架构概览)
2. [环境配置](#环境配置)
3. [数据库设计](#数据库设计)
4. [核心代码实现](#核心代码实现)
5. [支付流程详解](#支付流程详解)
6. [Webhook验证系统](#webhook验证系统)
7. [订阅支付实现](#订阅支付实现)
8. [前端集成](#前端集成)
9. [错误处理机制](#错误处理机制)
10. [部署与监控](#部署与监控)
11. [最佳实践](#最佳实践)

---

## 🏗️ 系统架构概览

### 整体架构图

```mermaid
graph TB
    A[用户前端] --> B[Next.js应用]
    B --> C[PayPal Checkout API]
    C --> D[PayPal服务器]
    D --> E[Webhook通知]
    E --> F[Webhook验证]
    F --> G[订单状态更新]
    G --> H[积分系统]
    B --> I[管理后台]
    I --> J[退款API]
    J --> K[PayPal Refund API]
    
    subgraph "支付流程"
        L[创建订单] --> M[PayPal支付]
        M --> N[支付确认]
        N --> O[积分发放]
    end
    
    subgraph "订阅流程"
        P[创建订阅] --> Q[PayPal订阅]
        Q --> R[周期扣费]
        R --> S[自动续费]
    end
```

### 核心组件

- **PayPal Client**: 统一的PayPal API客户端
- **Webhook验证**: 安全的支付通知处理
- **订单管理**: 完整的订单生命周期管理
- **积分系统**: 自动化的积分发放和管理
- **订阅管理**: 支持周期性扣费的订阅系统

---

## ⚙️ 环境配置

### 1. PayPal开发者账户设置

1. 访问 [PayPal Developer](https://developer.paypal.com/)
2. 创建应用并获取以下信息：
   - Client ID
   - Client Secret
   - Webhook ID

### 2. 环境变量配置

```bash
# PayPal基础配置
PAYPAL_ENVIRONMENT=sandbox  # 或 production
PAYPAL_CLIENT_ID=your_client_id
PAYPAL_CLIENT_SECRET=your_client_secret
PAYPAL_WEBHOOK_ID=your_webhook_id

# 应用配置
NEXT_PUBLIC_PROJECT_NAME=YourProjectName
NEXT_PUBLIC_SITE_URL=https://yourdomain.com

# PayPal订阅计划ID（需要通过API创建）
PAYPAL_BASIC_MONTHLY_PLAN_ID=P-xxx
PAYPAL_PRO_MONTHLY_PLAN_ID=P-xxx
PAYPAL_PREMIUM_MONTHLY_PLAN_ID=P-xxx
PAYPAL_BASIC_YEARLY_PLAN_ID=P-xxx
PAYPAL_PRO_YEARLY_PLAN_ID=P-xxx
PAYPAL_PREMIUM_YEARLY_PLAN_ID=P-xxx
```

### 3. Webhook配置

在PayPal开发者控制台配置Webhook URL：
```
https://yourdomain.com/api/paypal-notify
```

需要监听的事件：
- `CHECKOUT.ORDER.APPROVED`
- `PAYMENT.CAPTURE.COMPLETED`
- `BILLING.SUBSCRIPTION.CREATED`
- `BILLING.SUBSCRIPTION.ACTIVATED`
- `BILLING.SUBSCRIPTION.RENEWED`
- `PAYMENT.SALE.COMPLETED`

---

## 🗄️ 数据库设计

### 订单表结构

```sql
CREATE TABLE orders (
    id SERIAL PRIMARY KEY,
    order_no VARCHAR(50) UNIQUE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    user_uuid VARCHAR(100) NOT NULL DEFAULT '',
    user_email VARCHAR(255) NOT NULL DEFAULT '',
    amount INTEGER NOT NULL,
    credits INTEGER NOT NULL,
    currency VARCHAR(10) DEFAULT 'USD',
    status VARCHAR(20) NOT NULL DEFAULT 'created',

    -- 产品信息
    product_id VARCHAR(100),
    product_name VARCHAR(200),
    valid_months INTEGER,
    expired_at TIMESTAMPTZ,

    -- 支付信息
    paid_at TIMESTAMPTZ,
    paid_email VARCHAR(255),
    paid_detail TEXT,

    -- PayPal专用字段
    paypal_order_id VARCHAR(100),
    paypal_subscription_id VARCHAR(100),
    paypal_plan_id VARCHAR(100),

    -- 订阅相关字段
    order_type VARCHAR(20) DEFAULT 'one_time',
    subscription_status VARCHAR(20),
    next_billing_time TIMESTAMPTZ,
    parent_order_no VARCHAR(50),
    billing_cycle_count INTEGER DEFAULT 0
);
```

### 积分表结构

```sql
CREATE TABLE credits (
    id SERIAL PRIMARY KEY,
    trans_no VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    user_uuid VARCHAR(255) NOT NULL,
    trans_type VARCHAR(50) NOT NULL,
    credits INTEGER NOT NULL,
    order_no VARCHAR(255),
    expired_at TIMESTAMPTZ
);
```

### 索引优化

```sql
-- 订单查询优化
CREATE INDEX idx_orders_user_uuid ON orders(user_uuid);
CREATE INDEX idx_orders_paypal_order_id ON orders(paypal_order_id);
CREATE INDEX idx_orders_paypal_subscription_id ON orders(paypal_subscription_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_order_type ON orders(order_type);

-- 积分查询优化
CREATE INDEX idx_credits_user_uuid ON credits(user_uuid);
CREATE INDEX idx_credits_expired_at ON credits(expired_at);
CREATE INDEX idx_credits_trans_type ON credits(trans_type);
```

---

## 💻 核心代码实现

### 1. PayPal配置管理

```typescript
// lib/paypal-config.ts
export interface PayPalConfig {
  baseUrl: string;
  clientId: string;
  clientSecret: string;
  webhookId: string;
  environment: 'sandbox' | 'production';
  projectName: string;
  webUrl: string;
}

export function getPayPalConfig(): PayPalConfig {
  const environment = process.env.PAYPAL_ENVIRONMENT as 'sandbox' | 'production' || 'sandbox';
  
  return {
    baseUrl: environment === 'production' 
      ? 'https://api-m.paypal.com' 
      : 'https://api.sandbox.paypal.com',
    clientId: process.env.PAYPAL_CLIENT_ID || '',
    clientSecret: process.env.PAYPAL_CLIENT_SECRET || '',
    webhookId: process.env.PAYPAL_WEBHOOK_ID || '',
    environment,
    projectName: process.env.NEXT_PUBLIC_PROJECT_NAME || 'YourProject',
    webUrl: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
  };
}

// PayPal应用上下文配置
export const PAYPAL_APPLICATION_CONTEXT = {
  user_action: 'PAY_NOW',
  shipping_preference: 'NO_SHIPPING',
  payment_method: {
    payee_preferred: 'IMMEDIATE_PAYMENT_REQUIRED'
  }
};

// 构建PayPal回调URL
export function buildPayPalUrls(returnUrl?: string, cancelUrl?: string) {
  const config = getPayPalConfig();
  const baseUrl = config.webUrl;
  
  return {
    successUrl: returnUrl || `${baseUrl}/payment/success`,
    cancelUrl: cancelUrl || `${baseUrl}/payment/cancel`
  };
}
```

### 2. PayPal API客户端

```typescript
// lib/paypal-client.ts
export class PayPalClient {
  private config: PayPalConfig | null = null;
  private accessToken: string | null = null;
  private tokenExpiry: number = 0;

  private getConfig(): PayPalConfig {
    if (!this.config) {
      this.config = getPayPalConfig();
    }
    return this.config;
  }

  async getAccessToken(): Promise<string> {
    // 检查缓存的token是否还有效（提前5分钟刷新）
    if (this.accessToken && Date.now() < this.tokenExpiry - 300000) {
      return this.accessToken;
    }

    const config = this.getConfig();
    const auth = Buffer.from(`${config.clientId}:${config.clientSecret}`).toString('base64');

    const response = await fetch(`${config.baseUrl}/v1/oauth2/token`, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${auth}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: 'grant_type=client_credentials',
    });

    if (!response.ok) {
      throw new Error(`PayPal token request failed: ${response.status}`);
    }

    const data = await response.json();
    this.accessToken = data.access_token;
    this.tokenExpiry = Date.now() + (data.expires_in * 1000);

    return this.accessToken;
  }

  async createOrder(orderData: any): Promise<any> {
    const accessToken = await this.getAccessToken();
    const config = this.getConfig();

    const response = await fetch(`${config.baseUrl}/v2/checkout/orders`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(orderData)
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`PayPal order creation failed: ${response.status} ${errorData}`);
    }

    return await response.json();
  }

  async captureOrder(orderId: string): Promise<any> {
    const accessToken = await this.getAccessToken();
    const config = this.getConfig();

    const response = await fetch(`${config.baseUrl}/v2/checkout/orders/${orderId}/capture`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      }
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`PayPal order capture failed: ${response.status} ${errorData}`);
    }

    return await response.json();
  }

  async createSubscription(subscriptionData: any): Promise<any> {
    const accessToken = await this.getAccessToken();
    const config = this.getConfig();

    const response = await fetch(`${config.baseUrl}/v1/billing/subscriptions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(subscriptionData)
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`PayPal subscription creation failed: ${response.status} ${errorData}`);
    }

    return await response.json();
  }
}

export const paypalClient = new PayPalClient();
```

---

## 🔄 支付流程详解

### 1. 一次性支付流程

#### 步骤1: 创建PayPal订单

```typescript
// app/api/checkout-paypal/route.ts
export async function POST(request: NextRequest) {
  try {
    const {
      product_id,
      product_name,
      credits,
      amount,
      currency = "USD",
      valid_months = 12,
      returnUrl,
      cancelUrl
    } = await request.json();

    // 1. 验证用户登录状态
    const user_uuid = await getUserUuid();
    const user_email = await getUserEmail();

    if (!user_uuid) {
      return NextResponse.json({ error: '请先登录' }, { status: 401 });
    }

    // 2. 创建本地订单
    const order_no = getSnowId();
    const order: Order = {
      order_no: order_no.toString(),
      created_at: new Date().toISOString(),
      user_uuid: user_uuid,
      user_email: user_email,
      amount: Math.round(amount),
      expired_at: new Date(Date.now() + valid_months * 30 * 24 * 60 * 60 * 1000).toISOString(),
      status: "created",
      credits: credits,
      currency: currency.toLowerCase(),
      product_id: product_id,
      product_name: product_name,
      valid_months: valid_months,
      order_type: 'one_time'
    };

    await insertOrder(order);

    // 3. 构建PayPal订单数据
    const config = getPayPalConfig();
    const { successUrl, cancelUrl: cancelUrlFinal } = buildPayPalUrls(returnUrl, cancelUrl);

    const paypalOrder = {
      intent: 'CAPTURE',
      application_context: {
        return_url: successUrl,
        cancel_url: cancelUrlFinal,
        brand_name: config.projectName,
        ...PAYPAL_APPLICATION_CONTEXT
      },
      purchase_units: [{
        reference_id: order_no.toString(),
        amount: {
          currency_code: currency.toUpperCase(),
          value: (amount / 100).toFixed(2) // 转换为元
        },
        description: product_name,
        custom_id: user_uuid
      }]
    };

    // 4. 创建PayPal订单
    const paypalOrderResult = await paypalClient.createOrder(paypalOrder);

    // 5. 更新本地订单的PayPal ID
    await updateOrderPayPalId(order_no.toString(), paypalOrderResult.id);

    // 6. 返回支付链接
    const approvalUrl = paypalOrderResult.links?.find((link: any) => link.rel === 'approve')?.href;

    return respData({
      approvalUrl,
      order_no: order_no.toString(),
      paypal_order_id: paypalOrderResult.id
    });

  } catch (error) {
    console.error('PayPal checkout error:', error);
    return respErr('支付创建失败');
  }
}
```

#### 步骤2: 支付成功处理

```typescript
// app/api/paypal-payment-process/route.ts
export async function POST(request: NextRequest) {
  try {
    const { paypal_order_id, payer_id } = await request.json();

    if (!paypal_order_id) {
      return respErr('Missing PayPal order ID');
    }

    // 1. 查找本地订单
    const order = await findOrderByPayPalId(paypal_order_id);
    if (!order) {
      return respErr('Order not found');
    }

    // 2. 验证PayPal订单状态
    const paypalOrderDetails = await paypalClient.getOrderDetails(paypal_order_id);

    if (paypalOrderDetails.status !== 'APPROVED') {
      return respErr('PayPal order not approved');
    }

    // 3. 捕获PayPal支付
    const captureResult = await paypalClient.captureOrder(paypal_order_id);

    if (captureResult.status !== 'COMPLETED') {
      return respErr('Payment capture failed');
    }

    // 4. 更新订单状态
    const paidDetail = {
      paypal_order_id,
      payer_id,
      capture_result: captureResult,
      processed_at: new Date().toISOString(),
      processed_by: 'api'
    };

    await updateOrderStatus(
      order.order_no,
      'paid',
      new Date().toISOString(),
      paypalOrderDetails.payer?.email_address || '',
      JSON.stringify(paidDetail)
    );

    // 5. 增加用户积分
    if (order.user_uuid && order.credits > 0) {
      await increaseCredits({
        user_uuid: order.user_uuid,
        trans_type: CreditsTransType.OrderPay,
        credits: order.credits,
        expired_at: order.expired_at,
        order_no: order.order_no
      });
    }

    return respData({
      success: true,
      message: 'Payment processed successfully',
      order_no: order.order_no,
      credits: order.credits,
      paid_at: new Date().toISOString()
    });

  } catch (error) {
    console.error('PayPal payment process error:', error);
    return respErr('Payment processing failed');
  }
}
```

### 2. 支付流程时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as API
    participant P as PayPal
    participant W as Webhook
    participant D as 数据库

    U->>F: 点击支付
    F->>A: POST /api/checkout-paypal
    A->>D: 创建本地订单
    A->>P: 创建PayPal订单
    P-->>A: 返回支付链接
    A-->>F: 返回支付链接
    F->>P: 跳转到PayPal
    U->>P: 完成支付
    P->>F: 跳转回成功页面
    F->>A: POST /api/paypal-payment-process
    A->>P: 验证并捕获支付
    A->>D: 更新订单状态
    A->>D: 增加用户积分
    A-->>F: 返回成功结果

    Note over W: 异步Webhook处理
    P->>W: 发送Webhook通知
    W->>D: 验证并更新订单
```

---

## 🔐 Webhook验证系统

### 1. Webhook验证实现

```typescript
// lib/paypal-webhook-verify.ts
import crypto from 'crypto';
import { getPayPalConfig } from './paypal-config';

export async function verifyPayPalWebhook(
  headers: any,
  body: string
): Promise<boolean> {
  try {
    const config = getPayPalConfig();

    // 获取必要的头部信息
    const authAlgo = headers['paypal-auth-algo'];
    const transmission_id = headers['paypal-transmission-id'];
    const cert_id = headers['paypal-cert-id'];
    const transmission_time = headers['paypal-transmission-time'];
    const webhook_id = config.webhookId;
    const webhook_event = body;

    // 构建验证请求
    const verifyData = {
      auth_algo: authAlgo,
      cert_id: cert_id,
      transmission_id: transmission_id,
      transmission_time: transmission_time,
      webhook_id: webhook_id,
      webhook_event: JSON.parse(webhook_event)
    };

    // 获取访问令牌
    const accessToken = await getPayPalAccessToken();

    // 发送验证请求到PayPal
    const response = await fetch(`${config.baseUrl}/v1/notifications/verify-webhook-signature`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
      },
      body: JSON.stringify(verifyData)
    });

    if (!response.ok) {
      console.error('PayPal webhook verification failed:', response.status);
      return false;
    }

    const result = await response.json();
    return result.verification_status === 'SUCCESS';

  } catch (error) {
    console.error('Webhook verification error:', error);
    return false;
  }
}
```

### 2. Webhook处理器

```typescript
// app/api/paypal-notify/route.ts
export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const headers = Object.fromEntries(request.headers.entries());

    // 1. 验证Webhook签名
    const isValid = await verifyPayPalWebhook(headers, body);
    if (!isValid) {
      console.error('Invalid PayPal webhook signature');
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
    }

    const event = JSON.parse(body);
    const eventType = event.event_type;

    console.log(`Processing PayPal webhook: ${eventType}`);

    // 2. 根据事件类型处理
    switch (eventType) {
      case 'CHECKOUT.ORDER.APPROVED':
        await handleOrderApproved(event);
        break;

      case 'PAYMENT.CAPTURE.COMPLETED':
        await handlePaymentCaptureCompleted(event);
        break;

      case 'BILLING.SUBSCRIPTION.CREATED':
        await handleSubscriptionCreated(event);
        break;

      case 'BILLING.SUBSCRIPTION.ACTIVATED':
        await handleSubscriptionActivated(event);
        break;

      case 'BILLING.SUBSCRIPTION.RENEWED':
        await handleSubscriptionRenewed(event);
        break;

      case 'PAYMENT.SALE.COMPLETED':
        await handlePaymentSaleCompleted(event);
        break;

      default:
        console.log(`Unhandled PayPal webhook event: ${eventType}`);
    }

    return NextResponse.json({ status: 'success' });

  } catch (error) {
    console.error('PayPal webhook processing error:', error);
    return NextResponse.json({ error: 'Processing failed' }, { status: 500 });
  }
}

// 处理订单批准事件
async function handleOrderApproved(event: any) {
  const resource = event.resource;
  const paypalOrderId = resource.id;

  const order = await findOrderByPayPalId(paypalOrderId);
  if (!order) {
    console.error(`Order not found for PayPal ID: ${paypalOrderId}`);
    return;
  }

  // 更新订单状态为已批准
  await updateOrderStatus(
    order.order_no,
    'approved',
    new Date().toISOString(),
    resource.payer?.email_address || '',
    JSON.stringify({
      order_approved: true,
      approval_details: resource,
      processed_at: new Date().toISOString(),
      processed_by: 'webhook'
    })
  );
}

// 处理支付捕获完成事件
async function handlePaymentCaptureCompleted(event: any) {
  const resource = event.resource;
  const paypalOrderId = resource.supplementary_data?.related_ids?.order_id;

  if (!paypalOrderId) {
    console.error('No PayPal order ID found in capture event');
    return;
  }

  const order = await findOrderByPayPalId(paypalOrderId);
  if (!order) {
    console.error(`Order not found for PayPal ID: ${paypalOrderId}`);
    return;
  }

  // 更新订单状态为已支付
  await updateOrderStatus(
    order.order_no,
    'paid',
    new Date().toISOString(),
    resource.payee?.email || '',
    JSON.stringify({
      capture_completed: true,
      capture_details: resource,
      processed_at: new Date().toISOString(),
      processed_by: 'webhook'
    })
  );

  // 增加用户积分
  if (order.user_uuid && order.credits > 0) {
    await increaseCredits({
      user_uuid: order.user_uuid,
      trans_type: CreditsTransType.OrderPay,
      credits: order.credits,
      expired_at: order.expired_at,
      order_no: order.order_no
    });
  }
}
```

---

## 🔄 订阅支付实现

### 1. PayPal订阅计划配置

```typescript
// lib/paypal-plans.ts
export interface PayPalPlanMapping {
  product_id: string;
  paypal_plan_id: string;
}

// PayPal Plan ID映射表
export const PAYPAL_PLAN_MAPPINGS: PayPalPlanMapping[] = [
  // 月度计划
  { product_id: 'basic_monthly', paypal_plan_id: process.env.PAYPAL_BASIC_MONTHLY_PLAN_ID || '' },
  { product_id: 'pro_monthly', paypal_plan_id: process.env.PAYPAL_PRO_MONTHLY_PLAN_ID || '' },
  { product_id: 'premium_monthly', paypal_plan_id: process.env.PAYPAL_PREMIUM_MONTHLY_PLAN_ID || '' },

  // 年度计划
  { product_id: 'basic_yearly', paypal_plan_id: process.env.PAYPAL_BASIC_YEARLY_PLAN_ID || '' },
  { product_id: 'pro_yearly', paypal_plan_id: process.env.PAYPAL_PRO_YEARLY_PLAN_ID || '' },
  { product_id: 'premium_yearly', paypal_plan_id: process.env.PAYPAL_PREMIUM_YEARLY_PLAN_ID || '' }
];

export function getPayPalPlanId(product_id: string): string | undefined {
  const mapping = PAYPAL_PLAN_MAPPINGS.find(mapping => mapping.product_id === product_id);
  if (mapping && !mapping.paypal_plan_id) {
    throw new Error(`PayPal Plan ID未配置: ${product_id}。请运行创建订阅计划脚本。`);
  }
  return mapping?.paypal_plan_id;
}
```

### 2. 创建PayPal订阅

```typescript
// app/api/paypal-subscription/route.ts
export async function POST(request: NextRequest) {
  try {
    const {
      product_id,
      product_name,
      credits,
      interval,
      amount,
      currency = "USD",
      valid_months = 1,
      returnUrl,
      cancelUrl
    } = await request.json();

    // 1. 验证用户登录
    const user_uuid = await getUserUuid();
    const user_email = await getUserEmail();

    if (!user_uuid) {
      return NextResponse.json({ error: '请先登录' }, { status: 401 });
    }

    // 2. 获取PayPal计划ID
    const paypalPlanId = getPayPalPlanId(product_id);
    if (!paypalPlanId) {
      return respErr(`不支持的订阅产品: ${product_id}`);
    }

    // 3. 创建本地订单
    const order_no = getSnowId();
    const order: Order = {
      order_no: order_no.toString(),
      created_at: new Date().toISOString(),
      user_uuid: user_uuid,
      user_email: user_email,
      amount: Math.round(amount),
      expired_at: new Date(Date.now() + valid_months * 30 * 24 * 60 * 60 * 1000).toISOString(),
      status: "created",
      credits: credits,
      currency: currency.toLowerCase(),
      product_id: product_id,
      product_name: product_name,
      valid_months: valid_months,
      order_type: 'subscription',
      paypal_plan_id: paypalPlanId
    };

    await insertOrder(order);

    // 4. 构建PayPal订阅数据
    const config = getPayPalConfig();
    const { successUrl, cancelUrl: cancelUrlFinal } = buildPayPalUrls(returnUrl, cancelUrl);

    const subscriptionData = {
      plan_id: paypalPlanId,
      start_time: new Date(Date.now() + 60000).toISOString(), // 1分钟后开始
      subscriber: {
        email_address: user_email
      },
      application_context: {
        brand_name: config.projectName,
        locale: 'en-US',
        return_url: successUrl,
        cancel_url: cancelUrlFinal,
        user_action: 'SUBSCRIBE_NOW',
        payment_method: {
          payer_selected: 'PAYPAL',
          payee_preferred: 'IMMEDIATE_PAYMENT_REQUIRED'
        }
      },
      custom_id: order_no.toString()
    };

    // 5. 创建PayPal订阅
    const subscriptionResult = await paypalClient.createSubscription(subscriptionData);

    // 6. 更新订单的PayPal订阅ID
    await updateOrderSubscriptionId(order_no.toString(), subscriptionResult.id);

    // 7. 返回订阅链接
    const approvalUrl = subscriptionResult.links?.find((link: any) => link.rel === 'approve')?.href;

    if (!approvalUrl) {
      throw new Error('PayPal未返回订阅链接');
    }

    return respData({
      approvalUrl,
      order_no: order_no.toString(),
      subscription_id: subscriptionResult.id
    });

  } catch (error) {
    console.error('PayPal subscription error:', error);
    return respErr('订阅创建失败');
  }
}
```

### 3. 订阅Webhook处理

```typescript
// 处理订阅激活事件
async function handleSubscriptionActivated(event: any) {
  const resource = event.resource;
  const subscriptionId = resource.id;

  const order = await findOrderBySubscriptionId(subscriptionId);
  if (!order) {
    console.error(`Order not found for subscription ID: ${subscriptionId}`);
    return;
  }

  // 更新订单状态
  await updateOrderSubscriptionStatus(
    order.order_no,
    subscriptionId,
    'ACTIVE',
    resource.billing_info?.next_billing_time,
    JSON.stringify({
      subscription_activated: true,
      subscription_details: resource,
      processed_at: new Date().toISOString(),
      processed_by: 'webhook'
    }),
    resource.plan_id || order.paypal_plan_id
  );

  // 增加用户积分（首次激活）
  if (order.user_uuid && order.credits > 0) {
    await increaseCredits({
      user_uuid: order.user_uuid,
      trans_type: CreditsTransType.OrderPay,
      credits: order.credits,
      expired_at: resource.billing_info?.next_billing_time || order.expired_at,
      order_no: order.order_no
    });
  }
}

// 处理订阅续费事件
async function handleSubscriptionRenewed(event: any) {
  const resource = event.resource;
  const subscriptionId = resource.id;

  // 处理订阅周期性扣费
  await handleSubscriptionRecurringPayment(subscriptionId, {
    subscription_renewed: true,
    renewal_details: resource,
    processed_at: new Date().toISOString(),
    processed_by: 'webhook'
  });
}

// 处理支付完成事件（订阅相关）
async function handlePaymentSaleCompleted(event: any) {
  const resource = event.resource;
  const billingAgreementId = resource.billing_agreement_id;

  if (!billingAgreementId) {
    return; // 不是订阅支付
  }

  // 查找订阅订单
  const order = await findOrderBySubscriptionId(billingAgreementId);
  if (!order) {
    console.error(`Order not found for billing agreement ID: ${billingAgreementId}`);
    return;
  }

  // 检查是否已有周期性订单
  const hasRecurringOrders = await checkRecurringOrderExists(order.order_no);

  if (!hasRecurringOrders && order.status !== 'paid') {
    // 首次支付 - 激活订阅
    await updateOrderStatus(
      order.order_no,
      'paid',
      new Date().toISOString(),
      resource.payee?.email || '',
      JSON.stringify({
        sale_completed: true,
        sale_details: resource,
        event_type: 'PAYMENT.SALE.COMPLETED',
        processed_at: new Date().toISOString(),
        processed_by: 'webhook'
      })
    );

    // 增加用户积分
    if (order.user_uuid && order.credits > 0) {
      await increaseCredits({
        user_uuid: order.user_uuid,
        trans_type: CreditsTransType.OrderPay,
        credits: order.credits,
        expired_at: order.expired_at,
        order_no: order.order_no
      });
    }
  } else {
    // 周期性支付
    await handleSubscriptionRecurringPayment(billingAgreementId, {
      sale_completed: true,
      sale_details: resource,
      processed_at: new Date().toISOString(),
      processed_by: 'webhook'
    });
  }
}
```

### 4. 订阅管理服务

```typescript
// services/subscription.ts
export async function handleSubscriptionRecurringPayment(
  subscriptionId: string,
  paymentDetails?: any
): Promise<void> {
  // 查找原始订阅订单
  const supabase = getSupabaseClient();
  const { data: originalOrder, error } = await supabase
    .from("orders")
    .select("*")
    .eq("paypal_subscription_id", subscriptionId)
    .eq("status", "paid")
    .is("parent_order_no", null)
    .single();

  if (error || !originalOrder) {
    return;
  }

  // 获取当前周期数
  const { data: recurringOrders } = await supabase
    .from("orders")
    .select("billing_cycle_count")
    .eq("parent_order_no", originalOrder.order_no)
    .order("billing_cycle_count", { ascending: false })
    .limit(1);

  const currentCycle = recurringOrders && recurringOrders.length > 0
    ? recurringOrders[0].billing_cycle_count + 1
    : 1;

  // 创建周期性订单
  const recurringOrder = await createRecurringOrder(originalOrder, currentCycle, paymentDetails);

  // 为用户增加积分
  if (originalOrder.user_uuid && originalOrder.credits > 0) {
    await increaseCredits({
      user_uuid: originalOrder.user_uuid,
      trans_type: CreditsTransType.OrderPay,
      credits: originalOrder.credits,
      expired_at: originalOrder.expired_at,
      order_no: recurringOrder.order_no
    });
  }
}

export async function createRecurringOrder(
  originalOrder: Order,
  cycleCount: number,
  paymentDetails?: any
): Promise<Order> {
  const recurringOrder: Order = {
    order_no: getSnowId().toString(),
    created_at: new Date().toISOString(),
    user_uuid: originalOrder.user_uuid,
    user_email: originalOrder.user_email,
    amount: originalOrder.amount,
    expired_at: originalOrder.expired_at,
    status: "paid",
    credits: originalOrder.credits,
    currency: originalOrder.currency,
    product_id: originalOrder.product_id,
    product_name: originalOrder.product_name,
    valid_months: originalOrder.valid_months,
    order_type: 'subscription',
    parent_order_no: originalOrder.order_no,
    billing_cycle_count: cycleCount,
    paypal_subscription_id: originalOrder.paypal_subscription_id,
    paypal_plan_id: originalOrder.paypal_plan_id,
    paid_at: new Date().toISOString(),
    paid_detail: paymentDetails ? JSON.stringify(paymentDetails) : undefined
  };

  await insertOrder(recurringOrder);
  return recurringOrder;
}
```

---

## 🎨 前端集成

### 1. 支付组件实现

```typescript
// components/ui/subscription-modal.tsx
import { useState } from 'react';
import { toast } from 'react-hot-toast';

interface PricingItem {
  product_id: string;
  product_name: string;
  credits: number;
  amount: number;
  currency: string;
  interval: 'month' | 'year';
  valid_months: number;
  is_subscription?: boolean;
}

export function SubscriptionModal() {
  const [isLoading, setIsLoading] = useState(false);
  const [loadingText, setLoadingText] = useState("Processing...");
  const [productId, setProductId] = useState<string | null>(null);

  // PayPal订阅支付处理
  const handlePayPalSubscription = async (item: PricingItem) => {
    try {
      setIsLoading(true);
      setProductId(item.product_id);
      setLoadingText("Creating subscription...");

      const response = await fetch("/api/paypal-subscription", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          product_id: item.product_id,
          product_name: item.product_name,
          credits: item.credits,
          interval: item.interval,
          amount: item.amount,
          currency: item.currency,
          valid_months: item.valid_months,
        }),
      });

      if (response.status === 401) {
        setIsLoading(false);
        setLoadingText("Processing...");
        setProductId(null);
        // 显示登录模态框
        return;
      }

      const result = await response.json();
      const { code, message, data } = result;

      if (code !== 0) {
        toast.error(message);
        setIsLoading(false);
        setLoadingText("Processing...");
        setProductId(null);
        return;
      }

      if (data?.approvalUrl) {
        setLoadingText("Redirecting to PayPal...");

        setTimeout(() => {
          window.location.href = data.approvalUrl;
        }, 500);
        return;
      } else {
        toast.error("订阅创建失败: 缺少支付链接");
        setIsLoading(false);
        setLoadingText("Processing...");
        setProductId(null);
      }
    } catch (error) {
      console.error("PayPal Subscription error:", error);
      toast.error("订阅创建失败");
      setIsLoading(false);
      setLoadingText("Processing...");
      setProductId(null);
    }
  };

  // PayPal一次性支付处理
  const handleRegularPayment = async (item: PricingItem) => {
    try {
      setIsLoading(true);
      setProductId(item.product_id);
      setLoadingText("Creating payment...");

      const response = await fetch("/api/checkout-paypal", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          product_id: item.product_id,
          product_name: item.product_name,
          credits: item.credits,
          interval: item.interval,
          amount: item.amount,
          currency: item.currency,
          valid_months: item.valid_months,
        }),
      });

      if (response.status === 401) {
        setIsLoading(false);
        setLoadingText("Processing...");
        setProductId(null);
        // 显示登录模态框
        return;
      }

      const result = await response.json();
      const { code, message, data } = result;

      if (code !== 0) {
        toast.error(message);
        setIsLoading(false);
        setLoadingText("Processing...");
        setProductId(null);
        return;
      }

      const { approvalUrl } = data;

      if (approvalUrl) {
        setLoadingText("Redirecting to PayPal...");

        setTimeout(() => {
          window.location.href = approvalUrl;
        }, 500);
      } else {
        toast.error("支付创建失败");
        setIsLoading(false);
        setLoadingText("Processing...");
        setProductId(null);
      }
    } catch (error) {
      console.error("PayPal payment error:", error);
      toast.error("支付创建失败");
      setIsLoading(false);
      setLoadingText("Processing...");
      setProductId(null);
    }
  };

  return (
    <div className="subscription-modal">
      {/* 支付按钮 */}
      <button
        onClick={() => item.is_subscription
          ? handlePayPalSubscription(item)
          : handleRegularPayment(item)
        }
        disabled={isLoading && productId === item.product_id}
        className="payment-button"
      >
        {isLoading && productId === item.product_id ? loadingText : "Pay with PayPal"}
      </button>
    </div>
  );
}
```

### 2. 支付成功页面

```typescript
// app/payment/success/page.tsx
'use client';

import { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';

export default function PaymentSuccessPage() {
  const [isProcessing, setIsProcessing] = useState(true);
  const [paymentResult, setPaymentResult] = useState<any>(null);
  const searchParams = useSearchParams();
  const router = useRouter();

  useEffect(() => {
    const processPayment = async () => {
      try {
        const paypalOrderId = searchParams.get('token');
        const payerId = searchParams.get('PayerID');
        const subscriptionId = searchParams.get('subscription_id');

        if (!paypalOrderId && !subscriptionId) {
          toast.error('缺少支付参数');
          router.push('/');
          return;
        }

        // 处理订阅支付成功
        if (subscriptionId) {
          // 订阅支付通常通过webhook处理，这里只需要显示成功信息
          setPaymentResult({
            success: true,
            message: '订阅创建成功',
            type: 'subscription'
          });
          setIsProcessing(false);

          // 3秒后跳转到我的订单页面
          setTimeout(() => {
            router.push('/my-orders');
          }, 3000);
          return;
        }

        // 处理一次性支付
        if (paypalOrderId) {
          const response = await fetch('/api/paypal-payment-process', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              paypal_order_id: paypalOrderId,
              payer_id: payerId,
            }),
          });

          const result = await response.json();

          if (result.code === 0) {
            setPaymentResult({
              success: true,
              message: '支付成功',
              credits: result.data.credits,
              order_no: result.data.order_no,
              type: 'one_time'
            });
            toast.success('支付成功！积分已到账');
          } else {
            setPaymentResult({
              success: false,
              message: result.message || '支付处理失败'
            });
            toast.error(result.message || '支付处理失败');
          }
        }

      } catch (error) {
        console.error('Payment processing error:', error);
        setPaymentResult({
          success: false,
          message: '支付处理异常'
        });
        toast.error('支付处理异常');
      } finally {
        setIsProcessing(false);

        // 5秒后自动跳转
        setTimeout(() => {
          router.push('/my-orders');
        }, 5000);
      }
    };

    processPayment();
  }, [searchParams, router]);

  if (isProcessing) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-lg">正在处理支付结果...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="max-w-md mx-auto text-center p-6 bg-white rounded-lg shadow-lg">
        {paymentResult?.success ? (
          <>
            <div className="text-green-600 text-6xl mb-4">✓</div>
            <h1 className="text-2xl font-bold text-green-600 mb-2">
              {paymentResult.type === 'subscription' ? '订阅成功' : '支付成功'}
            </h1>
            <p className="text-gray-600 mb-4">{paymentResult.message}</p>
            {paymentResult.credits && (
              <p className="text-lg font-semibold text-blue-600 mb-4">
                获得积分: {paymentResult.credits}
              </p>
            )}
            {paymentResult.order_no && (
              <p className="text-sm text-gray-500 mb-4">
                订单号: {paymentResult.order_no}
              </p>
            )}
            <p className="text-sm text-gray-500">
              页面将在5秒后自动跳转到我的订单...
            </p>
          </>
        ) : (
          <>
            <div className="text-red-600 text-6xl mb-4">✗</div>
            <h1 className="text-2xl font-bold text-red-600 mb-2">支付失败</h1>
            <p className="text-gray-600 mb-4">{paymentResult?.message}</p>
            <button
              onClick={() => router.push('/')}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
            >
              返回首页
            </button>
          </>
        )}
      </div>
    </div>
  );
}
```

### 3. 支付取消页面

```typescript
// app/payment/cancel/page.tsx
'use client';

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function PaymentCancelPage() {
  const router = useRouter();

  useEffect(() => {
    // 5秒后自动跳转回首页
    const timer = setTimeout(() => {
      router.push('/');
    }, 5000);

    return () => clearTimeout(timer);
  }, [router]);

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="max-w-md mx-auto text-center p-6 bg-white rounded-lg shadow-lg">
        <div className="text-yellow-600 text-6xl mb-4">⚠</div>
        <h1 className="text-2xl font-bold text-yellow-600 mb-2">支付已取消</h1>
        <p className="text-gray-600 mb-4">
          您已取消支付，如有需要可以重新发起支付。
        </p>
        <div className="space-y-3">
          <button
            onClick={() => router.push('/')}
            className="w-full bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
          >
            返回首页
          </button>
          <p className="text-sm text-gray-500">
            页面将在5秒后自动跳转...
          </p>
        </div>
      </div>
    </div>
  );
}
```

---

## ⚠️ 错误处理机制

### 1. 统一错误响应格式

```typescript
// lib/resp.ts
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data?: T;
}

export function respData<T>(data: T, message: string = 'success'): Response {
  return Response.json({
    code: 0,
    message,
    data
  } as ApiResponse<T>);
}

export function respErr(message: string, code: number = 1): Response {
  return Response.json({
    code,
    message
  } as ApiResponse);
}
```

### 2. PayPal错误处理

```typescript
// lib/paypal-error-handler.ts
export enum PayPalErrorType {
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  BUSINESS_ERROR = 'BUSINESS_ERROR',
  SYSTEM_ERROR = 'SYSTEM_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR'
}

export interface PayPalError {
  type: PayPalErrorType;
  code: string;
  message: string;
  details?: any;
}

export function handlePayPalError(error: any): PayPalError {
  // 网络错误
  if (error.name === 'TypeError' && error.message.includes('fetch')) {
    return {
      type: PayPalErrorType.NETWORK_ERROR,
      code: 'NETWORK_ERROR',
      message: 'PayPal服务连接失败，请稍后重试'
    };
  }

  // HTTP错误
  if (error.status) {
    switch (error.status) {
      case 401:
        return {
          type: PayPalErrorType.AUTHENTICATION_ERROR,
          code: 'UNAUTHORIZED',
          message: 'PayPal认证失败，请检查配置'
        };
      case 400:
        return {
          type: PayPalErrorType.VALIDATION_ERROR,
          code: 'BAD_REQUEST',
          message: 'PayPal请求参数错误'
        };
      case 422:
        return {
          type: PayPalErrorType.BUSINESS_ERROR,
          code: 'UNPROCESSABLE_ENTITY',
          message: 'PayPal业务规则验证失败'
        };
      case 500:
        return {
          type: PayPalErrorType.SYSTEM_ERROR,
          code: 'INTERNAL_SERVER_ERROR',
          message: 'PayPal服务器内部错误'
        };
      default:
        return {
          type: PayPalErrorType.SYSTEM_ERROR,
          code: 'UNKNOWN_ERROR',
          message: `PayPal服务异常: ${error.status}`
        };
    }
  }

  // 默认错误
  return {
    type: PayPalErrorType.SYSTEM_ERROR,
    code: 'UNKNOWN_ERROR',
    message: error.message || 'PayPal处理异常'
  };
}
```

### 3. 日志记录系统

```typescript
// lib/paypal-logger.ts
export enum PayPalOperation {
  TOKEN_REQUEST = 'TOKEN_REQUEST',
  ORDER_CREATE = 'ORDER_CREATE',
  ORDER_CAPTURE = 'ORDER_CAPTURE',
  SUBSCRIPTION_CREATE = 'SUBSCRIPTION_CREATE',
  WEBHOOK_VERIFY = 'WEBHOOK_VERIFY',
  WEBHOOK_PROCESS = 'WEBHOOK_PROCESS'
}

export interface LogContext {
  requestId: string;
  userId?: string;
  orderId?: string;
  subscriptionId?: string;
}

export class PayPalLogger {
  createContext(context: Partial<LogContext>): LogContext {
    return {
      requestId: context.requestId || crypto.randomUUID(),
      ...context
    };
  }

  info(operation: PayPalOperation, message: string, data?: any, context?: LogContext) {
    console.log(`[PayPal][${operation}][INFO] ${message}`, {
      context,
      data,
      timestamp: new Date().toISOString()
    });
  }

  error(operation: PayPalOperation, message: string, error: any, data?: any, context?: LogContext) {
    console.error(`[PayPal][${operation}][ERROR] ${message}`, {
      context,
      error: {
        message: error.message,
        stack: error.stack,
        name: error.name
      },
      data,
      timestamp: new Date().toISOString()
    });
  }

  warn(operation: PayPalOperation, message: string, data?: any, context?: LogContext) {
    console.warn(`[PayPal][${operation}][WARN] ${message}`, {
      context,
      data,
      timestamp: new Date().toISOString()
    });
  }
}

export const paypalLogger = new PayPalLogger();
```

### 4. 重试机制

```typescript
// lib/paypal-retry.ts
export interface RetryOptions {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
}

export async function withRetry<T>(
  operation: () => Promise<T>,
  options: RetryOptions = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffFactor: 2
  }
): Promise<T> {
  let lastError: Error;

  for (let attempt = 0; attempt <= options.maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;

      // 最后一次尝试失败，抛出错误
      if (attempt === options.maxRetries) {
        throw lastError;
      }

      // 计算延迟时间
      const delay = Math.min(
        options.baseDelay * Math.pow(options.backoffFactor, attempt),
        options.maxDelay
      );

      console.warn(`PayPal operation failed, retrying in ${delay}ms (attempt ${attempt + 1}/${options.maxRetries})`, {
        error: lastError.message,
        attempt: attempt + 1
      });

      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError!;
}
```

---

## 🚀 部署与监控

### 1. 环境变量检查

```typescript
// lib/env-check.ts
export function validatePayPalEnvironment(): void {
  const requiredEnvVars = [
    'PAYPAL_CLIENT_ID',
    'PAYPAL_CLIENT_SECRET',
    'PAYPAL_WEBHOOK_ID',
    'PAYPAL_ENVIRONMENT'
  ];

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    throw new Error(`Missing required PayPal environment variables: ${missingVars.join(', ')}`);
  }

  // 验证环境值
  const environment = process.env.PAYPAL_ENVIRONMENT;
  if (environment !== 'sandbox' && environment !== 'production') {
    throw new Error('PAYPAL_ENVIRONMENT must be either "sandbox" or "production"');
  }

  // 生产环境额外检查
  if (environment === 'production') {
    const productionVars = [
      'NEXT_PUBLIC_SITE_URL',
      'PAYPAL_BASIC_MONTHLY_PLAN_ID',
      'PAYPAL_PRO_MONTHLY_PLAN_ID',
      'PAYPAL_PREMIUM_MONTHLY_PLAN_ID'
    ];

    const missingProdVars = productionVars.filter(varName => !process.env[varName]);
    if (missingProdVars.length > 0) {
      console.warn(`Missing recommended production environment variables: ${missingProdVars.join(', ')}`);
    }
  }
}
```

### 2. 健康检查端点

```typescript
// app/api/health/paypal/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { paypalClient } from '@/lib/paypal-client';
import { validatePayPalEnvironment } from '@/lib/env-check';

export async function GET(request: NextRequest) {
  try {
    // 1. 检查环境变量
    validatePayPalEnvironment();

    // 2. 测试PayPal连接
    const accessToken = await paypalClient.getAccessToken();

    if (!accessToken) {
      throw new Error('Failed to obtain PayPal access token');
    }

    // 3. 返回健康状态
    return NextResponse.json({
      status: 'healthy',
      service: 'PayPal',
      environment: process.env.PAYPAL_ENVIRONMENT,
      timestamp: new Date().toISOString(),
      checks: {
        environment_variables: 'ok',
        paypal_connection: 'ok',
        access_token: 'ok'
      }
    });

  } catch (error) {
    return NextResponse.json({
      status: 'unhealthy',
      service: 'PayPal',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
```

### 3. 监控指标

```typescript
// lib/paypal-metrics.ts
export class PayPalMetrics {
  private static instance: PayPalMetrics;
  private metrics: Map<string, number> = new Map();

  static getInstance(): PayPalMetrics {
    if (!PayPalMetrics.instance) {
      PayPalMetrics.instance = new PayPalMetrics();
    }
    return PayPalMetrics.instance;
  }

  increment(metric: string, value: number = 1): void {
    const current = this.metrics.get(metric) || 0;
    this.metrics.set(metric, current + value);
  }

  getMetric(metric: string): number {
    return this.metrics.get(metric) || 0;
  }

  getAllMetrics(): Record<string, number> {
    return Object.fromEntries(this.metrics);
  }

  // 常用指标方法
  recordPaymentSuccess(): void {
    this.increment('paypal.payments.success');
  }

  recordPaymentFailure(): void {
    this.increment('paypal.payments.failure');
  }

  recordSubscriptionCreated(): void {
    this.increment('paypal.subscriptions.created');
  }

  recordWebhookReceived(eventType: string): void {
    this.increment(`paypal.webhooks.${eventType}`);
    this.increment('paypal.webhooks.total');
  }

  recordApiCall(endpoint: string): void {
    this.increment(`paypal.api.${endpoint}`);
    this.increment('paypal.api.total');
  }
}

export const paypalMetrics = PayPalMetrics.getInstance();
```

---

## 📋 最佳实践

### 1. 安全最佳实践

#### 环境变量安全
```bash
# 生产环境必须使用强密钥
PAYPAL_CLIENT_SECRET=your_production_secret_key

# 使用HTTPS
NEXT_PUBLIC_SITE_URL=https://yourdomain.com

# Webhook验证必须启用
PAYPAL_WEBHOOK_ID=your_webhook_id
```

#### API安全
- ✅ 所有PayPal API调用都在后端进行
- ✅ 前端不直接暴露PayPal密钥
- ✅ Webhook必须验证签名
- ✅ 订单金额在后端验证
- ✅ 用户权限验证

#### 数据安全
```typescript
// 敏感信息加密存储
const encryptedPaymentDetails = encrypt(JSON.stringify(paymentDetails));
await updateOrderStatus(orderNo, 'paid', paidAt, email, encryptedPaymentDetails);

// 日志中不记录敏感信息
console.log('Payment processed', {
  order_no: orderNo,
  amount: amount,
  // 不记录: paypal_order_id, payer_email 等敏感信息
});
```

### 2. 性能优化

#### 数据库优化
```sql
-- 关键索引
CREATE INDEX CONCURRENTLY idx_orders_paypal_order_id ON orders(paypal_order_id) WHERE paypal_order_id IS NOT NULL;
CREATE INDEX CONCURRENTLY idx_orders_user_status ON orders(user_uuid, status);
CREATE INDEX CONCURRENTLY idx_orders_subscription_active ON orders(paypal_subscription_id) WHERE subscription_status = 'ACTIVE';

-- 分区表（大量订单时）
CREATE TABLE orders_2024 PARTITION OF orders FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');
```

#### 缓存策略
```typescript
// PayPal访问令牌缓存
class PayPalTokenCache {
  private static token: string | null = null;
  private static expiry: number = 0;

  static async getToken(): Promise<string> {
    if (this.token && Date.now() < this.expiry - 300000) {
      return this.token;
    }

    // 重新获取令牌
    const newToken = await fetchNewToken();
    this.token = newToken.access_token;
    this.expiry = Date.now() + (newToken.expires_in * 1000);

    return this.token;
  }
}
```

#### 异步处理
```typescript
// Webhook异步处理
export async function POST(request: NextRequest) {
  // 快速响应PayPal
  const response = NextResponse.json({ status: 'received' });

  // 异步处理业务逻辑
  setImmediate(async () => {
    try {
      await processWebhookEvent(await request.json());
    } catch (error) {
      console.error('Webhook processing failed:', error);
      // 可以实现重试机制
    }
  });

  return response;
}
```

### 3. 监控和告警

#### 关键指标监控
```typescript
// 监控配置
const MONITORING_METRICS = {
  // 支付成功率
  payment_success_rate: {
    threshold: 0.95, // 95%以下告警
    window: '5m'
  },

  // Webhook处理延迟
  webhook_processing_time: {
    threshold: 5000, // 5秒以上告警
    window: '1m'
  },

  // API错误率
  api_error_rate: {
    threshold: 0.05, // 5%以上告警
    window: '5m'
  }
};
```

#### 告警通知
```typescript
// 告警服务
class AlertService {
  static async sendAlert(metric: string, value: number, threshold: number) {
    const message = `PayPal Alert: ${metric} = ${value}, threshold = ${threshold}`;

    // 发送到监控系统
    await fetch('https://your-monitoring-system.com/alerts', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        service: 'PayPal',
        metric,
        value,
        threshold,
        timestamp: new Date().toISOString()
      })
    });
  }
}
```

### 4. 测试策略

#### 单元测试
```typescript
// PayPal客户端测试
describe('PayPalClient', () => {
  it('should create order successfully', async () => {
    const mockOrderData = {
      intent: 'CAPTURE',
      purchase_units: [{ amount: { currency_code: 'USD', value: '10.00' } }]
    };

    const result = await paypalClient.createOrder(mockOrderData);

    expect(result.id).toBeDefined();
    expect(result.status).toBe('CREATED');
  });

  it('should handle authentication errors', async () => {
    // 模拟认证失败
    jest.spyOn(paypalClient, 'getAccessToken').mockRejectedValue(new Error('401 Unauthorized'));

    await expect(paypalClient.createOrder({})).rejects.toThrow('401 Unauthorized');
  });
});
```

#### 集成测试
```typescript
// Webhook集成测试
describe('PayPal Webhook', () => {
  it('should process payment completion webhook', async () => {
    const webhookEvent = {
      event_type: 'PAYMENT.CAPTURE.COMPLETED',
      resource: {
        id: 'test-capture-id',
        supplementary_data: {
          related_ids: { order_id: 'test-order-id' }
        }
      }
    };

    const response = await POST(createMockRequest(webhookEvent));

    expect(response.status).toBe(200);

    // 验证订单状态已更新
    const order = await findOrderByPayPalId('test-order-id');
    expect(order?.status).toBe('paid');
  });
});
```

### 5. 部署检查清单

#### 生产环境部署前检查
- [ ] 所有环境变量已正确配置
- [ ] PayPal Webhook URL已配置并可访问
- [ ] 数据库索引已创建
- [ ] SSL证书已配置
- [ ] 监控和告警已设置
- [ ] 备份策略已实施
- [ ] 错误日志收集已配置

#### 上线后验证
- [ ] 健康检查端点正常
- [ ] 测试支付流程完整
- [ ] Webhook接收正常
- [ ] 监控指标正常
- [ ] 日志记录正常

---

## 📚 总结

本文档提供了基于Next.js + TypeScript的完整PayPal支付系统集成方案，包括：

### 🎯 核心功能
- ✅ 一次性支付处理
- ✅ 订阅支付管理
- ✅ Webhook安全验证
- ✅ 积分系统集成
- ✅ 订单生命周期管理

### 🔧 技术特性
- ✅ TypeScript类型安全
- ✅ 统一错误处理
- ✅ 完整日志记录
- ✅ 性能优化
- ✅ 安全最佳实践

### 📊 监控运维
- ✅ 健康检查
- ✅ 指标监控
- ✅ 告警通知
- ✅ 测试覆盖

### 🚀 部署支持
- ✅ 环境配置
- ✅ 数据库迁移
- ✅ 部署检查
- ✅ 上线验证

通过遵循本指南，您可以快速构建一个稳定、安全、可扩展的PayPal支付系统。

---

## 📞 技术支持

如有问题，请参考：
1. [PayPal开发者文档](https://developer.paypal.com/docs/)
2. [Next.js官方文档](https://nextjs.org/docs)
3. 项目内部技术文档

---

*最后更新: 2024年12月*
```
