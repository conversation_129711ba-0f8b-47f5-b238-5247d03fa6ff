# PayPal支付系统API参考文档

## 📋 API概览

| 端点 | 方法 | 描述 | 认证 |
|------|------|------|------|
| `/api/checkout-paypal` | POST | 创建一次性支付订单 | 需要 |
| `/api/paypal-subscription` | POST | 创建订阅支付 | 需要 |
| `/api/paypal-payment-process` | POST | 处理支付结果 | 需要 |
| `/api/paypal-notify` | POST | PayPal Webhook通知 | 无 |
| `/api/health/paypal` | GET | PayPal服务健康检查 | 无 |

---

## 💳 一次性支付API

### POST /api/checkout-paypal

创建PayPal一次性支付订单

#### 请求参数

```typescript
interface CheckoutRequest {
  product_id: string;        // 产品ID
  product_name: string;      // 产品名称
  credits: number;           // 积分数量
  amount: number;            // 金额（分为单位）
  currency?: string;         // 货币代码，默认USD
  valid_months?: number;     // 有效期月数，默认12
  returnUrl?: string;        // 成功回调URL
  cancelUrl?: string;        // 取消回调URL
}
```

#### 请求示例

```bash
curl -X POST https://yourdomain.com/api/checkout-paypal \
  -H "Content-Type: application/json" \
  -H "Cookie: your-auth-cookie" \
  -d '{
    "product_id": "basic_plan",
    "product_name": "Basic Plan",
    "credits": 100,
    "amount": 999,
    "currency": "USD",
    "valid_months": 12
  }'
```

#### 响应格式

```typescript
interface CheckoutResponse {
  code: number;
  message: string;
  data?: {
    approvalUrl: string;      // PayPal支付链接
    order_no: string;         // 本地订单号
    paypal_order_id: string;  // PayPal订单ID
  };
}
```

#### 成功响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "approvalUrl": "https://www.sandbox.paypal.com/checkoutnow?token=xxx",
    "order_no": "1234567890",
    "paypal_order_id": "8XY12345678901234"
  }
}
```

---

## 🔄 订阅支付API

### POST /api/paypal-subscription

创建PayPal订阅支付

#### 请求参数

```typescript
interface SubscriptionRequest {
  product_id: string;        // 产品ID（必须在PAYPAL_PLAN_MAPPINGS中配置）
  product_name: string;      // 产品名称
  credits: number;           // 每期积分数量
  interval: 'month' | 'year'; // 订阅周期
  amount: number;            // 每期金额（分为单位）
  currency?: string;         // 货币代码，默认USD
  valid_months?: number;     // 每期有效期，默认1
  returnUrl?: string;        // 成功回调URL
  cancelUrl?: string;        // 取消回调URL
}
```

#### 请求示例

```bash
curl -X POST https://yourdomain.com/api/paypal-subscription \
  -H "Content-Type: application/json" \
  -H "Cookie: your-auth-cookie" \
  -d '{
    "product_id": "basic_monthly",
    "product_name": "Basic Monthly Plan",
    "credits": 100,
    "interval": "month",
    "amount": 999,
    "currency": "USD",
    "valid_months": 1
  }'
```

#### 响应格式

```typescript
interface SubscriptionResponse {
  code: number;
  message: string;
  data?: {
    approvalUrl: string;      // PayPal订阅链接
    order_no: string;         // 本地订单号
    subscription_id: string;  // PayPal订阅ID
  };
}
```

---

## ✅ 支付确认API

### POST /api/paypal-payment-process

处理PayPal支付成功后的确认

#### 请求参数

```typescript
interface PaymentProcessRequest {
  paypal_order_id: string;   // PayPal订单ID
  payer_id?: string;         // 付款人ID
}
```

#### 请求示例

```bash
curl -X POST https://yourdomain.com/api/paypal-payment-process \
  -H "Content-Type: application/json" \
  -H "Cookie: your-auth-cookie" \
  -d '{
    "paypal_order_id": "8XY12345678901234",
    "payer_id": "PAYER123456789"
  }'
```

#### 响应格式

```typescript
interface PaymentProcessResponse {
  code: number;
  message: string;
  data?: {
    success: boolean;
    order_no: string;         // 本地订单号
    credits: number;          // 获得的积分
    paid_at: string;          // 支付时间
  };
}
```

---

## 🔔 Webhook通知API

### POST /api/paypal-notify

接收PayPal Webhook通知（由PayPal服务器调用）

#### 支持的事件类型

| 事件类型 | 描述 | 处理逻辑 |
|----------|------|----------|
| `CHECKOUT.ORDER.APPROVED` | 订单已批准 | 更新订单状态为approved |
| `PAYMENT.CAPTURE.COMPLETED` | 支付捕获完成 | 更新订单状态为paid，发放积分 |
| `BILLING.SUBSCRIPTION.CREATED` | 订阅已创建 | 记录订阅创建事件 |
| `BILLING.SUBSCRIPTION.ACTIVATED` | 订阅已激活 | 激活订阅，发放首期积分 |
| `BILLING.SUBSCRIPTION.RENEWED` | 订阅已续费 | 创建周期性订单，发放积分 |
| `PAYMENT.SALE.COMPLETED` | 销售完成 | 处理订阅支付完成 |

#### Webhook验证

所有Webhook请求都会进行签名验证：

```typescript
const isValid = await verifyPayPalWebhook(headers, body);
if (!isValid) {
  return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
}
```

#### 响应格式

```json
{
  "status": "success"
}
```

---

## 🏥 健康检查API

### GET /api/health/paypal

检查PayPal服务连接状态

#### 响应格式

```typescript
interface HealthResponse {
  status: 'healthy' | 'unhealthy';
  service: string;
  environment: string;
  timestamp: string;
  checks?: {
    environment_variables: string;
    paypal_connection: string;
    access_token: string;
  };
  error?: string;
}
```

#### 成功响应示例

```json
{
  "status": "healthy",
  "service": "PayPal",
  "environment": "sandbox",
  "timestamp": "2024-12-09T10:00:00.000Z",
  "checks": {
    "environment_variables": "ok",
    "paypal_connection": "ok",
    "access_token": "ok"
  }
}
```

---

## 🔐 认证和授权

### 用户认证

大部分API需要用户登录状态：

```typescript
const user_uuid = await getUserUuid();
if (!user_uuid) {
  return NextResponse.json({ error: '请先登录' }, { status: 401 });
}
```

### Webhook认证

Webhook使用PayPal签名验证：

```typescript
const isValid = await verifyPayPalWebhook(headers, body);
```

---

## 📊 错误代码

| 错误代码 | 描述 | HTTP状态码 |
|----------|------|------------|
| 0 | 成功 | 200 |
| 1 | 通用错误 | 400 |
| 401 | 未授权 | 401 |
| 404 | 资源不存在 | 404 |
| 500 | 服务器内部错误 | 500 |

### 常见错误响应

```json
{
  "code": 1,
  "message": "缺少必需参数"
}
```

```json
{
  "code": 401,
  "message": "请先登录"
}
```

---

## 🔄 数据模型

### Order订单模型

```typescript
interface Order {
  order_no: string;                    // 订单号
  created_at: string;                  // 创建时间
  updated_at?: string;                 // 更新时间
  user_uuid: string;                   // 用户UUID
  user_email: string;                  // 用户邮箱
  amount: number;                      // 金额（分）
  credits: number;                     // 积分数量
  currency: string;                    // 货币
  status: string;                      // 状态：created, paid, refunded
  
  // 产品信息
  product_id?: string;                 // 产品ID
  product_name?: string;               // 产品名称
  valid_months?: number;               // 有效期月数
  expired_at?: string;                 // 过期时间
  
  // 支付信息
  paid_at?: string;                    // 支付时间
  paid_email?: string;                 // 支付邮箱
  paid_detail?: string;                // 支付详情JSON
  
  // PayPal字段
  paypal_order_id?: string;            // PayPal订单ID
  paypal_subscription_id?: string;     // PayPal订阅ID
  paypal_plan_id?: string;             // PayPal计划ID
  
  // 订阅字段
  order_type: 'one_time' | 'subscription'; // 订单类型
  subscription_status?: string;        // 订阅状态
  next_billing_time?: string;          // 下次计费时间
  parent_order_no?: string;            // 父订单号
  billing_cycle_count?: number;        // 计费周期数
}
```

### Credit积分模型

```typescript
interface Credit {
  id: number;                          // 主键ID
  trans_no: string;                    // 交易号
  created_at: string;                  // 创建时间
  user_uuid: string;                   // 用户UUID
  trans_type: string;                  // 交易类型
  credits: number;                     // 积分数量
  order_no?: string;                   // 关联订单号
  expired_at?: string;                 // 过期时间
}
```

---

*API参考文档 - 最后更新: 2024年12月*
