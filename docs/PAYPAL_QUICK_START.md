# PayPal支付系统快速开始指南

> 5分钟快速集成PayPal支付功能

## 🚀 快速开始

### 1. 环境配置

```bash
# .env.local
PAYPAL_ENVIRONMENT=sandbox
PAYPAL_CLIENT_ID=your_client_id
PAYPAL_CLIENT_SECRET=your_client_secret
PAYPAL_WEBHOOK_ID=your_webhook_id
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_PROJECT_NAME=YourProject
```

### 2. 数据库初始化

```sql
-- 执行 data/install.sql 中的表结构
-- 关键表：orders, credits
```

### 3. 核心文件结构

```
lib/
├── paypal-config.ts          # PayPal配置
├── paypal-client.ts          # PayPal API客户端
├── paypal-webhook-verify.ts  # Webhook验证
└── paypal-logger.ts          # 日志记录

app/api/
├── checkout-paypal/          # 创建支付订单
├── paypal-payment-process/   # 处理支付结果
├── paypal-subscription/      # 创建订阅
└── paypal-notify/           # Webhook处理

components/
└── ui/subscription-modal.tsx # 支付组件

app/payment/
├── success/                 # 支付成功页面
└── cancel/                  # 支付取消页面
```

## 💳 一次性支付集成

### 前端调用

```typescript
const handlePayment = async (item: PricingItem) => {
  const response = await fetch("/api/checkout-paypal", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      product_id: item.product_id,
      product_name: item.product_name,
      credits: item.credits,
      amount: item.amount,
      currency: item.currency,
      valid_months: item.valid_months,
    }),
  });

  const result = await response.json();
  if (result.code === 0) {
    window.location.href = result.data.approvalUrl;
  }
};
```

### 支付流程

1. 用户点击支付按钮
2. 调用 `/api/checkout-paypal` 创建订单
3. 跳转到PayPal支付页面
4. 用户完成支付后跳转回成功页面
5. 成功页面调用 `/api/paypal-payment-process` 确认支付
6. 系统更新订单状态并发放积分

## 🔄 订阅支付集成

### 前端调用

```typescript
const handleSubscription = async (item: PricingItem) => {
  const response = await fetch("/api/paypal-subscription", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      product_id: item.product_id,
      product_name: item.product_name,
      credits: item.credits,
      interval: item.interval,
      amount: item.amount,
      currency: item.currency,
      valid_months: item.valid_months,
    }),
  });

  const result = await response.json();
  if (result.code === 0) {
    window.location.href = result.data.approvalUrl;
  }
};
```

### 订阅流程

1. 用户选择订阅计划
2. 调用 `/api/paypal-subscription` 创建订阅
3. 跳转到PayPal订阅页面
4. 用户确认订阅后自动激活
5. Webhook处理订阅事件和周期扣费

## 🔔 Webhook配置

### PayPal控制台配置

1. 登录 [PayPal Developer](https://developer.paypal.com/)
2. 选择应用 → Webhooks
3. 添加Webhook URL: `https://yourdomain.com/api/paypal-notify`
4. 选择事件类型：
   - `CHECKOUT.ORDER.APPROVED`
   - `PAYMENT.CAPTURE.COMPLETED`
   - `BILLING.SUBSCRIPTION.CREATED`
   - `BILLING.SUBSCRIPTION.ACTIVATED`
   - `BILLING.SUBSCRIPTION.RENEWED`
   - `PAYMENT.SALE.COMPLETED`

### 关键事件处理

```typescript
// app/api/paypal-notify/route.ts
switch (eventType) {
  case 'PAYMENT.CAPTURE.COMPLETED':
    // 一次性支付完成
    await handlePaymentCaptureCompleted(event);
    break;
    
  case 'BILLING.SUBSCRIPTION.ACTIVATED':
    // 订阅激活
    await handleSubscriptionActivated(event);
    break;
    
  case 'BILLING.SUBSCRIPTION.RENEWED':
    // 订阅续费
    await handleSubscriptionRenewed(event);
    break;
}
```

## 🎨 前端页面

### 支付成功页面

```typescript
// app/payment/success/page.tsx
export default function PaymentSuccessPage() {
  useEffect(() => {
    const processPayment = async () => {
      const paypalOrderId = searchParams.get('token');
      const payerId = searchParams.get('PayerID');

      if (paypalOrderId) {
        const response = await fetch('/api/paypal-payment-process', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ paypal_order_id: paypalOrderId, payer_id: payerId }),
        });
        
        const result = await response.json();
        // 处理结果...
      }
    };
    
    processPayment();
  }, []);
  
  // 渲染成功页面...
}
```

## 🔧 常用工具函数

### 订单查询

```typescript
// 根据PayPal订单ID查询
const order = await findOrderByPayPalId(paypal_order_id);

// 根据订阅ID查询
const order = await findOrderBySubscriptionId(subscription_id);

// 更新订单状态
await updateOrderStatus(order_no, 'paid', paid_at, paid_email, paid_detail);
```

### 积分管理

```typescript
// 增加用户积分
await increaseCredits({
  user_uuid: user_uuid,
  trans_type: CreditsTransType.OrderPay,
  credits: credits,
  expired_at: expired_at,
  order_no: order_no
});
```

## ⚠️ 常见问题

### 1. 支付后积分未到账
- 检查Webhook是否正常接收
- 查看订单状态是否已更新为 'paid'
- 检查积分记录表是否有对应记录

### 2. 订阅无法创建
- 确认PayPal计划ID已正确配置
- 检查环境变量中的计划ID是否存在
- 验证用户邮箱格式是否正确

### 3. Webhook验证失败
- 确认Webhook ID配置正确
- 检查PayPal控制台中的Webhook URL是否可访问
- 验证SSL证书是否有效

## 🚀 部署检查

### 生产环境配置

```bash
# 生产环境变量
PAYPAL_ENVIRONMENT=production
PAYPAL_CLIENT_ID=production_client_id
PAYPAL_CLIENT_SECRET=production_client_secret
PAYPAL_WEBHOOK_ID=production_webhook_id
NEXT_PUBLIC_SITE_URL=https://yourdomain.com

# 订阅计划ID
PAYPAL_BASIC_MONTHLY_PLAN_ID=P-xxx
PAYPAL_PRO_MONTHLY_PLAN_ID=P-xxx
PAYPAL_PREMIUM_MONTHLY_PLAN_ID=P-xxx
```

### 健康检查

```bash
# 检查PayPal连接状态
curl https://yourdomain.com/api/health/paypal
```

## 📚 相关文档

- [完整集成指南](./PAYPAL_INTEGRATION_COMPLETE_GUIDE.md)
- [数据库迁移指南](./DATABASE_MIGRATION_GUIDE.md)
- [PayPal官方文档](https://developer.paypal.com/docs/)

---

*快速开始指南 - 最后更新: 2024年12月*
