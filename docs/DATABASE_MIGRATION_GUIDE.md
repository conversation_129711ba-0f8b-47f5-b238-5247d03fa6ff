# PayPal-only数据库迁移指南

## 概述

本指南说明如何将数据库从多支付提供商结构优化为PayPal专用结构。

## ⚠️ 重要提醒

**在执行迁移前，请务必备份数据库！**

```bash
# 备份数据库（示例）
pg_dump -h your-host -U your-user -d your-database > backup_$(date +%Y%m%d_%H%M%S).sql
```

## 迁移内容

### 1. 添加的PayPal专用字段

- `paypal_order_id` - PayPal订单ID
- `paypal_subscription_id` - PayPal订阅ID  
- `paypal_plan_id` - PayPal计划ID
- `subscription_status` - 订阅状态
- `next_billing_time` - 下次计费时间
- `order_type` - 订单类型（one_time/subscription）
- `parent_order_no` - 父订单号
- `billing_cycle_count` - 计费周期计数
- `updated_at` - 更新时间

### 2. 移除的多支付提供商字段

- `payment_provider` - 支付提供商标识
- `stripe_*` - 所有Stripe相关字段
- `creem_*` - 所有Creem相关字段
- `sub_*` - 通用订阅字段

### 3. 新增索引

- PayPal专用字段索引
- 复合查询优化索引
- 活跃订阅查询索引

### 4. 新增约束

- 订阅订单必须有PayPal订阅ID
- 订单类型有效性检查

### 5. 新增视图

- `active_subscriptions` - 活跃订阅视图

## 执行迁移

### 方法1: 直接执行SQL文件

```bash
# 连接到数据库并执行迁移
psql -h your-host -U your-user -d your-database -f data/migrations/paypal_only_optimization.sql
```

### 方法2: 通过Supabase Dashboard

1. 登录Supabase Dashboard
2. 进入SQL Editor
3. 复制`data/migrations/paypal_only_optimization.sql`内容
4. 执行SQL

### 方法3: 使用数据库管理工具

使用pgAdmin、DBeaver等工具执行SQL文件。

## 验证迁移

### 1. 检查表结构

```sql
-- 查看orders表结构
\d orders;

-- 检查新增字段
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'orders' 
AND column_name IN (
    'paypal_order_id', 'paypal_subscription_id', 'paypal_plan_id',
    'subscription_status', 'next_billing_time', 'order_type',
    'parent_order_no', 'billing_cycle_count', 'updated_at'
);
```

### 2. 检查索引

```sql
-- 查看orders表索引
SELECT indexname, indexdef 
FROM pg_indexes 
WHERE tablename = 'orders';
```

### 3. 检查约束

```sql
-- 查看表约束
SELECT conname, contype, pg_get_constraintdef(oid) 
FROM pg_constraint 
WHERE conrelid = 'orders'::regclass;
```

### 4. 检查视图

```sql
-- 测试活跃订阅视图
SELECT * FROM active_subscriptions LIMIT 5;
```

## 应用程序更新

迁移完成后，确保应用程序代码已更新：

1. ✅ Order类型定义已更新（`types/order.d.ts`）
2. ✅ 移除了多支付提供商相关函数
3. ✅ 更新了脚本文件引用

## 回滚计划

如果需要回滚，请：

1. 恢复数据库备份
2. 或手动添加回被删除的字段（不推荐）

## 测试建议

1. 运行应用程序确保无错误
2. 测试PayPal支付流程
3. 检查订单查询功能
4. 验证订阅管理功能

## 性能优化

迁移后的性能改进：

- 减少了不必要的字段存储
- 优化了查询索引
- 简化了数据模型
- 提高了查询效率

## 支持

如遇问题，请检查：

1. 数据库连接配置
2. 权限设置
3. SQL语法兼容性
4. 应用程序日志
