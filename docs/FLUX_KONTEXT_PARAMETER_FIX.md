# FLUX Kontext 模型参数修复文档

## 问题描述

在检查 FLUX Kontext 模型（flux-kontext-dev 和 flux-kontext-pro）的实现时发现参数传递存在问题：

1. **错误的参数传递方式**：
   - 当前代码将 `inputImage` 作为 AI SDK `generateImage` 函数的顶级参数
   - 但 AI SDK 不支持 `inputImage` 作为顶级参数
   - 应该通过 `providerOptions.replicate` 传递模型特定的参数

2. **缺少 flux-kontext-dev 模型配置**：
   - 配置文件中只有 flux-kontext-pro，缺少 flux-kontext-dev

## 修复内容

### 1. 修复参数传递方式

**修复前**：
```typescript
// ❌ 错误：将 inputImage 作为顶级参数传递
const generateOptions: any = {
  model: imageModel,
  prompt: prompt,
  // ...其他选项
};

if (inputImage) {
  generateOptions.inputImage = inputImage; // 这是错误的
}
```

**修复后**：
```typescript
// ✅ 正确：通过 providerOptions.replicate 传递参数
const replicateOptions: any = {
  output_quality: quality,
  ...otherOptions
};

if (inputImage) {
  if (config.model.includes('flux-kontext')) {
    // FLUX Kontext 模型使用 'input_image' 参数
    replicateOptions.input_image = inputImage;
  }
  // ...其他模型的参数映射
}

const generateOptions: any = {
  model: imageModel,
  prompt: prompt,
  providerOptions: {
    replicate: replicateOptions
  }
};
```

### 2. 添加 flux-kontext-dev 模型配置

在 `aisdk/provider/config.ts` 中添加：

```typescript
{
  id: 'flux-kontext-dev',
  name: 'FLUX Kontext Dev',
  provider: 'replicate',
  model: 'black-forest-labs/flux-kontext-dev',
  costCredits: 3,
  maxImages: 1,
  supportedModes: ['text-to-image', 'image-to-image'],
  defaultForText: false,
  defaultForImage: false,
  description: '开源版本的图像编辑模型，通过文本指令编辑图像',
  capabilities: ['open-source', 'context-aware', 'image-editing', 'text-guided-editing']
}
```

### 3. 完善参数映射逻辑

支持多种 FLUX 模型的参数映射：

| 模型类型 | 参数名称 | 用途 |
|----------|----------|------|
| flux-kontext-* | `input_image` | 图像编辑 |
| flux-fill-* | `image` | 图像填充/修复 |
| flux-canny-* | `control_image` | 边缘引导生成 |
| flux-depth-* | `control_image` | 深度引导生成 |
| 其他模型 | `input_image` | 默认参数 |

### 4. 增强调试和错误处理

- 添加详细的参数映射日志
- 增强错误信息，包含模型和参数信息
- 添加参数验证逻辑

## API 调用结构

### 文生图模式（Text-to-Image）
```json
{
  "model": "black-forest-labs/flux-kontext-pro",
  "prompt": "A beautiful landscape",
  "providerOptions": {
    "replicate": {
      "output_quality": 90
    }
  },
  "aspectRatio": "1:1"
}
```

### 图生图模式（Image-to-Image）
```json
{
  "model": "black-forest-labs/flux-kontext-pro", 
  "prompt": "Convert this image to a coloring page",
  "providerOptions": {
    "replicate": {
      "output_quality": 90,
      "input_image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD..."
    }
  },
  "aspectRatio": "1:1"
}
```

## 测试验证

创建了测试脚本 `test-flux-kontext-params.js` 来验证修复：

```bash
node test-flux-kontext-params.js
```

测试结果确认：
- ✅ flux-kontext-dev 使用 `input_image` 参数
- ✅ flux-kontext-pro 使用 `input_image` 参数  
- ✅ 参数正确传递到 `providerOptions.replicate`
- ✅ 支持文生图和图生图两种模式

## 影响范围

这个修复影响：
- 涂色页图生图功能
- FLUX Kontext 模型的所有调用
- 图像编辑相关功能

## 注意事项

1. **向后兼容性**：修复保持了 API 接口兼容性
2. **参数格式**：输入图像需要是 base64 格式或 URL
3. **模型选择**：确保使用正确的模型 ID（flux-kontext-dev 或 flux-kontext-pro）

## 相关文档

- [Replicate FLUX Kontext Pro](https://replicate.com/black-forest-labs/flux-kontext-pro)
- [Replicate FLUX Kontext Dev](https://replicate.com/black-forest-labs/flux-kontext-dev)
- [AI SDK Replicate Provider](https://ai-sdk.dev/providers/ai-sdk-providers/replicate)
