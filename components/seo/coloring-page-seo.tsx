import { ColoringPage } from '@/types/coloring-category';

interface ColoringPageSEOProps {
  coloringPage: ColoringPage;
  locale?: string;
}

// 这个组件现在只返回结构化数据，meta标签通过metadata API处理
export default function ColoringPageSEO({ coloringPage, locale = 'en' }: ColoringPageSEOProps) {
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://makecoloring.com';
  const pageUrl = locale === 'en'
    ? `${baseUrl}${coloringPage.seo_slug}`
    : `${baseUrl}/${locale}${coloringPage.seo_slug}`;

  // 生成面包屑结构化数据
  const breadcrumbJsonLd = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Home",
        "item": baseUrl
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Free Coloring Pages",
        "item": `${baseUrl}/free-coloring-pages`
      },
      {
        "@type": "ListItem",
        "position": 3,
        "name": coloringPage.title,
        "item": pageUrl
      }
    ]
  };

  // 生成图片结构化数据
  const imageJsonLd = {
    "@context": "https://schema.org",
    "@type": "ImageObject",
    "url": coloringPage.image_url,
    "description": coloringPage.alt_text || coloringPage.title,
    "name": coloringPage.title,
    "width": "400",
    "height": "400",
    "encodingFormat": "image/jpeg",
    "license": "https://creativecommons.org/licenses/by-nc/4.0/",
    "acquireLicensePage": pageUrl,
    "creditText": "MakeColoring.com",
    "creator": {
      "@type": "Organization",
      "name": "MakeColoring.com"
    }
  };

  // 生成网页结构化数据
  const webPageJsonLd = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "@id": pageUrl,
    "url": pageUrl,
    "name": coloringPage.meta_title || coloringPage.title,
    "description": coloringPage.meta_description || coloringPage.description,
    "inLanguage": locale,
    "isPartOf": {
      "@type": "WebSite",
      "@id": `${baseUrl}/#website`,
      "url": baseUrl,
      "name": "MakeColoring.com",
      "description": "Free printable coloring pages for kids and adults",
      "publisher": {
        "@type": "Organization",
        "name": "MakeColoring.com"
      }
    },
    "primaryImageOfPage": {
      "@id": `${pageUrl}#primaryimage`
    },
    "datePublished": coloringPage.created_at,
    "dateModified": coloringPage.updated_at || coloringPage.created_at
  };

  return (
    <>
      {/* 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbJsonLd)
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(imageJsonLd)
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(webPageJsonLd)
        }}
      />
    </>
  );
}

// 导出生成metadata的函数
export function generateColoringPageMetadata(coloringPage: ColoringPage, locale: string = 'en') {
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://makecoloring.com';
  const pageUrl = locale === 'en'
    ? `${baseUrl}${coloringPage.seo_slug}`
    : `${baseUrl}/${locale}${coloringPage.seo_slug}`;

  // 生成hreflang链接
  const locales = ['en', 'ru', 'hi', 'vi', 'pt', 'fr', 'de', 'es', 'it'];
  const alternates = {
    canonical: pageUrl,
    languages: {} as Record<string, string>
  };

  locales.forEach(lang => {
    alternates.languages[lang] = lang === 'en'
      ? `${baseUrl}${coloringPage.seo_slug}`
      : `${baseUrl}/${lang}${coloringPage.seo_slug}`;
  });

  return {
    title: coloringPage.meta_title || coloringPage.title,
    description: coloringPage.meta_description || coloringPage.description,
    keywords: coloringPage.tags?.join(', ') || '',
    authors: [{ name: 'MakeColoring.com' }],
    robots: 'index, follow',
    alternates,
    openGraph: {
      type: 'website',
      title: coloringPage.meta_title || coloringPage.title,
      description: coloringPage.meta_description || coloringPage.description,
      images: [
        {
          url: coloringPage.image_url,
          width: 400,
          height: 400,
          alt: coloringPage.alt_text || coloringPage.title,
        }
      ],
      url: pageUrl,
      siteName: 'MakeColoring.com',
      locale: locale.replace('-', '_'),
    },
    twitter: {
      card: 'summary_large_image',
      title: coloringPage.meta_title || coloringPage.title,
      description: coloringPage.meta_description || coloringPage.description,
      images: [coloringPage.image_url],
    },
    other: {
      'theme-color': '#3b82f6',
      'format-detection': 'telephone=no',
      'apple-mobile-web-app-capable': 'yes',
      'apple-mobile-web-app-status-bar-style': 'default',
    }
  };
}
