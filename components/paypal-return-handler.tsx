'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';

interface PaymentResult {
  success: boolean;
  message: string;
  order_no?: string;
  credits?: number;
}

/**
 * PayPal返回处理组件
 * 处理PayPal支付完成后直接返回到订单页面的情况
 * 根据PayPal官方文档，token和PayerID会自动添加到return_url
 */
export default function PayPalReturnHandler() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const t = useTranslations();
  const [isProcessing, setIsProcessing] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  useEffect(() => {
    const processPayPalReturn = async () => {
      // 检查是否是PayPal返回
      const paypalReturn = searchParams.get('paypal_return');
      const paypalOrderId = searchParams.get('token'); // PayPal自动添加的token参数
      const payerId = searchParams.get('PayerID'); // PayPal自动添加的PayerID参数
      const paymentType = searchParams.get('payment_type'); // 支付类型标识

      if (!paypalReturn || !paypalOrderId || !payerId || isProcessing) {
        return;
      }

      setIsProcessing(true);

      try {
        // 检查是否是订阅支付
        if (paymentType === 'subscription') {

          // 对于订阅支付，我们不调用payment-process API
          // 而是等待webhook处理，然后清理URL参数
          const newUrl = new URL(window.location.href);
          newUrl.searchParams.delete('paypal_return');
          newUrl.searchParams.delete('token');
          newUrl.searchParams.delete('PayerID');
          newUrl.searchParams.delete('payment_type');
          newUrl.searchParams.delete('order_no');
          newUrl.searchParams.set('success', 'subscription_processing');

          // 使用replace避免用户后退到处理页面
          window.history.replaceState({}, '', newUrl.toString());
          router.refresh(); // 刷新页面显示最新订单
          return;
        }

        // 一次性支付处理
        const response = await fetch('/api/paypal-payment-process', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            paypal_order_id: paypalOrderId,
            payer_id: payerId,
          }),
        });

        const apiResponse = await response.json();

        // 解析API响应格式 {code: 0, message: "ok", data: {...}}
        const result: PaymentResult = apiResponse.code === 0 ? apiResponse.data : { success: false, message: apiResponse.message };

        if (result.success) {
          // 支付成功，清理所有PayPal相关参数，只保留成功状态
          const newUrl = new URL(window.location.href);
          newUrl.searchParams.delete('paypal_return');
          newUrl.searchParams.delete('token');
          newUrl.searchParams.delete('PayerID');
          newUrl.searchParams.set('success', 'payment_completed');
          // 不需要在URL中传递订单号，订单列表会自动刷新显示最新数据

          // 使用replace避免用户后退到处理页面
          window.history.replaceState({}, '', newUrl.toString());
          router.refresh(); // 刷新页面显示最新订单
        } else {
          // 支付失败，显示错误消息

          // 如果是服务器错误且重试次数少于3次，则重试
          if (retryCount < 3 && (
            result.message?.includes('服务') ||
            result.message?.includes('网络') ||
            result.message?.includes('超时')
          )) {
            setRetryCount(prev => prev + 1);
            setTimeout(() => {
              setIsProcessing(false);
              processPayPalReturn();
            }, 2000);
            return;
          }

          // 重试失败或其他错误，跳转到错误页面
          const newUrl = new URL(window.location.href);
          newUrl.searchParams.delete('paypal_return');
          newUrl.searchParams.delete('token');
          newUrl.searchParams.delete('PayerID');
          newUrl.searchParams.set('error', 'payment_processing_failed');
          window.history.replaceState({}, '', newUrl.toString());
          router.refresh();
        }
      } catch (error) {
        // 网络错误重试
        if (retryCount < 3) {
          setRetryCount(prev => prev + 1);
          setTimeout(() => {
            setIsProcessing(false);
            processPayPalReturn();
          }, 2000);
          return;
        }

        // 重试失败，显示错误
        const newUrl = new URL(window.location.href);
        newUrl.searchParams.delete('paypal_return');
        newUrl.searchParams.delete('token');
        newUrl.searchParams.delete('PayerID');
        newUrl.searchParams.set('error', 'payment_processing_failed');
        window.history.replaceState({}, '', newUrl.toString());
        router.refresh();
      } finally {
        setIsProcessing(false);
      }
    };

    processPayPalReturn();
  }, [searchParams, router, isProcessing, retryCount]);

  // 如果正在处理PayPal返回，显示处理提示
  if (isProcessing) {
    return (
      <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-center">
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3"></div>
          <div>
            <p className="text-sm text-blue-800 font-medium">
              {t('paypal_success.processing_payment_result')}
            </p>
            {retryCount > 0 && (
              <p className="text-xs text-blue-600 mt-1">
                {t('paypal_success.retrying_count', { current: retryCount, total: 3 })}
              </p>
            )}
          </div>
        </div>
      </div>
    );
  }

  return null;
}
