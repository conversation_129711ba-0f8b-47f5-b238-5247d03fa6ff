'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { X } from 'lucide-react';

interface PaymentMessageProps {
  autoHideDelay?: number; // 自动隐藏延迟时间（毫秒），默认5秒
}

/**
 * 支付消息提示组件
 * 支持国际化、手动删除和自动删除功能
 */
export default function PaymentMessage({ autoHideDelay = 5000 }: PaymentMessageProps) {
  const searchParams = useSearchParams();
  const t = useTranslations();
  
  const [isVisible, setIsVisible] = useState(true);
  const [countdown, setCountdown] = useState(Math.ceil(autoHideDelay / 1000));
  
  const error = searchParams.get('error');
  const success = searchParams.get('success');
  const cancelled = searchParams.get('cancelled');

  // 手动关闭消息
  const handleClose = () => {
    setIsVisible(false);
    // 清理URL参数
    const newUrl = new URL(window.location.href);
    newUrl.searchParams.delete('error');
    newUrl.searchParams.delete('success');
    newUrl.searchParams.delete('cancelled');
    // 不再需要清理order_no参数，因为我们不再使用它
    window.history.replaceState({}, '', newUrl.toString());
  };

  // 自动隐藏逻辑
  useEffect(() => {
    if (!error && !success && !cancelled) return;
    
    let countdownTimer: NodeJS.Timeout;
    let hideTimer: NodeJS.Timeout;

    // 成功消息自动隐藏
    if (success === 'payment_completed') {
      countdownTimer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(countdownTimer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      hideTimer = setTimeout(() => {
        handleClose();
      }, autoHideDelay);
    }

    return () => {
      if (countdownTimer) clearInterval(countdownTimer);
      if (hideTimer) clearTimeout(hideTimer);
    };
  }, [error, success, cancelled, autoHideDelay]);

  // 如果没有消息或已隐藏，不显示
  if (!isVisible || (!error && !success && !cancelled)) {
    return null;
  }

  // 错误消息
  if (error === 'payment_processing_failed') {
    return (
      <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg relative">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3 flex-1">
            <p className="text-sm text-red-800">
              {t('my_orders.payment_processing_failed')}
            </p>
          </div>
          <button
            onClick={handleClose}
            className="ml-3 flex-shrink-0 text-red-400 hover:text-red-600 transition-colors"
            aria-label={t('common.close')}
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </div>
    );
  }

  // 成功消息
  if (success === 'payment_completed') {
    return (
      <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg relative">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3 flex-1">
            <p className="text-sm text-green-800">
              {t('my_orders.payment_success')}
            </p>
            {countdown > 0 && (
              <p className="text-xs text-green-600 mt-1">
                {t('my_orders.auto_hide_countdown', { seconds: countdown })}
              </p>
            )}
          </div>
          <button
            onClick={handleClose}
            className="ml-3 flex-shrink-0 text-green-400 hover:text-green-600 transition-colors"
            aria-label={t('common.close')}
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </div>
    );
  }

  // 订阅处理中消息
  if (success === 'subscription_processing') {
    return (
      <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg relative">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-400 animate-spin" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3 flex-1">
            <p className="text-sm text-blue-800">
              {t('my_orders.subscription_processing')}
            </p>
            <p className="text-xs text-blue-600 mt-1">
              {t('my_orders.subscription_processing_note')}
            </p>
          </div>
          <button
            onClick={handleClose}
            className="ml-3 flex-shrink-0 text-blue-400 hover:text-blue-600 transition-colors"
            aria-label={t('common.close')}
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </div>
    );
  }

  // 取消支付消息
  if (cancelled === 'payment') {
    return (
      <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg relative">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3 flex-1">
            <p className="text-sm text-yellow-800">
              {t('my_orders.payment_cancelled')}
            </p>
          </div>
          <button
            onClick={handleClose}
            className="ml-3 flex-shrink-0 text-yellow-400 hover:text-yellow-600 transition-colors"
            aria-label={t('common.close')}
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </div>
    );
  }

  return null;
}
