"use client";

import { Button } from "@/components/ui/button";
import { Button as ButtonType } from "@/types/blocks/base";
import Icon from "@/components/icon";
import Link from "next/link";

interface HeroButtonProps {
  button: ButtonType;
  className?: string;
}

export default function HeroButton({ button, className }: HeroButtonProps) {
  const handleClick = (e: React.MouseEvent) => {
    // 如果 URL 以 # 开头，说明是页面内锚点
    if (button.url?.startsWith('#')) {
      e.preventDefault();
      const targetId = button.url.substring(1);
      const targetElement = document.getElementById(targetId);
      
      if (targetElement) {
        targetElement.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
      return;
    }
  };

  // 转换尺寸，确保兼容 UI Button 组件
  const getButtonSize = (size?: string) => {
    if (size === "md") return "default";
    if (size === "sm" || size === "lg" || size === "icon") return size;
    return "lg"; // 默认使用 lg
  };

  // 如果是锚点链接，使用 button 元素
  if (button.url?.startsWith('#')) {
    return (
      <Button
        className={className}
        size={getButtonSize(button.size)}
        variant={button.variant || "default"}
        onClick={handleClick}
      >
        {button.title}
        {button.icon && (
          <Icon name={button.icon} className="ml-1" />
        )}
      </Button>
    );
  }

  // 其他情况使用 Link
  return (
    <Link
      href={button.url || ""}
      target={button.target || ""}
      className="flex items-center"
    >
      <Button
        className={className}
        size={getButtonSize(button.size)}
        variant={button.variant || "default"}
      >
        {button.title}
        {button.icon && (
          <Icon name={button.icon} className="ml-1" />
        )}
      </Button>
    </Link>
  );
}
