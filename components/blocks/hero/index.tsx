import { Badge } from "@/components/ui/badge";
import HappyUsers from "./happy-users";
import HeroImages from "./hero-images";
import HeroButton from "./hero-button";
import { Hero as HeroType } from "@/types/blocks/hero";
import { cn } from "@/lib/utils";
import HeroBg from "./bg";

export default function Hero({ hero }: { hero: HeroType }) {
  if (hero.disabled) {
    return null;
  }

  const highlightText = hero.highlight_text;
  let texts = null;
  if (highlightText) {
    texts = hero.title?.split(highlightText, 2);
  }

  // 确定布局模式
  const layout = hero.layout || "center";
  const hasImages = hero.images?.length || hero.image;

  // 确定图片展示模式
  let displayMode = hero.image_display_mode;
  if (!displayMode && hero.images) {
    if (hero.images.length === 1) displayMode = "single";
    else if (hero.images.length === 2) displayMode = "before-after";
    else if (hero.images.length >= 4) displayMode = "grid";
    else displayMode = "single";
  }

  // 准备图片数据
  const images = hero.images || (hero.image ? [hero.image] : []);

  return (
    <>
      {/* <HeroBg /> */}
      <section className={cn(
        "py-8 sm:py-12 lg:py-12 min-h-[70vh] sm:min-h-[65vh] lg:min-h-[60vh] flex items-center",
        layout === "split" ? "" : "text-center"
      )}>
        <div className="container">
          {hero.show_badge && (
            <div className="flex items-center justify-center mb-8">
              <img
                src="/imgs/badges/phdaily.svg"
                alt="phdaily"
                className="h-10 object-cover"
              />
            </div>
          )}

          {layout === "split" && hasImages ? (
            // 左右分栏布局 - 文字区域占5，图片区域占4（稍微增大图片区域）
            <div className="grid grid-cols-1 lg:grid-cols-9 gap-8 sm:gap-10 lg:gap-16 items-center">
              {/* 左侧内容区域 - 占5列 */}
              <div className="lg:col-span-5 flex flex-col justify-center space-y-4 sm:space-y-6 text-left lg:text-left">
                {hero.announcement && (
                  <a
                    href={hero.announcement.url}
                    className="inline-flex items-center gap-3 rounded-full border px-2 py-1 text-sm"
                  >
                    {hero.announcement.label && (
                      <Badge>{hero.announcement.label}</Badge>
                    )}
                    {hero.announcement.title}
                  </a>
                )}

                {texts && texts.length > 1 ? (
                  <h1 className="text-4xl sm:text-5xl font-bold lg:text-6xl xl:text-6xl leading-tight overflow-hidden"
                      style={{
                        display: '-webkit-box',
                        WebkitLineClamp: 3,
                        WebkitBoxOrient: 'vertical',
                        lineHeight: '1.2',
                        maxHeight: 'calc(1.2em * 3)'
                      }}>
                    {texts[0]}
                    <span className="bg-gradient-to-r from-primary via-rose-500 to-amber-500 bg-clip-text text-transparent">
                      {highlightText}
                    </span>
                    {texts[1]}
                  </h1>
                ) : (
                  <h1 className="text-4xl sm:text-5xl font-bold lg:text-6xl xl:text-6xl leading-tight overflow-hidden"
                      style={{
                        display: '-webkit-box',
                        WebkitLineClamp: 3,
                        WebkitBoxOrient: 'vertical',
                        lineHeight: '1.3',
                        maxHeight: 'calc(1.3em * 3)'
                      }}>
                    {hero.title}
                  </h1>
                )}

                <p
                  className="text-lg lg:text-xl text-muted-foreground max-w-2xl leading-relaxed"
                  dangerouslySetInnerHTML={{ __html: hero.description || "" }}
                />

                {/* Tags Section */}
                {hero.tag && hero.tag.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {hero.tag.map((tag, index) => {
                      const tagText = typeof tag === 'string' ? tag : tag.text;
                      const tagColor = typeof tag === 'string' ? undefined : tag.color;
                      const tagClassName = typeof tag === 'string' ? undefined : tag.className;

                      return (
                        <Badge
                          key={index}
                          variant="outline"
                          className={cn(
                            "px-3 py-1.5 text-xs font-medium rounded-full bg-transparent hover:bg-opacity-5 transition-colors",
                            tagClassName
                          )}
                          style={tagColor ? {
                            color: tagColor,
                            borderColor: tagColor,
                            backgroundColor: 'transparent'
                          } : undefined}
                        >
                          {tagText}
                        </Badge>
                      );
                    })}
                  </div>
                )}

                {hero.buttons && (
                  <div className="flex flex-col sm:flex-row gap-4">
                    {hero.buttons.map((item, i) => (
                      <HeroButton
                        key={i}
                        button={item}
                        className="w-full sm:w-auto"
                      />
                    ))}
                  </div>
                )}

                {hero.tip && (
                  <p className="text-sm text-muted-foreground">{hero.tip}</p>
                )}

                {hero.show_happy_users && <HappyUsers />}
              </div>

              {/* 右侧图片区域 - 占4列（稍微增大） */}
              <div className="lg:col-span-4 order-first lg:order-last flex items-center justify-center min-h-[400px] lg:min-h-[450px]">
                <HeroImages
                  images={images}
                  displayMode={displayMode}
                  className="w-full"
                />
              </div>
            </div>
          ) : (
            // 居中布局（原有布局）
            <div className="text-center max-w-5xl mx-auto">
              {hero.announcement && (
                <a
                  href={hero.announcement.url}
                  className="mx-auto mb-3 inline-flex items-center gap-3 rounded-full border px-2 py-1 text-sm"
                >
                  {hero.announcement.label && (
                    <Badge>{hero.announcement.label}</Badge>
                  )}
                  {hero.announcement.title}
                </a>
              )}

              {texts && texts.length > 1 ? (
                <h1 className="mx-auto mb-4 mt-6 max-w-4xl text-balance text-4xl sm:text-5xl font-bold lg:mb-8 lg:text-7xl overflow-hidden"
                    style={{
                      display: '-webkit-box',
                      WebkitLineClamp: 3,
                      WebkitBoxOrient: 'vertical',
                      lineHeight: '1.3',
                      maxHeight: 'calc(1.3em * 3)'
                    }}>
                  {texts[0]}
                  <span className="bg-gradient-to-r from-primary via-rose-500 to-amber-500 bg-clip-text text-transparent">
                    {highlightText}
                  </span>
                  {texts[1]}
                </h1>
              ) : (
                <h1 className="mx-auto mb-4 mt-6 max-w-4xl text-balance text-4xl sm:text-5xl font-bold lg:mb-8 lg:text-7xl overflow-hidden"
                    style={{
                      display: '-webkit-box',
                      WebkitLineClamp: 3,
                      WebkitBoxOrient: 'vertical',
                      lineHeight: '1.3',
                      maxHeight: 'calc(1.3em * 3)'
                    }}>
                  {hero.title}
                </h1>
              )}
              <p
                className="mx-auto max-w-4xl text-lg text-muted-foreground lg:text-xl"
                dangerouslySetInnerHTML={{ __html: hero.description || "" }}
              />

              {/* Tags Section */}
              {hero.tag && hero.tag.length > 0 && (
                <div className="mt-6 flex flex-wrap justify-center gap-2 sm:gap-3">
                  {hero.tag.map((tag, index) => {
                    const tagText = typeof tag === 'string' ? tag : tag.text;
                    const tagColor = typeof tag === 'string' ? undefined : tag.color;
                    const tagClassName = typeof tag === 'string' ? undefined : tag.className;

                    return (
                      <Badge
                        key={index}
                        variant="outline"
                        className={cn(
                          "px-4 py-2 text-xs font-medium rounded-full bg-transparent hover:bg-opacity-5 transition-colors",
                          tagClassName
                        )}
                        style={tagColor ? {
                          color: tagColor,
                          borderColor: tagColor,
                          backgroundColor: 'transparent'
                        } : undefined}
                      >
                        {tagText}
                      </Badge>
                    );
                  })}
                </div>
              )}

              {hero.buttons && (
                <div className="mt-10 flex flex-col justify-center gap-4 sm:flex-row">
                  {hero.buttons.map((item, i) => (
                    <HeroButton
                      key={i}
                      button={item}
                      className="w-full sm:w-auto"
                    />
                  ))}
                </div>
              )}

              {hero.tip && (
                <p className="mt-8 text-md text-muted-foreground">{hero.tip}</p>
              )}

              {hero.show_happy_users && <HappyUsers />}

              {/* 居中布局时的图片展示 */}
              {hasImages && (
                <div className="mt-16">
                  <HeroImages
                    images={images}
                    displayMode={displayMode}
                    className="max-w-5xl mx-auto"
                  />
                </div>
              )}
            </div>
          )}
        </div>
      </section>
    </>
  );
}
