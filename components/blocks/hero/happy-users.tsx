import { Avatar, AvatarImage } from "@/components/ui/avatar";
import { useTranslations } from "next-intl";
import { Star } from "lucide-react";

export default function HappyUsers() {
  const t = useTranslations('hero');
  return (
    <div className="mt-4 flex w-fit flex-col items-start gap-1.5 sm:flex-row sm:items-center sm:gap-3">
      <span className="inline-flex items-center -space-x-1">
        {Array.from({ length: 4 }).map((_, index) => (
          <Avatar className="size-7 border-2 border-white shadow-sm" key={index}>
            <AvatarImage
              src={`/imgs/users/${index + 6}.webp`}
              alt="placeholder"
            />
          </Avatar>
        ))}
      </span>
      <div className="flex flex-col items-start gap-0.5">
        <div className="flex items-center gap-0.5">
          {Array.from({ length: 5 }).map((_, index) => (
            <Star
              key={index}
              className="size-3 fill-yellow-400 text-yellow-400"
            />
          ))}
        </div>
        <p className="text-xs font-medium text-muted-foreground">
          {t('happy_users_text')}
        </p>
      </div>
    </div>
  );
}
