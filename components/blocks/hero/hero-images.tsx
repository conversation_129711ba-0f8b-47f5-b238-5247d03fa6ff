"use client";


import { HeroImage, ImageDisplayMode } from "@/types/blocks/hero";
import { cn } from "@/lib/utils";
import BeforeAfterSlider from "@/components/ui/before-after-slider";

interface HeroImagesProps {
  images: HeroImage[];
  displayMode?: ImageDisplayMode;
  className?: string;
}

export default function HeroImages({
  images,
  displayMode = "single",
  className
}: HeroImagesProps) {

  if (!images || images.length === 0) {
    return null;
  }

  // 单图展示
  if (displayMode === "single" || images.length === 1) {
    const image = images[0];
    return (
      <div className={cn("relative w-full flex items-center justify-center", className)}>
        <div className="relative w-full aspect-[4/3]">
          <img
            src={image.src}
            alt={image.alt || "Hero image"}
            className="w-full h-full object-cover rounded-lg shadow-lg"
          />
          {image.title && (
            <div className="absolute bottom-4 left-4 bg-black/70 text-white px-3 py-1 rounded">
              {image.title}
            </div>
          )}
        </div>
      </div>
    );
  }

  // Before-After Slider (双图)
  if (displayMode === "before-after" && images.length >= 2) {
    const beforeImage = images[0];
    const afterImage = images[1];

    // 确保图片有有效的src
    if (!beforeImage.src || !afterImage.src) {
      return null;
    }

    return (
      <div className={cn("w-full flex items-center justify-center", className)}>
        <BeforeAfterSlider
          beforeImage={{
            src: beforeImage.src,
            alt: beforeImage.alt || "Before",
            title: beforeImage.title
          }}
          afterImage={{
            src: afterImage.src,
            alt: afterImage.alt || "After",
            title: afterImage.title
          }}
          className="shadow-lg w-full"
          aspectRatio="4/3"
          showLabels={true}
          initialPosition={50}
        />
      </div>
    );
  }

  // 四宫格展示
  if (displayMode === "grid" && images.length >= 4) {
    return (
      <div className={cn("w-full flex items-center justify-center", className)}>
        <div className="grid grid-cols-2 gap-4 w-full aspect-square">
          {images.slice(0, 4).map((image, index) => (
            <div key={index} className="relative aspect-square">
              <img
                src={image.src}
                alt={image.alt || `Grid image ${index + 1}`}
                className="w-full h-full object-cover rounded-lg shadow-md"
              />
              {image.title && (
                <div className="absolute bottom-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs">
                  {image.title}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  }

  // 默认网格布局（适应任意数量的图片）
  return (
    <div className={cn(
      "grid gap-4",
      images.length === 2 ? "grid-cols-1 sm:grid-cols-2" : 
      images.length === 3 ? "grid-cols-1 sm:grid-cols-3" :
      "grid-cols-2",
      className
    )}>
      {images.map((image, index) => (
        <div key={index} className="relative">
          <img
            src={image.src}
            alt={image.alt || `Image ${index + 1}`}
            className="w-full h-auto rounded-lg shadow-md"
          />
          {image.title && (
            <div className="absolute bottom-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs">
              {image.title}
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
