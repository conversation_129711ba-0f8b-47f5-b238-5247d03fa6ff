# Hero 组件使用指南

## 概述

Hero 组件已经升级，现在支持多种布局模式和图片展示方式，完美适配你的 Make Coloring 产品需求。

## 新增功能

### 1. 布局模式 (Layout)

- **`center`** (默认): 居中布局，适合传统的 Hero 设计
- **`split`**: 左右分栏布局，左侧内容，右侧图片

### 2. 图片展示模式 (Image Display Mode)

- **`single`**: 单图展示
- **`before-after`**: Before-After 滑动对比（适合展示转换效果）
- **`grid`**: 四宫格展示（展示多种样式）

### 3. 智能按钮

- 支持页面内锚点跳转（URL 以 `#` 开头）
- 支持外部链接跳转
- 平滑滚动到指定区域

## 配置示例

### 左右分栏 + 单图展示

```json
{
  "layout": "split",
  "title": "AI-powered Coloring Pages Generator",
  "highlight_text": "Coloring Pages",
  "description": "Turn any photo or idea into printable coloring pages in seconds",
  "buttons": [
    {
      "title": "Try for Free",
      "url": "#image-generator",
      "variant": "default"
    }
  ],
  "images": [
    {
      "src": "/imgs/hero/example.jpg",
      "alt": "Coloring Page Example",
      "title": "AI Generated"
    }
  ],
  "image_display_mode": "single",
  "show_happy_users": false
}
```

### 左右分栏 + Before-After 滑块

```json
{
  "layout": "split",
  "title": "Transform Photos into Beautiful Coloring Pages",
  "images": [
    {
      "src": "/imgs/hero/original.jpg",
      "alt": "Original Photo",
      "title": "Original Photo"
    },
    {
      "src": "/imgs/hero/coloring.jpg",
      "alt": "Coloring Page",
      "title": "AI Coloring Page"
    }
  ],
  "image_display_mode": "before-after"
}
```

### 左右分栏 + 四宫格展示

```json
{
  "layout": "split",
  "title": "Endless Coloring Possibilities",
  "images": [
    {
      "src": "/imgs/hero/animals.jpg",
      "title": "Animals"
    },
    {
      "src": "/imgs/hero/nature.jpg",
      "title": "Nature"
    },
    {
      "src": "/imgs/hero/fantasy.jpg",
      "title": "Fantasy"
    },
    {
      "src": "/imgs/hero/mandala.jpg",
      "title": "Mandala"
    }
  ],
  "image_display_mode": "grid"
}
```

## 类型定义

```typescript
export interface Hero {
  layout?: "center" | "split";
  image_display_mode?: "single" | "before-after" | "grid";
  images?: HeroImage[];
  // ... 其他现有属性
}

export interface HeroImage extends Image {
  title?: string;
  description?: string;
}
```

## 向后兼容性

- 现有的 `image` 属性仍然支持
- 未指定 `layout` 时默认使用 `center` 布局
- 未指定 `image_display_mode` 时会根据图片数量自动选择

## 高度优化

- 新增 `min-h-[80vh]` 确保 Hero 区域有足够高度
- 使用 `flex items-center` 实现垂直居中
- 响应式设计，移动端自动调整布局

## 按钮功能增强

- 支持锚点跳转：`"url": "#section-id"`
- 支持平滑滚动到页面指定区域
- 保持原有的外部链接功能

## 使用建议

1. **产品展示页面**：使用 `split` 布局 + `single` 图片模式
2. **功能演示页面**：使用 `split` 布局 + `before-after` 模式
3. **作品展示页面**：使用 `split` 布局 + `grid` 模式
4. **传统落地页**：使用 `center` 布局

## 注意事项

- Before-After 滑块需要至少 2 张图片
- 四宫格模式建议使用 4 张图片
- 图片建议使用相同的宽高比以获得最佳效果
- 移动端会自动调整为单列布局
