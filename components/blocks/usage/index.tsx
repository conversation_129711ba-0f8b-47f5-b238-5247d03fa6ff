"use client";

import { useState, useEffect } from "react";
import { Section as SectionType } from "@/types/blocks/section";
import { Badge } from "@/components/ui/badge";

export default function Usage({ section }: { section: SectionType }) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);

  useEffect(() => {
    if (!section.items || section.items.length === 0) return;

    const interval = setInterval(() => {
      setIsTransitioning(true);
      
      setTimeout(() => {
        setCurrentImageIndex((prev) => 
          prev === section.items!.length - 1 ? 0 : prev + 1
        );
        setIsTransitioning(false);
      }, 300);
    }, 3000);

    return () => clearInterval(interval);
  }, [section.items]);

  if (section.disabled) {
    return null;
  }

  const currentItem = section.items?.[currentImageIndex];

  return (
    <section id={section.name} className="py-12 bg-muted/30">
      <div className="container">
        {/* 标题和描述 */}
        <div className="mx-auto mb-16 max-w-4xl text-center">
          {section.label && (
            <Badge className="text-xs font-medium mb-4">{section.label}</Badge>
          )}
          {section.title && (
            <h2 className="mb-4 text-3xl font-bold tracking-tight lg:text-4xl">
              {section.title}
            </h2>
          )}
          {section.description && (
            <p className="text-lg text-muted-foreground">
              {section.description}
            </p>
          )}
        </div>

        {/* 主要内容区域 */}
        <div className="grid items-center gap-12 lg:grid-cols-2 lg:gap-16">
          {/* 图片区域 */}
          <div className="relative">
            <div className="relative mx-auto max-w-lg">
              {/* 图片容器 */}
              <div className="relative overflow-hidden rounded-2xl bg-background shadow-2xl">
                {currentItem?.image && (
                  <img
                    src={currentItem.image.src}
                    alt={currentItem.image.alt || currentItem.title}
                    className={`w-full h-auto object-cover transition-all duration-300 ${
                      isTransitioning ? "opacity-50 scale-95" : "opacity-100 scale-100"
                    }`}
                  />
                )}
              </div>

              {/* 加载点指示器 */}
              <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
                <div className="flex items-center space-x-2">
                  {section.items?.map((_, index) => (
                    <div key={index} className="flex items-center">
                      <button
                        onClick={() => {
                          setIsTransitioning(true);
                          setTimeout(() => {
                            setCurrentImageIndex(index);
                            setIsTransitioning(false);
                          }, 300);
                        }}
                        className={`w-3 h-3 rounded-full transition-all duration-300 ${
                          index === currentImageIndex
                            ? "bg-primary scale-125"
                            : "bg-muted-foreground/30 hover:bg-muted-foreground/50"
                        }`}
                      />
                      {index < section.items!.length - 1 && (
                        <div className="w-8 h-0.5 mx-2 bg-muted-foreground/20" />
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* 步骤列表 */}
          <div className="space-y-8">
            {section.items?.map((item, index) => (
              <div
                key={index}
                className={`flex items-start space-x-4 transition-all duration-300 ${
                  index === currentImageIndex
                    ? "opacity-100 scale-100"
                    : "opacity-60 scale-95"
                }`}
              >
                {/* 步骤编号 */}
                <div
                  className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold transition-all duration-300 ${
                    index === currentImageIndex
                      ? "bg-primary text-primary-foreground shadow-lg"
                      : "bg-muted text-muted-foreground"
                  }`}
                >
                  {index + 1}
                </div>

                {/* 步骤内容 */}
                <div className="flex-1 min-w-0">
                  <h3
                    className={`text-xl font-semibold mb-2 transition-colors duration-300 ${
                      index === currentImageIndex
                        ? "text-foreground"
                        : "text-muted-foreground"
                    }`}
                  >
                    {item.title}
                  </h3>
                  <p
                    className={`text-base leading-relaxed transition-colors duration-300 ${
                      index === currentImageIndex
                        ? "text-muted-foreground"
                        : "text-muted-foreground/70"
                    }`}
                  >
                    {item.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
