"use client"

import { useState, useEffect } from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { X, Download, Palette, Wand2, Settings, Crown, Check, Info } from "lucide-react"
import Image from "next/image"
import { toast } from "sonner"
import { useAppContext } from "@/contexts/app"
import { Photo } from "@/types/photo"
import { useTranslations } from "next-intl"

import { useUserCredits } from "@/hooks/use-user-credits"
import SubscriptionModal, { SubscriptionModalType } from "@/components/ui/subscription-modal"
import { useProviderConfig } from "@/hooks/use-provider-config"

import { CreditsAmount } from "@/services/credit"
import { generateDownloadFilename, generatePDFFilename, extractUuidFromUrl } from "@/lib/file-naming"


interface StyleOption {
  value: string
  label: string
  description?: string
  image?: string
}



interface ExamplePrompt {
  prompt: string
  image: string
}

interface TextToColoringProps {
  className?: string
  description?: string
  styleOptions?: StyleOption[]
}

// 验证是否为有效的R2存储URL
const isValidR2Url = (url: string): boolean => {
  if (!url || !url.startsWith('http')) return false;

  // 检查是否包含已知的R2域名模式
  const r2Patterns = [
    'bucket.makecoloring.com',
    'r2.dev',
    'cloudflarestorage.com',
    'r2.cloudflarestorage.com'
  ];

  return r2Patterns.some(pattern => url.includes(pattern));
};

export default function TextToColoring({
  className = "",
  description,
  styleOptions: customStyleOptions
}: TextToColoringProps) {
  const t = useTranslations('text_to_coloring')
  const [textDescription, setTextDescription] = useState<string>("")
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [generatedImages, setGeneratedImages] = useState<Photo[]>([])
  const [selectedImage, setSelectedImage] = useState<Photo | null>(null)
  const [processingStep, setProcessingStep] = useState<string>("")
  const [progress, setProgress] = useState<number>(0)
  const [selectedStyle, setSelectedStyle] = useState<string>("default")
  const [selectedSize, setSelectedSize] = useState<string>("2:3")
  const [privateMode, setPrivateMode] = useState<boolean>(false)
  const [removeWatermark, setRemoveWatermark] = useState<boolean>(false)
  const [highQualityPrinting, setHighQualityPrinting] = useState<boolean>(false)
  const [enhance, setEnhance] = useState<boolean>(false)
  const [showExampleResult, setShowExampleResult] = useState<boolean>(false)
  const [selectedExampleImage, setSelectedExampleImage] = useState<string | null>(null)

  // 异步任务状态
  const [isAsyncTask, setIsAsyncTask] = useState<boolean>(false)
  const [currentAsyncTaskUuid, setCurrentAsyncTaskUuid] = useState<string | null>(null)
  const [hasShownSuccessToast, setHasShownSuccessToast] = useState<boolean>(false)
  const [hasShownErrorToast, setHasShownErrorToast] = useState<boolean>(false)

  // 订阅弹窗相关状态
  const [showSubscriptionModal, setShowSubscriptionModal] = useState<boolean>(false)
  const [subscriptionModalType, setSubscriptionModalType] = useState<SubscriptionModalType>('insufficient-credits')
  const [subscriptionFeatureName, setSubscriptionFeatureName] = useState<string>('')

  const { user, setShowSignModal } = useAppContext()

  const { credits, refetch: refetchCredits } = useUserCredits()
  const { config: providerConfig, loading: providerConfigLoading } = useProviderConfig()

  // 轻量级轮询检查异步任务状态
  const pollAsyncTaskStatus = async (taskUuid: string) => {
    try {
      const response = await fetch(`/api/async-task-status?taskUuid=${taskUuid}`);
      if (!response.ok) {
        console.error('Failed to poll async task status:', response.status);
        return null;
      }

      const data = await response.json();
      if (data.code === 0 && data.data) {
        return data.data;
      }
      return null;
    } catch (error) {
      console.error('Error polling async task status:', error);
      return null;
    }
  };

  // 启动异步任务轮询（30秒后开始）
  const startAsyncTaskPolling = (taskUuid: string) => {
    console.log('🔄 Will start polling in 30 seconds for:', taskUuid);

    // 30秒后开始轮询
    setTimeout(() => {
      console.log('🔄 Starting async task polling for:', taskUuid);

      let pollInterval: NodeJS.Timeout;
      let timeoutId: NodeJS.Timeout;

      pollInterval = setInterval(async () => {
        const taskStatus = await pollAsyncTaskStatus(taskUuid);

        if (taskStatus) {
          if (taskStatus.status === 'success' && taskStatus.result_images?.length > 0) {
            console.log('✅ Async task completed successfully:', taskUuid, `Duration: ${taskStatus.duration_seconds || 'unknown'}s`);

            // 验证所有图片URL是否有效（确保R2上传完成）
            const validImages = taskStatus.result_images.filter((img: any) =>
              img.url && isValidR2Url(img.url)
            );

            if (validImages.length === 0) {
              console.log('⚠️ No valid R2 images found, continuing polling...');
              return; // 继续轮询，等待R2上传完成
            }

            // 转换结果格式为Photo[]
            const photos = validImages.map((img: any, index: number) => ({
              uuid: `async-${taskUuid}-${index}`,
              img_url: img.url,
              img_description: taskStatus.prompt || 'Generated by Kie.ai',
              user_uuid: taskStatus.user_uuid,
              created_at: taskStatus.completed_at || new Date().toISOString(),
              status: 'created'
            }));

            // 清除轮询和超时
            clearInterval(pollInterval);
            clearTimeout(timeoutId);

            // 更新UI状态
            setProgress(100);
            setGeneratedImages(photos);
            setIsLoading(false);
            setIsAsyncTask(false);
            setCurrentAsyncTaskUuid(null);
            setProcessingStep("");

            // 显示成功消息（防止重复弹窗）
            if (!hasShownSuccessToast) {
              toast.success(t('success_message', { count: photos.length }));
              setHasShownSuccessToast(true);
            }

            // 刷新积分
            refetchCredits();

            // 重置进度条
            setTimeout(() => setProgress(0), 1000);
          } else if (taskStatus.status === 'failed') {
            console.log('❌ Async task failed:', taskUuid, taskStatus.error_message);

            // 清除轮询和超时
            clearInterval(pollInterval);
            clearTimeout(timeoutId);

            // 更新UI状态
            setIsLoading(false);
            setIsAsyncTask(false);
            setCurrentAsyncTaskUuid(null);
            setProcessingStep("");
            setProgress(0);

            // 显示错误消息（防止重复弹窗）
            if (!hasShownErrorToast) {
              toast.error(taskStatus.error_message || t('error_message'));
              setHasShownErrorToast(true);
            }
          }
        }
      }, 3000); // 每3秒检查一次

      // 设置最大轮询时间（2分钟，从开始轮询算起）
      timeoutId = setTimeout(() => {
        clearInterval(pollInterval);
        if (currentAsyncTaskUuid === taskUuid) {
          setIsLoading(false);
          setIsAsyncTask(false);
          setCurrentAsyncTaskUuid(null);
          setProcessingStep("");
          setProgress(0);
          if (!hasShownErrorToast) {
            toast.error('Generation timeout. Please try again.');
            setHasShownErrorToast(true);
          }
        }
      }, 120000);
    }, 30000); // 30秒后开始轮询
  };

  // 临时开关：禁用会员功能检查
  const DISABLE_PREMIUM_CHECK = false

  // 检查用户是否为会员
  const isPremiumUser = DISABLE_PREMIUM_CHECK || credits?.is_pro || false

  // 监听会员状态变化，自动关闭会员功能
  useEffect(() => {
    if (!isPremiumUser && !DISABLE_PREMIUM_CHECK) {
      // 如果用户不再是会员，自动关闭所有会员功能
      if (privateMode) {
        setPrivateMode(false)
        toast.info('Private mode has been disabled as your subscription may have expired.')
      }
      if (removeWatermark) {
        setRemoveWatermark(false)
        toast.info('Remove watermark has been disabled as your subscription may have expired.')
      }
      if (highQualityPrinting) {
        setHighQualityPrinting(false)
        toast.info('High quality printing has been disabled as your subscription may have expired.')
      }
    }
  }, [isPremiumUser, privateMode, removeWatermark, highQualityPrinting])

  // 实时检查用户会员状态（减少不必要的API调用）
  const checkPremiumStatus = async (): Promise<boolean> => {
    if (DISABLE_PREMIUM_CHECK) return true

    // 首先检查本地状态，如果已经是会员则直接返回
    if (isPremiumUser) {
      return true
    }

    try {
      // 只有在本地状态显示非会员时才调用API进行二次确认
      const response = await fetch('/api/user-credits')
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      if (data.code === 0 && data.data?.credits) {
        const isActuallyPremium = data.data.credits.is_pro || false

        // 如果API返回是会员但本地状态不是，刷新积分状态
        if (isActuallyPremium && !isPremiumUser) {
          refetchCredits()
        }

        return isActuallyPremium
      }

      return false
    } catch (error) {
      console.error('Failed to check premium status:', error)
      // 网络错误时，信任本地状态避免误判
      return isPremiumUser
    }
  }

  // 处理会员专享功能点击
  const handlePremiumFeatureToggle = async (featureName: string, currentValue: boolean, setValue: (value: boolean) => void, newValue: boolean) => {
    if (DISABLE_PREMIUM_CHECK) {
      setValue(newValue)
      return
    }

    // 如果用户尝试开启会员功能，检查会员状态
    if (newValue && !isPremiumUser) {
      // 进行二次确认（仅在本地状态显示非会员时）
      const isCurrentlyPremium = await checkPremiumStatus()

      if (!isCurrentlyPremium) {
        setSubscriptionModalType('premium-feature')
        setSubscriptionFeatureName(featureName)
        setShowSubscriptionModal(true)
        return // 不改变状态，保持原状态
      }
    }

    // 如果用户尝试关闭功能但已经不是会员，强制关闭
    if (!isPremiumUser && currentValue && !newValue) {
      setValue(false)
      toast.error(`${featureName} is a premium feature. Your subscription may have expired.`)
      return
    }

    // 会员用户或正常操作
    setValue(newValue)
  }





  // Use translations for title and description if not provided
  const displayDescription = description || t('description')

  // Default style options - can be overridden by props
  const defaultStyleOptions: StyleOption[] = [
    { value: "default", label: t('styles.default'), image: "/imgs/examples/text/default.webp" },
    { value: "sci-fi", label: t('styles.sci-fi'), image: "/imgs/examples/text/sci-fi.webp" },
    { value: "pixel", label: t('styles.pixel'), image: "/imgs/examples/text/pixel.webp" },
    { value: "chibi", label: t('styles.chibi'), image: "/imgs/examples/text/chibi.webp" },
    { value: "graffiti", label: t('styles.graffiti'), image: "/imgs/examples/text/graffiti.webp" },
    { value: "minimalist", label: t('styles.minimalist'), image: "/imgs/examples/text/minimalist.webp" },
    { value: "anime", label: t('styles.anime'), image: "/imgs/examples/text/anime.webp" },
    { value: "lego", label: t('styles.lego'), image: "/imgs/examples/text/lego.webp" },
    { value: "intricate", label: t('styles.intricate'), image: "/imgs/examples/text/intricate.webp" },
    { value: "kawaii", label: t('styles.kawaii'), image: "/imgs/examples/text/kawaii.webp" }
  ]



  // Size options with visual icons
  const sizeOptions = [
    {
      value: "2:3",
      label: t('size_options.default'),
      icon: "rectangle-vertical",
      tooltip: t('size_tooltips.default')
    },
    {
      value: "1:1",
      label: t('size_options.square'),
      icon: "square",
      tooltip: t('size_tooltips.square')
    },
    {
      value: "3:2",
      label: t('size_options.landscape'),
      icon: "rectangle-horizontal",
      tooltip: t('size_tooltips.landscape')
    }
  ]

  const styleOptions = customStyleOptions || defaultStyleOptions

  // Get example prompts from translations
  const examplePrompts = (t.raw('examples') as ExamplePrompt[]) || [];

  const handleExamplePromptClick = (example: ExamplePrompt) => {
    // Support toggle functionality - click again to deselect
    if (selectedExampleImage === example.image) {
      setSelectedExampleImage(null)
      setShowExampleResult(false)
    } else {
      setSelectedExampleImage(example.image)
      setShowExampleResult(true)
      setTextDescription(example.prompt)
    }
  }

  const handleStyleClick = (style: StyleOption) => {
    setSelectedStyle(style.value)
    // Style selection no longer affects the generate area
  }

  const requestGenerateApi = async () => {
    // 清除之前的结果，但保持loading状态（已在handleSubmit中设置）
    setGeneratedImages([]);
    setProgress(0); // 确保进度条从0开始
    setProcessingStep(t('processing_analyze'));
    setHasShownSuccessToast(false); // 重置成功提示状态
    setHasShownErrorToast(false); // 重置错误提示状态
    // Clear example display state when starting new generation
    setShowExampleResult(false);
    setSelectedExampleImage(null);

    // 根据provider配置动态设置进度条时长
    // 如果配置还在加载中，使用默认的apicore设置
    const isReplicateProvider = !providerConfigLoading && providerConfig?.activeProvider === 'replicate';

    // kie.ai 和 apicore 都使用60秒加载体验，只有replicate使用12秒
    const totalDuration = isReplicateProvider ? 12000 : 60000; // replicate: 12秒, apicore/kie: 60秒
    const updateInterval = isReplicateProvider ? 200 : 1000; // replicate: 200ms, apicore/kie: 1000ms
    const maxProgress = 95; // 最大进度95%，留5%给API完成
    let startTime = Date.now();
    let progressInterval: NodeJS.Timeout | null = null;

    const startProgressTimer = () => {
      // 重置进度条到0，确保每次都从0开始
      setProgress(0);
      startTime = Date.now(); // 重置开始时间

      progressInterval = setInterval(() => {
        const elapsed = Date.now() - startTime;
        const timeProgress = Math.min((elapsed / totalDuration) * maxProgress, maxProgress);

        setProgress(prev => {
          // 使用时间进度和随机波动的组合，让进度更平滑
          const randomVariation = (Math.random() - 0.5) * 3; // ±1.5%的随机波动
          const newProgress = Math.min(maxProgress, Math.max(prev, timeProgress + randomVariation)); // 确保进度只能递增
          return newProgress;
        });
      }, updateInterval);
    };

    startProgressTimer();

    try {
      const requestBody = {
        description: textDescription.trim(),
        mode: 'text-to-image',
        style: selectedStyle,
        size: selectedSize,
        difficulty: 'kids', // Default difficulty since it's removed from UI
        watermark: !removeWatermark,
        enhance: enhance,
        private: privateMode,
        high_quality: highQualityPrinting
      };

      console.log("📤 Sending request to /api/coloring-generate", {
        style: selectedStyle,
        size: selectedSize
      });
      setProcessingStep(t('processing_generate'));

      const response = await fetch("/api/coloring-generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody)
      });

      console.log("📥 Response status:", response.status, response.statusText);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log("📋 API response:", {
        code: data.code,
        message: data.message,
        photosCount: data.data?.photos?.length || 0,
        leftCredits: data.data?.left_credits
      });

      if (data.code === 0 && data.data) {
        // 检查是否是异步任务
        if (data.data.is_async && data.data.async_task) {
          console.log("🔄 Async task started:", data.data.async_task.uuid);
          setIsAsyncTask(true);
          setCurrentAsyncTaskUuid(data.data.async_task.uuid);

          // 启动轮询检查任务状态
          startAsyncTaskPolling(data.data.async_task.uuid);

          if (data.data.left_credits !== undefined) {
            console.log("💳 Credits remaining:", data.data.left_credits);
            refetchCredits();
          }
        }
        // 同步处理结果（支持所有provider）
        else if (data.data.photos) {
          console.log("✅ Generation successful:", data.data.photos.length, "images");

          // 验证所有图片URL是否有效（确保R2上传完成）
          const validPhotos = data.data.photos.filter((photo: any) =>
            photo.img_url && isValidR2Url(photo.img_url)
          );

          if (validPhotos.length === 0) {
            console.error("❌ No valid R2 images found in response");
            toast.error('Image upload failed, please try again');

            // 清除loading状态
            if (progressInterval) {
              clearInterval(progressInterval);
            }
            setIsLoading(false);
            setProcessingStep("");
            setProgress(0);
            return;
          }

          console.log("📸 Valid photos:", validPhotos.map((p: any) => ({
            uuid: p.uuid,
            img_url: p.img_url?.substring(0, 50) + '...',
            img_description: p.img_description
          })));

          setProgress(100);
          setGeneratedImages(validPhotos);
          console.log("🎯 State updated - generatedImages length:", validPhotos.length);

          // 显示成功消息（防止重复弹窗）
          if (!hasShownSuccessToast) {
            toast.success(t('success_message', { count: validPhotos.length }));
            setHasShownSuccessToast(true);
          }

          if (data.data.left_credits !== undefined) {
            console.log("💳 Credits remaining:", data.data.left_credits);
            refetchCredits();
          }

          // 同步任务完成，清除loading状态
          if (progressInterval) {
            clearInterval(progressInterval);
          }
          setIsLoading(false);
          setProcessingStep("");
          setProgress(0);
        } else {
          console.error("❌ Unexpected response format:", data);
          if (!hasShownErrorToast) {
            toast.error('Unexpected response format');
            setHasShownErrorToast(true);
          }
        }
      } else {
        console.error("❌ Generation failed:", data);
        if (!hasShownErrorToast) {
          toast.error(data.message || t('error_message'));
          setHasShownErrorToast(true);
        }

        // 失败时清除loading状态
        if (progressInterval) {
          clearInterval(progressInterval);
        }
        setIsLoading(false);
        setProcessingStep("");
        setProgress(0);
      }
    } catch (error) {
      console.error("❌ Request error:", error);

      // 显示错误消息（防止重复弹窗）
      if (!hasShownErrorToast) {
        if (error instanceof Error) {
          toast.error(`${t('error_message')}: ${error.message}`);
        } else {
          toast.error(t('error_message'));
        }
        setHasShownErrorToast(true);
      }

      // 错误时清除loading状态
      if (progressInterval) {
        clearInterval(progressInterval);
      }
      setIsLoading(false);
      setProcessingStep("");
      setProgress(0);
    } finally {
      console.log("🏁 Generation request completed");
    }
  }

  const handleSubmit = () => {
    if(!textDescription.trim()) {
      toast.error(t('error_empty'));
      return;
    }

    if(!user){
      setShowSignModal(true);
      return;
    }

    // 检查积分是否足够
    const requiredCredits = CreditsAmount.ColoringGenerateCost;
    const currentCredits = credits?.left_credits || 0;

    if (currentCredits < requiredCredits) {
      console.log("❌ Insufficient credits:", currentCredits, "required:", requiredCredits);
      if (DISABLE_PREMIUM_CHECK) {
        toast.error(`Insufficient credits. You need ${requiredCredits} credits but only have ${currentCredits}.`);
      } else {
        setSubscriptionModalType('insufficient-credits');
        setShowSubscriptionModal(true);
      }
      return;
    }

    // 立即设置loading状态，确保按钮立即显示"Generating..."
    setIsLoading(true);
    setProcessingStep('Generating...');

    // 使用setTimeout确保状态更新立即反映在UI上
    setTimeout(() => {
      requestGenerateApi();
    }, 0);
  }

  const handleDownload = async (imageUrl: string, imageId: string, format: 'png' | 'pdf' = 'png') => {
    try {
      // 从URL中提取UUID，如果失败则使用传入的imageId
      const extractedUuid = extractUuidFromUrl(imageUrl) || imageId;
      const domain = window.location.hostname.replace('www.', '');

      if (format === 'pdf') {
        // For PDF download, we need to convert the image to PDF
        console.log('🔄 Starting PDF conversion for:', imageUrl);

        try {
          // Import jsPDF dynamically first
          const jsPDF = (await import('jspdf')).jsPDF;

          // 使用fetch获取图片数据，避免CORS问题
          console.log('🔄 Fetching image data to avoid CORS issues...');
          const imageResponse = await fetch(imageUrl);
          if (!imageResponse.ok) {
            throw new Error(`Failed to fetch image: ${imageResponse.status} ${imageResponse.statusText}`);
          }
          const imageBlob = await imageResponse.blob();
          const imageObjectUrl = URL.createObjectURL(imageBlob);

          const loadImage = (): Promise<HTMLImageElement> => {
            return new Promise((resolve, reject) => {
              const imgElement = document.createElement('img') as HTMLImageElement;

              imgElement.onload = () => {
                console.log('✅ Image loaded successfully:', {
                  width: imgElement.width,
                  height: imgElement.height,
                  src: 'blob URL (CORS-safe)'
                });
                resolve(imgElement);
              };

              imgElement.onerror = (error) => {
                console.error('❌ Image load failed:', error);
                reject(new Error('Failed to load image for PDF conversion'));
              };

              imgElement.src = imageObjectUrl;
            });
          };

          const loadedImg = await loadImage();

          // 清理blob URL
          URL.revokeObjectURL(imageObjectUrl);

          // Create canvas and draw image
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');

          if (!ctx) {
            throw new Error('Could not get canvas context');
          }

          // Set canvas size to image size
          canvas.width = loadedImg.width;
          canvas.height = loadedImg.height;

          // Draw image on canvas
          ctx.drawImage(loadedImg, 0, 0);

          // Create PDF with proper dimensions
          const pdf = new jsPDF({
            orientation: loadedImg.width > loadedImg.height ? 'landscape' : 'portrait',
            unit: 'px',
            format: [loadedImg.width, loadedImg.height]
          });

          // Convert canvas to image data
          const imgData = canvas.toDataURL('image/jpeg', 0.95);

          // Add image to PDF
          pdf.addImage(imgData, 'JPEG', 0, 0, loadedImg.width, loadedImg.height);

          // Download PDF with new naming convention
          const filename = generatePDFFilename(extractedUuid, domain);
          pdf.save(filename);

          console.log('✅ PDF download completed:', filename);
          toast.success(t('pdf_download_success'));

        } catch (error) {
          console.error('❌ PDF conversion failed:', error);
          toast.error(`PDF conversion failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
          throw error;
        }
      } else {
        // For PNG download
        const response = await fetch(imageUrl);
        if (!response.ok) {
          throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
        }
        const blob = await response.blob();

        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = generateDownloadFilename(extractedUuid, 0, 'png', domain);
        document.body.appendChild(link);
        link.click();

        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        toast.success(t('png_download_success'));
      }
    } catch (error) {
      console.error("Download failed:", error);
      toast.error("Failed to download coloring page");
    }
  }

  // Keyboard event handling
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && selectedImage) {
        setSelectedImage(null);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [selectedImage])

  return (
    <section className={`py-6 ${className}`}>
      <div className="container mx-auto px-4">
        <div className="w-full max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-6">
            <h1 className="text-4xl font-bold mb-4">
              <span className="text-sky-500">{t('page_title').split(' ').slice(0, -2).join(' ')} </span>
              <span className="text-sky-600">{t('page_title').split(' ').slice(-2).join(' ')}</span>
            </h1>
            <p className="text-lg text-muted-foreground max-w-4xl mx-auto">
              {displayDescription}
            </p>
          </div>

          {/* Main Content Area */}
          <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6 mb-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Left Column - Prompt Input and Controls */}
              <div className="space-y-5">
                {/* Enter Your Prompt Section */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">{t('input_section_title')}</h3>
                  <div className="relative">
                    <Textarea
                      placeholder={t('input_placeholder')}
                      className="min-h-[140px] max-h-[200px] resize-none border-2 border-gray-300 focus:border-primary rounded-lg p-4 text-base leading-relaxed transition-all duration-200 hover:border-primary bg-white focus:bg-white"
                      value={textDescription}
                      onChange={(e) => setTextDescription(e.target.value)}
                    />
                    <div className="absolute bottom-3 right-3 text-xs text-gray-500">
                      {textDescription.length}/500
                    </div>
                  </div>
                </div>

                {/* Select Style Section - Reduced size */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">{t('style_label')}</h3>
                  <div className="grid grid-cols-5 gap-2">
                    {styleOptions.map((style) => (
                      <div
                        key={style.value}
                        className={`cursor-pointer transition-all duration-200 text-center ${
                          selectedStyle === style.value
                            ? 'opacity-100 scale-105'
                            : 'opacity-80 hover:opacity-100 hover:scale-102'
                        }`}
                        onClick={() => handleStyleClick(style)}
                      >
                        <div className="relative mb-1 flex justify-center">
                          {style.image ? (
                            <div className="relative">
                              <Image
                                src={style.image}
                                alt={style.label}
                                width={50}
                                height={50}
                                className={`object-cover rounded-lg shadow-md transition-all duration-200 ${
                                  selectedStyle === style.value
                                    ? 'ring-2 ring-sky-500 ring-offset-1'
                                    : 'hover:shadow-lg'
                                }`}
                              />
                              {/* Selected indicator - blue check icon */}
                              {selectedStyle === style.value && (
                                <div className="absolute -top-1 -right-1 w-5 h-5 bg-sky-500 rounded-full flex items-center justify-center shadow-md">
                                  <Check className="w-3 h-3 text-white" />
                                </div>
                              )}
                            </div>
                          ) : (
                            <div className="w-[50px] h-[50px] bg-gray-200 rounded-lg flex items-center justify-center">
                              <span className="text-xs text-gray-500">Style</span>
                            </div>
                          )}
                        </div>
                        <h4 className={`font-medium text-xs transition-colors duration-200 text-center ${
                          selectedStyle === style.value
                            ? 'text-sky-600 font-semibold'
                            : 'text-gray-700'
                        }`}>{style.label}</h4>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Size Selection - 标题和选项在同一行 */}
                <div className="space-y-3">
                  <div className="flex items-center gap-4">
                    <h3 className="text-lg font-semibold">{t('size_label')}</h3>
                    <div className="flex gap-3">
                      {sizeOptions.map((size) => (
                        <TooltipProvider key={size.value}>
                          <Tooltip delayDuration={100}>
                            <TooltipTrigger asChild>
                              <button
                                className={`flex items-center gap-3 p-3 rounded-lg border-2 transition-all duration-200 min-w-[120px] ${
                                  selectedSize === size.value
                                    ? 'border-sky-500 bg-gradient-to-r from-sky-50 to-sky-100 text-sky-700 shadow-md'
                                    : 'border-gray-200 hover:border-sky-300 hover:bg-sky-50 text-gray-600'
                                }`}
                                onClick={() => setSelectedSize(size.value)}
                              >
                                {/* Visual icon for size ratio - Left side */}
                                <div className="flex items-center justify-center">
                                  {size.icon === 'rectangle-vertical' && (
                                    <div className={`w-5 h-7 border-2 rounded transition-colors ${
                                      selectedSize === size.value
                                        ? 'border-sky-600 bg-sky-200'
                                        : 'border-gray-300'
                                    }`}></div>
                                  )}
                                  {size.icon === 'square' && (
                                    <div className={`w-6 h-6 border-2 rounded transition-colors ${
                                      selectedSize === size.value
                                        ? 'border-sky-600 bg-sky-200'
                                        : 'border-gray-300'
                                    }`}></div>
                                  )}
                                  {size.icon === 'rectangle-horizontal' && (
                                    <div className={`w-7 h-5 border-2 rounded transition-colors ${
                                      selectedSize === size.value
                                        ? 'border-sky-600 bg-sky-200'
                                        : 'border-gray-300'
                                    }`}></div>
                                  )}
                                </div>
                                {/* Text label - Right side */}
                                <span className={`text-sm ${
                                  selectedSize === size.value ? 'font-medium' : 'font-normal'
                                }`}>{size.label}</span>
                              </button>
                            </TooltipTrigger>
                            <TooltipContent className="bg-white border border-gray-200 shadow-lg rounded-md">
                              <p className="max-w-xs text-sm text-gray-700">{size.tooltip}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Options - Matching image-to-coloring style */}
                <div className="space-y-3">
                  <TooltipProvider>
                    {/* Enhance - 放在最上面 */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <label className="text-sm font-medium">{t('options.enhance')}</label>
                        <Tooltip delayDuration={100}>
                          <TooltipTrigger asChild>
                            <Info className="h-4 w-4 text-gray-400 hover:text-gray-600 cursor-pointer transition-colors" />
                          </TooltipTrigger>
                          <TooltipContent className="bg-white border border-gray-200 shadow-lg rounded-md max-w-xs">
                            <p className="text-sm text-gray-700">{t('options.enhance_tooltip')}</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                      <Switch
                        checked={enhance}
                        onCheckedChange={setEnhance}
                        className="data-[state=checked]:bg-sky-500 data-[state=unchecked]:bg-gray-200 [&>span]:bg-white"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <label className="text-sm font-medium">{t('options.private')}</label>
                        <Tooltip delayDuration={100}>
                          <TooltipTrigger asChild>
                            <Info className="h-4 w-4 text-gray-400 hover:text-gray-600 cursor-pointer transition-colors" />
                          </TooltipTrigger>
                          <TooltipContent className="bg-white border border-gray-200 shadow-lg rounded-md max-w-xs">
                            <p className="text-sm text-gray-700">{t('options.private_tooltip')}</p>
                          </TooltipContent>
                        </Tooltip>
                        <Crown className="h-4 w-4 text-yellow-500" />
                      </div>
                      <Switch
                        checked={privateMode}
                        onCheckedChange={(checked) => handlePremiumFeatureToggle('Private', privateMode, setPrivateMode, checked)}
                        className="data-[state=checked]:bg-sky-500 data-[state=unchecked]:bg-gray-200 [&>span]:bg-white"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <label className="text-sm font-medium">{t('options.remove_watermark')}</label>
                        <Tooltip delayDuration={100}>
                          <TooltipTrigger asChild>
                            <Info className="h-4 w-4 text-gray-400 hover:text-gray-600 cursor-pointer transition-colors" />
                          </TooltipTrigger>
                          <TooltipContent className="bg-white border border-gray-200 shadow-lg rounded-md max-w-xs">
                            <p className="text-sm text-gray-700">{t('options.remove_watermark_tooltip')}</p>
                          </TooltipContent>
                        </Tooltip>
                        <Crown className="h-4 w-4 text-yellow-500" />
                      </div>
                      <Switch
                        checked={removeWatermark}
                        onCheckedChange={(checked) => handlePremiumFeatureToggle('Remove Watermark', removeWatermark, setRemoveWatermark, checked)}
                        className="data-[state=checked]:bg-sky-500 data-[state=unchecked]:bg-gray-200 [&>span]:bg-white"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <label className="text-sm font-medium">{t('options.high_quality_printing')}</label>
                        <Tooltip delayDuration={100}>
                          <TooltipTrigger asChild>
                            <Info className="h-4 w-4 text-gray-400 hover:text-gray-600 cursor-pointer transition-colors" />
                          </TooltipTrigger>
                          <TooltipContent className="bg-white border border-gray-200 shadow-lg rounded-md max-w-xs">
                            <p className="text-sm text-gray-700">{t('options.high_quality_printing_tooltip')}</p>
                          </TooltipContent>
                        </Tooltip>
                        <Crown className="h-4 w-4 text-yellow-500" />
                      </div>
                      <Switch
                        checked={highQualityPrinting}
                        onCheckedChange={(checked) => handlePremiumFeatureToggle('High Quality Printing', highQualityPrinting, setHighQualityPrinting, checked)}
                        className="data-[state=checked]:bg-sky-500 data-[state=unchecked]:bg-gray-200 [&>span]:bg-white"
                      />
                    </div>
                  </TooltipProvider>
                </div>

                {/* Generate Button */}
                <div className="pt-2">
                  <Button
                    disabled={isLoading || !textDescription.trim()}
                    onClick={handleSubmit}
                    className="w-full py-5 text-lg rounded-lg bg-gradient-to-r from-sky-500 to-sky-600 hover:from-sky-600 hover:to-sky-700 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mr-3"></div>
                      {processingStep || 'Generating...'}
                    </>
                  ) : (
                    <>
                      <Wand2 className="h-5 w-5 mr-2" />
                      {t('generate_button_text')}
                    </>
                  )}
                </Button>
                </div>
              </div>

              {/* Right Column - Result Display Area */}
              <div className="relative">
                <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg border-2 border-dashed border-gray-200 p-6 flex flex-col items-center justify-center h-[500px] transition-all duration-300">
                  {isLoading ? (
                    <div className="text-center space-y-6">
                      <div className="relative">
                        <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-500 mx-auto"></div>
                        <div className="absolute inset-0 flex items-center justify-center">
                          <Wand2 className="h-6 w-6 text-blue-500" />
                        </div>
                      </div>
                      <div className="space-y-4">
                        <p className="text-lg font-medium text-gray-700">{processingStep}</p>
                        <div className="w-64 mx-auto">
                          <Progress value={progress} className="h-2" />
                        </div>
                        <p className="text-sm text-gray-500">{Math.round(progress)}% Complete</p>

                        {/* Generation Time Notice - 在apicore和kie provider时显示 */}
                        {(() => {
                          // 在配置加载完成且是apicore或kie时显示时间提示，或者在异步任务时显示
                          const shouldShow = (!providerConfigLoading &&
                            (providerConfig?.activeProvider === 'apicore' || providerConfig?.activeProvider === 'kie')) ||
                            isAsyncTask;

                          return shouldShow;
                        })() && (
                          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mx-auto max-w-sm">
                            <p className="text-sm text-blue-700 text-center">
                              {t('generation_time_notice')}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  ) : showExampleResult && selectedExampleImage ? (
                    <div className="w-full h-full flex flex-col items-center justify-center space-y-4">
                      {/* Example Result Display - Direct image display */}
                      <div className="relative w-full max-w-md h-80">
                        <Image
                          src={selectedExampleImage}
                          alt="Example result"
                          fill
                          className="object-contain"
                          sizes="(max-width: 768px) 100vw, 400px"
                        />
                      </div>

                      {/* Download buttons for example */}
                      <div className="flex gap-2 justify-center">
                        <Button
                          variant="outline"
                          size="sm"
                          className="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100 hover:border-blue-300 shadow-sm"
                          onClick={() => handleDownload(selectedExampleImage!, 'example', 'png')}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          {t('download_formats.png')}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="bg-purple-50 border-purple-200 text-purple-700 hover:bg-purple-100 hover:border-purple-300 shadow-sm"
                          onClick={() => handleDownload(selectedExampleImage!, 'example', 'pdf')}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          {t('download_formats.pdf')}
                        </Button>
                      </div>
                    </div>
                  ) : generatedImages.length > 0 ? (
                    <div className="w-full h-full flex flex-col items-center justify-center space-y-4">
                      {/* Generated Image Display - Much larger size */}
                      <div className="relative w-full max-w-md h-80">
                        <Image
                          src={generatedImages[0].img_url}
                          alt="Generated coloring page"
                          fill
                          className="object-contain rounded-lg"
                        />
                      </div>

                      {/* Download buttons */}
                      <div className="flex gap-2 justify-center">
                        <Button
                          variant="outline"
                          size="sm"
                          className="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100 hover:border-blue-300 shadow-sm"
                          onClick={() => handleDownload(generatedImages[0].img_url, generatedImages[0].uuid, 'png')}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          {t('download_formats.png')}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="bg-purple-50 border-purple-200 text-purple-700 hover:bg-purple-100 hover:border-purple-300 shadow-sm"
                          onClick={() => handleDownload(generatedImages[0].img_url, generatedImages[0].uuid, 'pdf')}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          {t('download_formats.pdf')}
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-full text-center space-y-4">
                      {/* Placeholder with better visual design */}
                      <div className="relative">
                        <div className="w-32 h-32 mx-auto bg-white rounded-2xl shadow-inner border-2 border-gray-200 flex items-center justify-center">
                          <div className="text-center">
                            <Palette className="w-12 h-12 text-gray-300 mx-auto mb-2" />
                            <div className="w-16 h-1 bg-gray-200 rounded mx-auto mb-1"></div>
                            <div className="w-12 h-1 bg-gray-200 rounded mx-auto mb-1"></div>
                            <div className="w-20 h-1 bg-gray-200 rounded mx-auto"></div>
                          </div>
                        </div>
                        {/* Decorative elements */}
                        <div className="absolute -top-2 -right-2 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                          <svg className="w-3 h-3 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                        </div>
                        <div className="absolute -bottom-2 -left-2 w-4 h-4 bg-pink-100 rounded-full"></div>
                      </div>
                      <div className="space-y-2">
                        <h3 className="text-lg font-medium text-gray-600">{t('ready_to_generate_title')}</h3>
                        <p className="text-sm text-gray-500 max-w-xs mx-auto">
                          {t('ready_to_generate_description')}
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Example Prompts Section - Always show, disable during loading */}
                <div className="mt-4">
                  <div className="text-center mb-3">
                    <h4 className="text-sm font-semibold text-gray-700 mb-1">
                      💡 {t('examples_title')}
                    </h4>
                    <p className="text-xs text-gray-500">
                      {t('examples_subtitle')}
                    </p>
                  </div>
                  {/* Staggered prompt labels layout - More rows for height balance */}
                  <div className="space-y-2">
                    {/* First row - 4 labels */}
                    <div className="flex flex-wrap gap-2 justify-center">
                      {examplePrompts.slice(0, 4).map((example, index) => (
                        <button
                          key={index}
                          className={`px-3 py-2 rounded-full text-xs font-medium transition-all duration-200 ${
                            isLoading
                              ? 'cursor-not-allowed opacity-50 bg-gray-100 text-gray-400'
                              : selectedExampleImage === example.image
                                ? 'bg-sky-500 text-white shadow-md'
                                : 'bg-gray-100 text-gray-700 hover:bg-sky-100 hover:text-sky-700 hover:shadow-sm'
                          }`}
                          onClick={() => !isLoading && handleExamplePromptClick(example)}
                          disabled={isLoading}
                        >
                          {example.prompt}
                        </button>
                      ))}
                    </div>
                    {/* Second row - 3 labels, offset */}
                    <div className="flex flex-wrap gap-2 justify-center pl-8">
                      {examplePrompts.slice(4, 7).map((example, index) => (
                        <button
                          key={index + 4}
                          className={`px-3 py-2 rounded-full text-xs font-medium transition-all duration-200 ${
                            isLoading
                              ? 'cursor-not-allowed opacity-50 bg-gray-100 text-gray-400'
                              : selectedExampleImage === example.image
                                ? 'bg-sky-500 text-white shadow-md'
                                : 'bg-gray-100 text-gray-700 hover:bg-sky-100 hover:text-sky-700 hover:shadow-sm'
                          }`}
                          onClick={() => !isLoading && handleExamplePromptClick(example)}
                          disabled={isLoading}
                        >
                          {example.prompt}
                        </button>
                      ))}
                    </div>
                    {/* Third row - 3 labels */}
                    <div className="flex flex-wrap gap-2 justify-center">
                      {examplePrompts.slice(7, 10).map((example, index) => (
                        <button
                          key={index + 7}
                          className={`px-3 py-2 rounded-full text-xs font-medium transition-all duration-200 ${
                            isLoading
                              ? 'cursor-not-allowed opacity-50 bg-gray-100 text-gray-400'
                              : selectedExampleImage === example.image
                                ? 'bg-sky-500 text-white shadow-md'
                                : 'bg-gray-100 text-gray-700 hover:bg-sky-100 hover:text-sky-700 hover:shadow-sm'
                          }`}
                          onClick={() => !isLoading && handleExamplePromptClick(example)}
                          disabled={isLoading}
                        >
                          {example.prompt}
                        </button>
                      ))}
                    </div>
                    {/* Fourth row - 2 labels, offset right */}
                    <div className="flex flex-wrap gap-2 justify-center pr-8">
                      {examplePrompts.slice(10, 12).map((example, index) => (
                        <button
                          key={index + 10}
                          className={`px-3 py-2 rounded-full text-xs font-medium transition-all duration-200 ${
                            isLoading
                              ? 'cursor-not-allowed opacity-50 bg-gray-100 text-gray-400'
                              : selectedExampleImage === example.image
                                ? 'bg-sky-500 text-white shadow-md'
                                : 'bg-gray-100 text-gray-700 hover:bg-sky-100 hover:text-sky-700 hover:shadow-sm'
                          }`}
                          onClick={() => !isLoading && handleExamplePromptClick(example)}
                          disabled={isLoading}
                        >
                          {example.prompt}
                        </button>
                      ))}
                    </div>
                    {/* Fifth row - Additional prompts for height balance */}
                    {examplePrompts.length > 12 && (
                      <div className="flex flex-wrap gap-2 justify-center pl-4">
                        {examplePrompts.slice(12, 15).map((example, index) => (
                          <button
                            key={index + 12}
                            className={`px-3 py-2 rounded-full text-xs font-medium transition-all duration-200 ${
                              isLoading
                                ? 'cursor-not-allowed opacity-50 bg-gray-100 text-gray-400'
                                : selectedExampleImage === example.image
                                  ? 'bg-sky-500 text-white shadow-md'
                                  : 'bg-gray-100 text-gray-700 hover:bg-sky-100 hover:text-sky-700 hover:shadow-sm'
                            }`}
                            onClick={() => !isLoading && handleExamplePromptClick(example)}
                            disabled={isLoading}
                          >
                            {example.prompt}
                          </button>
                        ))}
                      </div>
                    )}
                    {/* Sixth row - More prompts for better height balance */}
                    {examplePrompts.length > 15 && (
                      <div className="flex flex-wrap gap-2 justify-center pr-4">
                        {examplePrompts.slice(15, 18).map((example, index) => (
                          <button
                            key={index + 15}
                            className={`px-3 py-2 rounded-full text-xs font-medium transition-all duration-200 ${
                              isLoading
                                ? 'cursor-not-allowed opacity-50 bg-gray-100 text-gray-400'
                                : selectedExampleImage === example.image
                                  ? 'bg-sky-500 text-white shadow-md'
                                  : 'bg-gray-100 text-gray-700 hover:bg-sky-100 hover:text-sky-700 hover:shadow-sm'
                            }`}
                            onClick={() => !isLoading && handleExamplePromptClick(example)}
                            disabled={isLoading}
                          >
                            {example.prompt}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* History Section */}
          {generatedImages.length > 1 && (
            <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-5">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  {t('history_title')}
                  <span className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full ml-2">
                    {generatedImages.length}
                  </span>
                </h3>
                <Button variant="ghost" size="sm" className="text-blue-600 hover:text-blue-700">
                  {t('view_all')}
                </Button>
              </div>
              <div className="grid grid-cols-3 gap-3">
                {generatedImages.slice(0, 3).map((photo) => (
                  <div key={photo.uuid} className="relative group">
                    <div
                      className="aspect-square relative overflow-hidden rounded-lg bg-muted cursor-pointer border-2 border-transparent hover:border-primary transition-all duration-200"
                      onClick={() => setSelectedImage(photo)}
                    >
                      <Image
                        src={photo.img_url}
                        alt={photo.img_description || "Generated coloring page"}
                        fill
                        className="object-cover transition-transform group-hover:scale-105"
                        onLoad={() => console.log("✅ Image loaded successfully:", photo.img_url)}
                        onError={(e) => console.error("❌ Image failed to load:", photo.img_url, e)}
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center">
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                          <div className="bg-white rounded-full p-2">
                            <Download className="w-4 h-4 text-gray-700" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

        </div>
      </div>

      {/* Large Image View Modal */}
      {selectedImage && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
          onClick={() => setSelectedImage(null)}
        >
          <div className="relative max-w-4xl max-h-full">
            <div className="relative">
              <Image
                src={selectedImage.img_url}
                alt={selectedImage.img_description || "Generated coloring page"}
                width={800}
                height={800}
                className="max-w-full max-h-[80vh] object-contain rounded-lg"
                onClick={(e) => e.stopPropagation()}
              />
              <Button
                variant="secondary"
                size="sm"
                className="absolute top-2 right-2"
                onClick={() => setSelectedImage(null)}
              >
                <X className="h-4 w-4" />
              </Button>
              <Button
                variant="secondary"
                size="sm"
                className="absolute bottom-2 right-2"
                onClick={(e) => {
                  e.stopPropagation();
                  handleDownload(selectedImage.img_url, selectedImage.uuid, 'png');
                }}
              >
                <Download className="h-4 w-4 mr-1" />
                Download
              </Button>
            </div>
            {selectedImage.img_description && (
              <div className="mt-4 text-center">
                <p className="text-foreground text-sm bg-background/90 rounded px-3 py-2 inline-block">
                  {selectedImage.img_description}
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 订阅弹窗 */}
      {!DISABLE_PREMIUM_CHECK && (
        <SubscriptionModal
          isOpen={showSubscriptionModal}
          onClose={() => setShowSubscriptionModal(false)}
          type={subscriptionModalType}
          currentCredits={credits?.left_credits || 0}
          requiredCredits={CreditsAmount.ColoringGenerateCost}
          featureName={subscriptionFeatureName}
        />
      )}
    </section>
  )
}
