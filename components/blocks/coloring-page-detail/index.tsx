"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { AspectRatio } from "@/components/ui/aspect-ratio"
import {
  Download,
  Share2,
  Heart,
  Printer,
  Clock,
  Palette
} from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { ColoringPage } from "@/types/coloring-category"
import { ColoringPagesService } from '@/lib/services/coloring-pages-service'
import { coloringCategories } from "@/lib/coloring-categories"
import ColoringPageSEO from "@/components/seo/coloring-page-seo"
import ColoringTips from "@/components/blocks/coloring-tips"

interface ColoringPageDetailProps {
  coloringPage: ColoringPage
  relatedPages?: ColoringPage[]
}

export default function ColoringPageDetail({ coloringPage, relatedPages: initialRelatedPages }: ColoringPageDetailProps) {
  const [relatedPages, setRelatedPages] = useState<ColoringPage[]>(initialRelatedPages || [])
  const [isLiked, setIsLiked] = useState(false)

  const category = coloringCategories.find(cat => cat.id === coloringPage.category_id)
  const subcategory = category?.subcategories?.find(sub => sub.id === coloringPage.subcategory_id)

  // 生成JSON-LD结构化数据
  const generateJsonLd = () => {
    const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://makecoloring.com';

    return {
      "@context": "https://schema.org",
      "@type": "CreativeWork",
      "name": coloringPage.title,
      "description": coloringPage.description,
      "image": {
        "@type": "ImageObject",
        "url": coloringPage.image_url,
        "description": coloringPage.alt_text || coloringPage.title,
        "width": "400",
        "height": "400"
      },
      "creator": {
        "@type": "Organization",
        "name": "MakeColoring.com",
        "url": baseUrl
      },
      "publisher": {
        "@type": "Organization",
        "name": "MakeColoring.com",
        "url": baseUrl,
        "logo": {
          "@type": "ImageObject",
          "url": `${baseUrl}/logo.png`
        }
      },
      "dateCreated": coloringPage.created_at,
      "dateModified": coloringPage.updated_at || coloringPage.created_at,
      "genre": "Coloring Page",
      "isAccessibleForFree": true,
      "license": "https://creativecommons.org/licenses/by-nc/4.0/",
      "audience": {
        "@type": "Audience",
        "audienceType": coloringPage.age_group === 'all' ? 'General' : coloringPage.age_group
      },
      "keywords": coloringPage.tags?.join(', ') || '',
      "about": {
        "@type": "Thing",
        "name": category?.name || 'Coloring Pages'
      },
      "interactionStatistic": [
        {
          "@type": "InteractionCounter",
          "interactionType": "https://schema.org/DownloadAction",
          "userInteractionCount": coloringPage.download_count || 0
        },
        {
          "@type": "InteractionCounter",
          "interactionType": "https://schema.org/ViewAction",
          "userInteractionCount": coloringPage.view_count || 0
        }
      ],
      "url": `${baseUrl}${coloringPage.seo_slug}`,
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": `${baseUrl}${coloringPage.seo_slug}`
      }
    }
  }

  useEffect(() => {
    // 如果没有传入相关页面，则获取相关页面
    if (!initialRelatedPages || initialRelatedPages.length === 0) {
      const loadRelatedPages = async () => {
        try {
          const related = await ColoringPagesService.getRelatedPages(
            coloringPage.id,
            coloringPage.category_id || '',
            coloringPage.subcategory_id,
            4
          )
          setRelatedPages(related)
        } catch (error) {
          console.error('Failed to load related pages:', error)
          setRelatedPages([])
        }
      }
      loadRelatedPages()
    }
  }, [coloringPage.id, coloringPage.category_id, coloringPage.subcategory_id, initialRelatedPages])

  const handleDownload = () => {
    // 实现下载逻辑
    console.log("Downloading:", coloringPage.title)
  }

  const handleShare = () => {
    // 实现分享逻辑
    if (navigator.share) {
      navigator.share({
        title: coloringPage.title,
        text: coloringPage.description,
        url: window.location.href,
      })
    }
  }

  return (
    <>
      {/* SEO结构化数据组件 */}
      <ColoringPageSEO coloringPage={coloringPage} />

      {/* 主要内容的JSON-LD结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(generateJsonLd()) }}
      />

      <div className="container mx-auto px-4 py-8">
        {/* 面包屑导航 */}
        <nav className="flex items-center space-x-2 text-sm text-muted-foreground mb-6">
          <Link href="/free-coloring-pages" className="hover:text-primary">
            Free Coloring Pages
          </Link>
          {category && (
            <>
              <span>/</span>
              <Link href={`/${category.seo_slug}`} className="hover:text-primary">
                {category.name}
              </Link>
            </>
          )}
          {subcategory && (
            <>
              <span>/</span>
              <Link href={`/${subcategory.seo_slug}`} className="hover:text-primary">
                {subcategory.name}
              </Link>
            </>
          )}
          <span>/</span>
          <span className="text-foreground">{coloringPage.title}</span>
        </nav>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 左侧：图片和主要操作 */}
          <div className="lg:col-span-2">
            <Card className="overflow-hidden">
              <AspectRatio ratio={1}>
                <Image
                  src={coloringPage.image_url}
                  alt={coloringPage.alt_text || coloringPage.title}
                  fill
                  className="object-cover"
                  sizes="(max-width: 1024px) 100vw, 66vw"
                  priority
                />
              </AspectRatio>
            </Card>

            {/* 操作按钮 */}
            <div className="flex flex-wrap gap-3 mt-6">
              <Button onClick={handleDownload} className="flex-1 sm:flex-none">
                <Download className="h-4 w-4 mr-2" />
                Download Free
              </Button>
              <Button variant="outline" onClick={handleShare}>
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
              <Button 
                variant="outline" 
                onClick={() => setIsLiked(!isLiked)}
                className={isLiked ? "text-red-500" : ""}
              >
                <Heart className={`h-4 w-4 mr-2 ${isLiked ? "fill-current" : ""}`} />
                {isLiked ? "Liked" : "Like"}
              </Button>
              <Button variant="outline">
                <Printer className="h-4 w-4 mr-2" />
                Print
              </Button>
            </div>
          </div>

          {/* 右侧：详细信息 */}
          <div className="space-y-6">
            {/* 标题和基本信息 */}
            <div>
              <h1 className="text-2xl font-bold text-foreground mb-2">
                {coloringPage.title}
              </h1>
              <p className="text-muted-foreground mb-4">
                {coloringPage.description}
              </p>

              {/* 标签和属性 */}
              <div className="flex flex-wrap gap-2 mb-4">
                {coloringPage.print_size && (
                  <Badge variant="secondary">
                    {coloringPage.print_size}
                  </Badge>
                )}
                {coloringPage.tags?.map((tag) => (
                  <Badge key={tag} variant="outline">
                    {tag}
                  </Badge>
                ))}
              </div>

              {/* 统计信息 */}
              <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                <div className="flex items-center">
                  <Download className="h-4 w-4 mr-1" />
                  {coloringPage.download_count} downloads
                </div>
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-1" />
                  {new Date(coloringPage.created_at).toLocaleDateString()}
                </div>
              </div>
            </div>

            <Separator />

            {/* 涂色技巧 */}
            {coloringPage.coloring_tips && coloringPage.coloring_tips.length > 0 && (
              <div>
                <h3 className="font-semibold mb-3 flex items-center">
                  <Palette className="h-4 w-4 mr-2" />
                  Coloring Tips
                </h3>
                <ul className="space-y-2">
                  {coloringPage.coloring_tips.map((tip, index) => (
                    <li key={index} className="text-sm text-muted-foreground flex items-start">
                      <span className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0" />
                      {tip}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* 推荐颜色 */}
            {coloringPage.color_suggestions && coloringPage.color_suggestions.length > 0 && (
              <div>
                <h3 className="font-semibold mb-3">Suggested Colors</h3>
                <div className="flex flex-wrap gap-2">
                  {coloringPage.color_suggestions.map((color, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {color}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* 使用场景 */}
            {coloringPage.usage_scenarios && coloringPage.usage_scenarios.length > 0 && (
              <div>
                <h3 className="font-semibold mb-3">Perfect For</h3>
                <div className="flex flex-wrap gap-2">
                  {coloringPage.usage_scenarios.map((scenario, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {scenario}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 相关涂色页面 */}
        {relatedPages.length > 0 && (
          <div className="mt-12">
            <h2 className="text-2xl font-bold mb-6">Related Coloring Pages</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {relatedPages.map((page) => (
                <Card key={page.id} className="overflow-hidden group hover:shadow-lg transition-all duration-300">
                  <AspectRatio ratio={1}>
                    <Image
                      src={page.image_url}
                      alt={page.title}
                      fill
                      className="object-cover transition-transform group-hover:scale-105"
                      sizes="(max-width: 768px) 50vw, (max-width: 1200px) 25vw, 20vw"
                    />
                  </AspectRatio>
                  <CardContent className="p-3">
                    <Link href={`/${page.seo_slug}`}>
                      <h3 className="font-medium text-sm hover:text-primary transition-colors line-clamp-2">
                        {page.title}
                      </h3>
                    </Link>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* 涂色技巧和SEO内容区域 */}
        <div className="mt-16">
          <div className="prose prose-sm max-w-none mb-8">
            <h2>About This {coloringPage.title}</h2>
            <p>
              This beautiful {coloringPage.title.toLowerCase()} coloring page is perfect for creative expression and relaxation.
              Whether you're a beginner or an experienced colorist, this design offers the perfect balance of detail and simplicity.
            </p>

            {coloringPage.educational_value && (
              <>
                <h3>Educational Value</h3>
                <p>{coloringPage.educational_value}</p>
              </>
            )}
          </div>

          {/* 涂色技巧组件 */}
          <ColoringTips coloringPage={coloringPage} />

          <div className="prose prose-sm max-w-none mt-8">
            <h3>Printing Tips</h3>
            <ul>
              <li>Use high-quality paper for best results</li>
              <li>Adjust printer settings to "Best Quality" or "Photo"</li>
              <li>Ensure your printer has enough ink for clear lines</li>
              <li>Consider printing multiple copies for practice</li>
            </ul>
          </div>
        </div>
      </div>
    </>
  )
}
