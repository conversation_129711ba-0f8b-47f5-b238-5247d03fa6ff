"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Section as SectionType } from "@/types/blocks/section";
import Icon from "@/components/icon";
import FeatureImages from "./feature-images";

export default function Feature4({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  return (
    <section id={section.name} className="py-12">
      {/* 整体标题和描述 */}
      {(section.title || section.description) && (
        <div className="container">
          <div className="mx-auto mb-12 max-w-6xl text-center">
            {section.label && (
              <Badge className="text-xs font-medium mb-4">
                {section.label}
              </Badge>
            )}
            {section.title && (
              <h2 className="mb-4 text-3xl font-bold tracking-tight lg:text-4xl text-center">
                {section.title}
              </h2>
            )}
            {section.description && (
              <p className="text-lg text-muted-foreground text-center">
                {section.description}
              </p>
            )}
          </div>
        </div>
      )}

      {/* 功能列表 */}
      <div className="space-y-0">
        {section.items?.map((item, index) => {
          // 确定图片展示模式
          let displayMode = item.image_display_mode;
          const hasImages = item.images?.length || item.image;

          if (!displayMode && item.images) {
            if (item.images.length === 1) displayMode = "single";
            else if (item.images.length === 2) displayMode = "before-after";
            else if (item.images.length === 3) displayMode = "cards";
            else if (item.images.length >= 4) displayMode = "grid";
            else displayMode = "single";
          }

          // 准备图片数据
          const images = item.images || (item.image ? [item.image] : []);

          return (
            <div
              key={index}
              className={`py-8 ${
                index % 2 === 0 ? "bg-background" : "bg-muted/30"
              }`}
            >
              <div className="container">
                <div className="grid items-center gap-8 lg:grid-cols-2 lg:gap-16">
                  {/* 文字内容区域 */}
                  <div
                    className={`flex flex-col justify-center ${
                      index % 2 === 1 ? "lg:order-2" : "lg:order-1"
                    }`}
                  >
                    <div className="max-w-none px-4 lg:px-0">
                      {item.title && (
                        <h3 className="mb-3 text-2xl font-bold tracking-tight lg:text-3xl">
                          {item.title}
                        </h3>
                      )}

                      {item.description && (
                        <div
                          className="mb-4 text-lg text-muted-foreground leading-relaxed"
                          dangerouslySetInnerHTML={{ __html: item.description }}
                        />
                      )}

                      {item.buttons && item.buttons.length > 0 && (
                        <div className="flex flex-wrap gap-3">
                          {item.buttons.map((button, buttonIndex) => (
                            <Button
                              key={buttonIndex}
                              variant={button.variant || "default"}
                              size={(button.size === "md" ? "default" : button.size) || "lg"}
                              className={button.className}
                              asChild
                            >
                              <a
                                href={button.url}
                                target={button.target || "_self"}
                                className="inline-flex items-center gap-2"
                              >
                                {button.icon && (
                                  <Icon name={button.icon} className="size-4" />
                                )}
                                {button.title}
                              </a>
                            </Button>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 图片区域 */}
                  {hasImages && (
                    <div
                      className={`flex items-center justify-center px-4 lg:px-0 ${
                        index % 2 === 1 ? "lg:order-1" : "lg:order-2"
                      }`}
                    >
                      <div className="relative w-full">
                        <FeatureImages
                          images={images}
                          displayMode={displayMode}
                          className="w-full"
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </section>
  );
}
