"use client";

import React from 'react';
import { SectionImage, ImageDisplayMode } from "@/types/blocks/section";
import { cn } from "@/lib/utils";
import BeforeAfterSlider from "@/components/ui/before-after-slider";

interface FeatureImagesProps {
  images: SectionImage[];
  displayMode?: ImageDisplayMode;
  className?: string;
}

export default function FeatureImages({
  images,
  displayMode = "single",
  className
}: FeatureImagesProps) {

  if (!images || images.length === 0) {
    return null;
  }

  // 3图卡片叠加模式 - 扇形布局优化
  if (displayMode === "cards" || (images.length === 3 && displayMode !== "grid")) {
    const [hoveredIndex, setHoveredIndex] = React.useState<number | null>(null);

    return (
      <div className={cn("w-full flex items-center justify-center py-8", className)}>
        <div
          className="relative w-full max-w-5xl h-80 sm:h-96 flex items-end justify-center px-4 pb-12"
          onMouseLeave={() => setHoveredIndex(null)}
        >
          {images.slice(0, 3).map((image, index) => {
            // 计算每张卡片的位置和旋转角度，增加底部重叠效果
            // 响应式间距：移动端较小，桌面端较大，适应缩小后的卡片尺寸
            const isMobile = typeof window !== 'undefined' && window.innerWidth < 640;
            const spacing = isMobile ? 70 : 85;

            const positions = [
              { rotate: -15, translateX: -spacing - 40, translateY: 10, zIndex: 10 }, // 左卡片，稍微下移，整体左移
              { rotate: 0, translateX: -40, translateY: 0, zIndex: 20 },              // 中间卡片（默认最前），整体左移
              { rotate: 15, translateX: spacing - 40, translateY: 10, zIndex: 10 }    // 右卡片，稍微下移，整体左移
            ];

            const position = positions[index];
            const isHovered = hoveredIndex === index;

            return (
              <div
                key={index}
                className={cn(
                  "absolute aspect-[2/3] w-40 sm:w-48 md:w-52 rounded-xl shadow-xl cursor-pointer",
                  "transition-all duration-500 ease-in-out",
                  isHovered && "scale-110 shadow-2xl z-50"
                )}
                style={{
                  transform: `translateX(${position.translateX}px) translateY(${position.translateY}px) rotate(${position.rotate}deg) ${isHovered ? 'scale(1.1) translateY(-10px)' : 'scale(1)'}`,
                  zIndex: isHovered ? 50 : position.zIndex,
                  transformOrigin: 'bottom center'
                }}
                onMouseEnter={() => setHoveredIndex(index)}
              >
                {/* 卡片内容 */}
                <div className={cn(
                  "relative w-full h-full transition-all duration-500 ease-in-out overflow-hidden rounded-xl",
                  isHovered && "brightness-110"
                )}>
                  <img
                    src={image.src}
                    alt={image.alt || `Card image ${index + 1}`}
                    className="w-full h-full object-cover transition-all duration-500 ease-in-out"
                  />
                  {image.title && (
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 via-black/50 to-transparent p-3 pt-8">
                      <p className={cn(
                        "text-center text-sm font-medium text-white transition-all duration-500 ease-in-out",
                        "drop-shadow-sm",
                        isHovered && "text-white font-semibold text-base"
                      )}>
                        {image.title}
                      </p>
                    </div>
                  )}
                </div>


              </div>
            );
          })}
        </div>
      </div>
    );
  }

  // 单图展示 - 修复居中问题
  if (displayMode === "single" || images.length === 1) {
    const image = images[0];
    return (
      <div className={cn("relative w-full flex items-center justify-center", className)}>
        <div className="relative w-full max-w-lg aspect-[4/3] mx-auto">
          <img
            src={image.src}
            alt={image.alt || "Feature image"}
            className="w-full h-full object-cover rounded-lg shadow-lg"
          />
          {image.title && (
            <div className="absolute bottom-4 left-4 bg-black/70 text-white px-3 py-1 rounded text-center">
              {image.title}
            </div>
          )}
        </div>
      </div>
    );
  }

  // Before-After Slider (双图)
  if (displayMode === "before-after" && images.length >= 2) {
    const beforeImage = images[0];
    const afterImage = images[1];

    // 确保图片有有效的src
    if (!beforeImage.src || !afterImage.src) {
      return null;
    }

    return (
      <div className={cn("w-full flex items-center justify-center", className)}>
        <div className="w-full max-w-lg scale-90">
          <BeforeAfterSlider
            beforeImage={{
              src: beforeImage.src,
              alt: beforeImage.alt || "Before",
              title: beforeImage.title
            }}
            afterImage={{
              src: afterImage.src,
              alt: afterImage.alt || "After",
              title: afterImage.title
            }}
            className="shadow-lg w-full"
            aspectRatio="4/3"
            showLabels={true}
            initialPosition={50}
          />
        </div>
      </div>
    );
  }

  // 四宫格展示
  if (displayMode === "grid" && images.length >= 4) {
    return (
      <div className={cn("w-full flex items-center justify-center", className)}>
        <div className="grid grid-cols-2 gap-4 w-full max-w-lg aspect-square">
          {images.slice(0, 4).map((image, index) => (
            <div key={index} className="relative aspect-square">
              <img
                src={image.src}
                alt={image.alt || `Grid image ${index + 1}`}
                className="w-full h-full object-cover rounded-lg shadow-md"
              />
              {image.title && (
                <div className="absolute bottom-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs">
                  {image.title}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  }

  // 默认网格布局（适应任意数量的图片）
  return (
    <div className={cn("w-full flex items-center justify-center", className)}>
      <div className={cn(
        "grid gap-4 w-full max-w-6xl",
        images.length === 2 ? "grid-cols-1 sm:grid-cols-2" :
        images.length === 3 ? "grid-cols-1 sm:grid-cols-3" :
        "grid-cols-2",
      )}>
        {images.map((image, index) => (
          <div key={index} className="relative group">
            <img
              src={image.src}
              alt={image.alt || `Image ${index + 1}`}
              className="w-full h-auto rounded-lg shadow-md transition-transform duration-300 group-hover:scale-105"
            />
            {image.title && (
              <div className="absolute bottom-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs">
                {image.title}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
