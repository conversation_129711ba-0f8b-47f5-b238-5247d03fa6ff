"use client";

import { useState, useEffect } from "react";
import { Section as SectionType } from "@/types/blocks/section";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";
import Icon from "@/components/icon";

export default function feature({ section }: { section: SectionType }) {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);

  useEffect(() => {
    if (!section.items || section.items.length === 0) return;

    const interval = setInterval(() => {
      setIsTransitioning(true);

      setTimeout(() => {
        setCurrentStepIndex((prev) =>
          prev === section.items!.length - 1 ? 0 : prev + 1
        );
        setIsTransitioning(false);
      }, 300);
    }, 4000);

    return () => clearInterval(interval);
  }, [section.items]);

  if (section.disabled) {
    return null;
  }

  return (
    <section id={section.name} className="py-12 bg-gradient-to-b from-slate-50/20 to-background">
      <div className="container max-w-6xl">
        {/* 标题和描述 */}
        <div className="text-center mb-16">
          {section.label && (
            <Badge className="text-xs font-medium mb-4">
              {section.label}
            </Badge>
          )}
          {section.title && (
            <h2 className="mb-4 text-3xl font-bold tracking-tight lg:text-4xl text-slate-900">
              {section.title}
            </h2>
          )}
          {section.description && (
            <p className="text-lg text-slate-600 max-w-2xl mx-auto">
              {section.description}
            </p>
          )}
        </div>

        {/* 步骤展示区域 */}
        <div className="relative">
          {/* 步骤卡片 */}
          <div className="grid md:grid-cols-3 gap-6 lg:gap-8">
            {section.items?.map((item, index) => {
              const hasImage = item.image?.src;

              return (
                <div
                  key={index}
                  className={`relative transition-all duration-500 ${
                    index === currentStepIndex
                      ? "transform scale-105 z-10"
                      : "transform scale-100 opacity-75"
                  }`}
                >
                  {/* 步骤卡片 - 统一高度 */}
                  <div className={`bg-white rounded-2xl transition-all duration-300 text-center ${
                    hasImage
                      ? 'shadow-lg hover:shadow-xl border border-sky-100/50 hover:border-sky-200/50 p-6 h-auto'
                      : 'shadow-sm hover:shadow-md border border-slate-200/30 hover:border-sky-200/40 p-5 h-64'
                  } flex flex-col`}>

                    {hasImage ? (
                      // 有图片模式 - 保持原有布局
                      <>
                        <div className="relative mb-6 h-48 flex items-center justify-center">
                          <div className={`transition-all duration-300 ${
                            isTransitioning && index === currentStepIndex
                              ? "opacity-0 scale-95"
                              : "opacity-100 scale-100"
                          }`}>
                            <Image
                              src={item.image!.src!}
                              alt={item.image!.alt || item.title || "Step image"}
                              width={200}
                              height={150}
                              className="max-w-full h-auto rounded-lg shadow-md"
                            />
                          </div>
                          <div className="absolute -top-3 -right-3 w-8 h-8 bg-gradient-to-br from-sky-500 to-sky-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg border-2 border-white">
                            {index + 1}
                          </div>
                        </div>
                        <div className="space-y-3">
                          <h3 className="text-xl font-semibold text-slate-900">
                            {item.title}
                          </h3>
                          <p className="text-sm text-slate-600 leading-relaxed">
                            {item.description}
                          </p>
                        </div>
                      </>
                    ) : (
                      // 无图片模式 - 简洁统一设计
                      <>
                        {/* 步骤编号 - 顶部中央 */}
                        <div className="flex justify-center mb-4">
                          <div className="w-14 h-14 bg-sky-50 rounded-full flex items-center justify-center border border-sky-100">
                            {item.icon ? (
                              <Icon name={item.icon} className="w-6 h-6 text-sky-600" />
                            ) : (
                              <span className="text-sky-700 font-bold text-lg">{index + 1}</span>
                            )}
                          </div>
                        </div>

                        {/* 内容区域 - 居中对齐，统一间距 */}
                        <div className="flex-1 flex flex-col justify-center space-y-3">
                          <h3 className="text-lg font-semibold text-slate-900 leading-tight">
                            {item.title}
                          </h3>
                          <p className="text-sm text-slate-600 leading-relaxed px-2">
                            {item.description}
                          </p>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          {/* 底部指示器 */}
          <div className="flex justify-center mt-12">
            <div className="flex items-center space-x-2">
              {section.items?.map((_, index) => (
                <button
                  key={index}
                  onClick={() => {
                    setIsTransitioning(true);
                    setTimeout(() => {
                      setCurrentStepIndex(index);
                      setIsTransitioning(false);
                    }, 300);
                  }}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentStepIndex
                      ? "bg-sky-500 scale-125 shadow-lg"
                      : "bg-slate-300 hover:bg-slate-400"
                  }`}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
