"use client";

import React, { useState } from 'react';
import Image from 'next/image';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, X, ZoomIn, ZoomOut, Download } from 'lucide-react';
import { GalleryItem } from '@/types/blocks/gallery';

interface LightboxProps {
  items: GalleryItem[];
  currentIndex: number;
  isOpen: boolean;
  onClose: () => void;
  texts?: {
    imageAlt?: string;
    imageLoadError?: string;
  };
}

const ImageLightbox: React.FC<LightboxProps> = ({
  items,
  currentIndex,
  isOpen,
  onClose,
  texts = {
    imageAlt: 'Image',
    imageLoadError: 'Failed to load image'
  }
}) => {
  const [index, setIndex] = useState(currentIndex);
  const [zoom, setZoom] = useState(1);

  // 确保items数组有效且index在有效范围内
  const currentItem = items && items.length > 0 && index >= 0 && index < items.length
    ? items[index]
    : null;

  const handlePrevious = () => {
    if (!items || items.length === 0) return;
    setIndex((prevIndex) => (prevIndex === 0 ? items.length - 1 : prevIndex - 1));
    setZoom(1); // Reset zoom when changing images
  };

  const handleNext = () => {
    if (!items || items.length === 0) return;
    setIndex((prevIndex) => (prevIndex === items.length - 1 ? 0 : prevIndex + 1));
    setZoom(1); // Reset zoom when changing images
  };

  const handleZoomIn = () => {
    setZoom((prevZoom) => Math.min(prevZoom + 0.5, 3));
  };

  const handleZoomOut = () => {
    setZoom((prevZoom) => Math.max(prevZoom - 0.5, 1));
  };

  const handleDownload = () => {
    if (!currentItem || !currentItem.src) return;

    const link = document.createElement('a');
    link.href = currentItem.src;
    link.download = `image-${currentItem.id || 'unknown'}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <Dialog open={isOpen} onOpenChange={() => {
      onClose();
      setZoom(1); // Reset zoom when closing
    }}>
      <DialogContent className="max-w-7xl w-[95vw] h-[90vh] p-0 overflow-hidden bg-background/95 backdrop-blur-sm">
        <div className="relative w-full h-full flex flex-col">
          {/* Controls */}
          <div className="absolute top-4 right-4 z-50 flex gap-2">
            <Button variant="ghost" size="icon" onClick={handleZoomOut} disabled={zoom <= 1}>
              <ZoomOut className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="icon" onClick={handleZoomIn} disabled={zoom >= 3}>
              <ZoomIn className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="icon" onClick={handleDownload}>
              <Download className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* Image container */}
          <div className="flex-1 overflow-hidden relative">
            <div
              className="absolute inset-0 flex items-center justify-center transition-transform duration-200 ease-out"
              style={{
                transform: `scale(${zoom})`,
                cursor: zoom > 1 ? 'grab' : 'default'
              }}
            >
              {currentItem && currentItem.src ? (
                <Image
                  src={currentItem.src}
                  alt={currentItem.alt || texts.imageAlt || 'Image'}
                  fill
                  sizes="100vw"
                  className="object-contain"
                  priority
                />
              ) : (
                <div className="flex items-center justify-center w-full h-full text-muted-foreground">
                  {texts.imageLoadError}
                </div>
              )}
            </div>
          </div>

          {/* Navigation buttons */}
          <Button
            variant="ghost"
            size="icon"
            className="absolute left-4 top-1/2 -translate-y-1/2 h-12 w-12 rounded-full bg-background/50 backdrop-blur-sm"
            onClick={handlePrevious}
          >
            <ChevronLeft className="h-8 w-8" />
          </Button>

          <Button
            variant="ghost"
            size="icon"
            className="absolute right-4 top-1/2 -translate-y-1/2 h-12 w-12 rounded-full bg-background/50 backdrop-blur-sm"
            onClick={handleNext}
          >
            <ChevronRight className="h-8 w-8" />
          </Button>

          {/* Caption */}
          <div className="absolute bottom-0 left-0 right-0 bg-background/75 backdrop-blur-sm p-4 text-center">
            <p className="text-sm font-medium">{currentItem?.alt || texts.imageAlt || 'Image'}</p>
            <p className="text-xs text-muted-foreground mt-1">{`${index + 1} / ${items?.length || 0}`}</p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ImageLightbox;
