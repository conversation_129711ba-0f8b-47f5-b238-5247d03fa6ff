"use client";

import React, { useState } from 'react';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { Card } from '@/components/ui/card';
import { GalleryItem, GalleryVariant, ScreenshotItem } from '@/types/blocks/gallery';
import ImageLightbox from './lightbox';
import ScreenshotGallery from './screenshot-gallery';

interface GalleryProps {
  items?: GalleryItem[];
  screenshots?: ScreenshotItem[];
  className?: string;
  variant?: GalleryVariant;
  texts?: {
    imageAlt?: string;
    imageLoadError?: string;
    beforeLabel?: string;
    afterLabel?: string;
  };
}

export const ImageCard = ({ item, onClick }: { item: GalleryItem; onClick?: () => void }) => {
  return (
    <Card
      className="overflow-hidden group hover:shadow-lg transition-all duration-300 cursor-pointer rounded-lg"
      onClick={onClick}
    >
      <AspectRatio
        ratio={
          item.width && item.height
            ? item.width / item.height
            : item.size === 'wide'
              ? 16/9
              : item.size === 'tall'
                ? 3/4
                : 4/3
        }
        className="bg-muted"
      >
        <Image
          src={item.src}
          alt={item.alt || 'Image'}
          fill
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          className="object-cover transition-all duration-300 group-hover:scale-105"
        />
      </AspectRatio>
    </Card>
  );
};

const Gallery = ({
  items = [],
  screenshots = [],
  className,
  variant = 'masonry',
  texts = {
    imageAlt: 'Image',
    imageLoadError: 'Failed to load image',
    beforeLabel: 'Before',
    afterLabel: 'After'
  }
}: GalleryProps) => {
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const openLightbox = (index: number) => {
    setCurrentImageIndex(index);
    setLightboxOpen(true);
  };

  const closeLightbox = () => {
    setLightboxOpen(false);
  };

  const getItemSizeClass = (item: GalleryItem) => {
    if (item.className) return item.className;

    switch (item.size) {
      case 'large':
        return 'col-span-2 row-span-2';
      case 'wide':
        return 'col-span-2';
      case 'tall':
        return 'row-span-2';
      default:
        return '';
    }
  };

  // Screenshot variant
  if (variant === 'screenshot') {
    return (
      <ScreenshotGallery
        items={screenshots.length > 0 ? screenshots : undefined}
        className={className}
      />
    );
  }

  if (variant === 'grid') {
    return (
      <>
        <div className={cn('w-full', className)}>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 auto-rows-auto">
            {items.map((item, index) => (
              <div
                key={item.id}
                className={cn(
                  getItemSizeClass(item)
                )}
              >
                <ImageCard
                  item={item}
                  onClick={() => openLightbox(index)}
                />
              </div>
            ))}
          </div>
        </div>

        {/* Lightbox for full-screen image viewing */}
        <ImageLightbox
          items={items}
          currentIndex={currentImageIndex}
          isOpen={lightboxOpen}
          onClose={closeLightbox}
          texts={texts}
        />
      </>
    );
  }

  return (
    <>
      <div className={cn('w-full', className)}>
        <div className="columns-1 sm:columns-2 md:columns-3 lg:columns-4 gap-4 space-y-4">
          {items.map((item, index) => (
            <div
              key={item.id}
              className="break-inside-avoid mb-4"
            >
              <ImageCard
                item={item}
                onClick={() => openLightbox(index)}
              />
            </div>
          ))}
        </div>
      </div>

      {/* Lightbox for full-screen image viewing */}
      <ImageLightbox
        items={items}
        currentIndex={currentImageIndex}
        isOpen={lightboxOpen}
        onClose={closeLightbox}
        texts={texts}
      />
    </>
  );
};

export default Gallery;
