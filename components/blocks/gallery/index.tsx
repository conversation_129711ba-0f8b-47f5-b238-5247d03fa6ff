"use client";

import React from 'react';
import { Gallery as GalleryType, GalleryItem, ScreenshotItem } from '@/types/blocks/gallery';
import Gallery from './gallery';

interface GalleryBlockProps {
  gallery?: GalleryType;
  items?: GalleryItem[];
  screenshots?: ScreenshotItem[];
  maxItems?: number;
}

export default function GalleryBlock({ gallery, items = [], screenshots = [], maxItems }: GalleryBlockProps) {
  if (gallery?.disabled) {
    return null;
  }

  // Get the max items limit from props or gallery config
  const itemLimit = maxItems || gallery?.max_items || 0; // 0 means no limit

  // Apply the limit if it's greater than 0
  let galleryItems = gallery?.items || items;
  if (itemLimit > 0 && galleryItems.length > itemLimit) {
    galleryItems = galleryItems.slice(0, itemLimit);
  }

  // Use screenshots from gallery config or props
  const galleryScreenshots = gallery?.screenshots || screenshots;

  const variant = gallery?.variant || 'masonry';
  const className = gallery?.className || 'animate-in fade-in duration-700';

  // i18n texts for the lightbox
  const texts = {
    imageAlt: gallery?.lightbox?.imageAlt || 'Image',
    imageLoadError: gallery?.lightbox?.imageLoadError || 'Failed to load image',
    beforeLabel: gallery?.lightbox?.beforeLabel || 'Before',
    afterLabel: gallery?.lightbox?.afterLabel || 'After'
  };

  return (
    <section id={gallery?.name || 'gallery'} className="py-6">
      <div className="container mx-auto px-4">
        {(gallery?.title || gallery?.description) && (
          <div className="text-center mb-12">
            {gallery?.title && (
              <h2 className="text-3xl md:text-4xl font-bold mb-4">{gallery.title}</h2>
            )}
            {gallery?.description && (
              <p className="text-muted-foreground max-w-3xl mx-auto">
                {gallery.description}
              </p>
            )}
          </div>
        )}

        {(variant === 'screenshot' || galleryItems.length > 0) ? (
          <Gallery
            items={variant === 'screenshot' ? [] : galleryItems}
            screenshots={galleryScreenshots}
            variant={variant}
            className={className}
            texts={texts}
          />
        ) : (
          <div className="text-center py-12 bg-card rounded-lg border border-border">
            <div className="mb-4 text-4xl">🖼️</div>
            <h3 className="text-xl font-medium mb-2">{gallery?.empty_title || 'No Images'}</h3>
            <p className="text-muted-foreground max-w-md mx-auto mb-6">
              {gallery?.empty_description || 'There are no images in the gallery yet.'}
            </p>
          </div>
        )}
      </div>
    </section>
  );
}
