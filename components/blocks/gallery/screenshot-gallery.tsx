"use client";

import React from 'react';
import { cn } from '@/lib/utils';
import { ScreenshotItem } from '@/types/blocks/gallery';
import BeforeAfterSlider from '@/components/ui/before-after-slider';

interface ScreenshotGalleryProps {
  items?: ScreenshotItem[];
  className?: string;
}

// 固定的示例截图数据
const defaultScreenshots: ScreenshotItem[] = [
  {
    id: '1',
    title: 'AI 图像增强',
    description: '将普通照片转换为高质量的艺术作品',
    beforeImage: {
      src: '/imgs/showcases/1_1.png',
      alt: '原始图片'
    },
    afterImage: {
      src: '/imgs/showcases/1_2.png',
      alt: 'AI增强后的图片'
    }
  },
  {
    id: '2',
    title: '风格转换',
    description: '将照片转换为不同的艺术风格',
    beforeImage: {
      src: '/imgs/showcases/2_1.png',
      alt: '原始照片'
    },
    afterImage: {
      src: '/imgs/showcases/2_2.png',
      alt: '风格转换后的图片'
    }
  },
  {
    id: '3',
    title: '背景替换',
    description: '智能替换图片背景，保持主体完整',
    beforeImage: {
      src: '/imgs/showcases/3_1.png',
      alt: '原始背景'
    },
    afterImage: {
      src: '/imgs/showcases/3_2.png',
      alt: '替换背景后'
    }
  },
  {
    id: '4',
    title: '人像美化',
    description: '自然的人像美化和修饰',
    beforeImage: {
      src: '/imgs/showcases/4_1.png',
      alt: '原始人像'
    },
    afterImage: {
      src: '/imgs/showcases/4_2.png',
      alt: '美化后的人像'
    }
  },
  {
    id: '5',
    title: '背景替换',
    description: '智能替换图片背景，保持主体完整',
    beforeImage: {
      src: '/imgs/showcases/5_1.png',
      alt: '原始背景'
    },
    afterImage: {
      src: '/imgs/showcases/5_2.png',
      alt: '替换背景后'
    }
  },
  {
    id: '6',
    title: '人像美化',
    description: '自然的人像美化和修饰',
    beforeImage: {
      src: '/imgs/showcases/6_1.png',
      alt: '原始人像'
    },
    afterImage: {
      src: '/imgs/showcases/6_2.png',
      alt: '美化后的人像'
    }
  }
];

const ScreenshotCard = ({ item }: { item: ScreenshotItem }) => {
  return (
    <div className="overflow-hidden group hover:shadow-xl transition-all duration-500 rounded-lg">
      <BeforeAfterSlider
        beforeImage={item.beforeImage}
        afterImage={item.afterImage}
        className="rounded-lg"
      />
    </div>
  );
};

const ScreenshotGallery = ({
  items,
  className
}: ScreenshotGalleryProps) => {
  // 使用默认截图数据，如果没有提供 items 或者 items 为空
  const displayItems = items && items.length > 0 ? items : defaultScreenshots;

  return (
    <div className={cn('w-full', className)}>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {displayItems.map((item) => (
          <ScreenshotCard
            key={item.id}
            item={item}
          />
        ))}
      </div>
    </div>
  );
};

export default ScreenshotGallery;
