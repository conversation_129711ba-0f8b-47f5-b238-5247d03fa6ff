"use client";

import { useState, useEffect } from "react";
import { X, Clock } from "lucide-react";
import { ActivityBanner as ActivityBannerType } from "@/types/blocks/header";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { cn } from "@/lib/utils";

interface ActivityBannerProps {
  banner: ActivityBannerType;
}

interface TimeLeft {
  hours: number;
  minutes: number;
  seconds: number;
}

// 生成唯一的存储键，基于banner配置
const getStorageKey = (banner: ActivityBannerType) => {
  // 使用简单的哈希函数替代btoa，避免非ASCII字符问题
  const configString = JSON.stringify({
    countdown_hours: banner.countdown_hours,
    main_text: banner.main_text,
    countdown_text: banner.countdown_text
  });

  // 简单的字符串哈希函数
  let hash = 0;
  for (let i = 0; i < configString.length; i++) {
    const char = configString.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }

  return `activity_banner_${Math.abs(hash).toString(36)}`;
};

export default function ActivityBanner({ banner }: ActivityBannerProps) {
  const [isVisible, setIsVisible] = useState(true);
  const [timeLeft, setTimeLeft] = useState<TimeLeft>({ hours: 0, minutes: 0, seconds: 0 });

  // 初始化倒计时
  useEffect(() => {
    if (!banner.countdown_hours || banner.countdown_hours <= 0) return;

    const storageKey = getStorageKey(banner);
    let endTime: number;

    // 尝试从localStorage获取已存储的结束时间
    try {
      const storedEndTime = localStorage.getItem(storageKey);
      if (storedEndTime) {
        endTime = parseInt(storedEndTime, 10);
        // 检查存储的结束时间是否已过期
        if (endTime <= new Date().getTime()) {
          // 已过期，移除存储并重新计算
          localStorage.removeItem(storageKey);
          endTime = new Date().getTime() + (banner.countdown_hours * 60 * 60 * 1000);
          localStorage.setItem(storageKey, endTime.toString());
        }
      } else {
        // 没有存储的结束时间，创建新的
        endTime = new Date().getTime() + (banner.countdown_hours * 60 * 60 * 1000);
        localStorage.setItem(storageKey, endTime.toString());
      }
    } catch (error) {
      // localStorage不可用时的降级处理
      console.warn('localStorage not available, countdown will reset on page refresh');
      endTime = new Date().getTime() + (banner.countdown_hours * 60 * 60 * 1000);
    }

    const calculateTimeLeft = () => {
      const now = new Date().getTime();
      const difference = endTime - now;

      if (difference > 0) {
        const hours = Math.floor(difference / (1000 * 60 * 60));
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        setTimeLeft({ hours, minutes, seconds });
      } else {
        // 倒计时结束，自动关闭并清理存储
        setTimeLeft({ hours: 0, minutes: 0, seconds: 0 });
        setIsVisible(false);
        try {
          localStorage.removeItem(storageKey);
        } catch (error) {
          console.warn('Failed to remove expired countdown from localStorage');
        }
      }
    };

    // 立即计算一次
    calculateTimeLeft();

    // 每秒更新
    const timer = setInterval(calculateTimeLeft, 1000);

    return () => clearInterval(timer);
  }, [banner.countdown_hours, banner.main_text, banner.countdown_text]);

  if (!banner.show || !isVisible) {
    return null;
  }

  const handleClose = () => {
    setIsVisible(false);
  };

  const formatTime = (time: number) => time.toString().padStart(2, '0');

  return (
    <div
      className={cn(
        "relative w-full py-2 md:py-2 px-4 text-sm font-medium transition-all duration-300"
      )}
      style={{
        background: banner.background_color || "linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary)/0.8) 100%)",
        color: banner.text_color || "#ffffff"
      }}
    >
      {/* 关闭按钮 - 绝对定位到整个div的右侧，增加padding */}
      {banner.closable && (
        <button
          onClick={handleClose}
          className="absolute right-2 md:right-4 top-1/2 -translate-y-1/2 p-1 hover:bg-black/10 rounded transition-colors z-10"
          aria-label="关闭活动提示"
          style={{ color: banner.text_color || "#ffffff" }}
        >
          <X className="size-3 md:size-4" />
        </button>
      )}

      {/* 桌面端布局 */}
      <div className="hidden md:flex items-center justify-center gap-4 max-w-7xl mx-auto pr-12">
        {/* 左侧：倒计时图标 + 倒计时文字 + 时间框 */}
        <div className="flex items-center gap-2">
          <Clock className="size-4 shrink-0" />
          <span>{banner.countdown_text || "Offer Ends in"}</span>
          {banner.countdown_hours && banner.countdown_hours > 0 && (
            <div className="flex items-center gap-1">
              <span className="bg-white/20 backdrop-blur-sm px-2 py-1 rounded text-xs font-mono font-bold border border-white/30">
                {formatTime(timeLeft.hours)}
              </span>
              <span className="text-white/80">:</span>
              <span className="bg-white/20 backdrop-blur-sm px-2 py-1 rounded text-xs font-mono font-bold border border-white/30">
                {formatTime(timeLeft.minutes)}
              </span>
              <span className="text-white/80">:</span>
              <span className="bg-white/20 backdrop-blur-sm px-2 py-1 rounded text-xs font-mono font-bold border border-white/30">
                {formatTime(timeLeft.seconds)}
              </span>
            </div>
          )}
        </div>

        {/* 中间：主要文字 */}
        <span className="font-medium">
          {banner.main_text}
        </span>

        {/* 右侧：订阅按钮 */}
        {banner.button && (
          <Button
            size="sm"
            className={cn(
              "h-7 px-4 text-xs font-bold rounded-full",
              "hover:opacity-90 transition-opacity"
            )}
            style={{
              backgroundColor: banner.button_color || "#fbbf24",
              color: "#000000"
            }}
            asChild
          >
            <Link href={banner.button.url || "#"} target={banner.button.target}>
              {banner.button.title}
            </Link>
          </Button>
        )}
      </div>

      {/* 移动端布局 */}
      <div className="md:hidden max-w-full mx-auto pr-8">
        {/* 第一行：主要文字 */}
        <div className="text-center mb-2">
          <span className="font-medium text-sm">
            {banner.main_text}
          </span>
        </div>

        {/* 第二行：倒计时和按钮 */}
        <div className="flex items-center justify-center gap-3">
          {/* 倒计时部分 */}
          <div className="flex items-center gap-2">
            <Clock className="size-3 shrink-0" />
            <span className="text-xs">{banner.countdown_text || "Offer Ends in"}</span>
            {banner.countdown_hours && banner.countdown_hours > 0 && (
              <div className="flex items-center gap-1">
                <span className="bg-white/20 backdrop-blur-sm px-1.5 py-0.5 rounded text-xs font-mono font-bold border border-white/30">
                  {formatTime(timeLeft.hours)}
                </span>
                <span className="text-white/80 text-xs">:</span>
                <span className="bg-white/20 backdrop-blur-sm px-1.5 py-0.5 rounded text-xs font-mono font-bold border border-white/30">
                  {formatTime(timeLeft.minutes)}
                </span>
                <span className="text-white/80 text-xs">:</span>
                <span className="bg-white/20 backdrop-blur-sm px-1.5 py-0.5 rounded text-xs font-mono font-bold border border-white/30">
                  {formatTime(timeLeft.seconds)}
                </span>
              </div>
            )}
          </div>

          {/* 按钮 */}
          {banner.button && (
            <Button
              size="sm"
              className={cn(
                "h-6 px-3 text-xs font-bold rounded-full",
                "hover:opacity-90 transition-opacity"
              )}
              style={{
                backgroundColor: banner.button_color || "#fbbf24",
                color: "#000000"
              }}
              asChild
            >
              <Link href={banner.button.url || "#"} target={banner.button.target}>
                {banner.button.title}
              </Link>
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
