"use client";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button, buttonVariants } from "@/components/ui/button";
import { navigationMenuTriggerStyle } from "@/components/ui/navigation-menu";
import { CustomDropdown, CustomDropdownItem } from "@/components/ui/custom-dropdown";
import {
  <PERSON><PERSON>,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";

import { Header as HeaderType } from "@/types/blocks/header";
import Icon from "@/components/icon";
import { Link, usePathname } from "@/i18n/routing";
import LocaleToggle from "@/components/locale/toggle";
import { Menu } from "lucide-react";
import SignToggle from "@/components/sign/toggle";
import ThemeToggle from "@/components/theme/toggle";
import ActivityBanner from "./activity-banner";
import { cn } from "@/lib/utils";

export default function Header({ header }: { header: HeaderType }) {
  const pathname = usePathname();

  if (header.disabled) {
    return null;
  }

  // 判断是否为首页
  const isHomePage = pathname === '/' || pathname === '';

  return (
    <>
      {/* 活动提示条 - 只在首页显示 */}
      {header.activity_banner && isHomePage && (
        <ActivityBanner banner={header.activity_banner} />
      )}

      <section className={cn(
        "py-3",
        header.sticky && "sticky top-0 z-50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 shadow"
      )}>
        <div className="w-full max-w-full md:max-w-[1400px] mx-auto px-4 sm:px-6">
        <nav className="hidden justify-between lg:flex">
          <div className="flex items-center gap-6">
            <Link
              href={header.brand?.url || "/"}
              className="flex items-center gap-2"
            >
              {header.brand?.logo?.src && (
                <img
                  src={header.brand.logo.src}
                  alt={header.brand.logo.alt || header.brand.title}
                  className="w-8"
                />
              )}
              {header.brand?.title && (
                <span className="text-xl text-primary font-bold">
                  {header.brand?.title || ""}
                </span>
              )}
            </Link>
            <div className="flex items-center space-x-1">
              {header.nav?.items?.map((item, i) => {
                if (item.children && item.children.length > 0) {
                  return (
                    <CustomDropdown
                      key={i}
                      className="text-muted-foreground"
                      trigger={
                        <div className="flex items-center">
                          {item.icon && (
                            <Icon
                              name={item.icon}
                              className="size-4 shrink-0 mr-2"
                            />
                          )}
                          <span>{item.title}</span>
                        </div>
                      }
                    >
                      <div className="grid w-[280px] gap-1 p-2">
                        {item.children.map((iitem, ii) => (
                          <CustomDropdownItem
                            key={ii}
                            href={iitem.url}
                            target={iitem.target}
                          >
                            <div className="text-sm font-medium leading-none">
                              {iitem.title}
                            </div>
                          </CustomDropdownItem>
                        ))}
                      </div>
                    </CustomDropdown>
                  );
                }

                // 判断是否为外部链接
                if (item.target === '_blank' || item.url?.startsWith('http') || item.url?.startsWith('mailto:') || item.url?.startsWith('tel:')) {
                  return (
                    <a
                      key={i}
                      className={cn(
                        "text-muted-foreground",
                        navigationMenuTriggerStyle,
                        buttonVariants({
                          variant: "ghost",
                        })
                      )}
                      href={item.url}
                      target={item.target}
                    >
                      {item.icon && (
                        <Icon
                          name={item.icon}
                          className="size-4 shrink-0 mr-0"
                        />
                      )}
                      {item.title}
                    </a>
                  );
                }

                // 内部链接使用国际化Link组件
                return (
                  <Link
                    key={i}
                    className={cn(
                      "text-muted-foreground",
                      navigationMenuTriggerStyle,
                      buttonVariants({
                        variant: "ghost",
                      })
                    )}
                    href={item.url || ""}
                  >
                    {item.icon && (
                      <Icon
                        name={item.icon}
                        className="size-4 shrink-0 mr-0"
                      />
                    )}
                    {item.title}
                  </Link>
                );
              })}
            </div>
          </div>
          <div className="shrink-0 flex gap-2 items-center">
            {header.show_locale && <LocaleToggle />}
            {header.show_theme && <ThemeToggle />}

            {header.buttons?.map((item, i) => {
              return (
                <Button key={i} variant={item.variant}>
                  <Link
                    href={item.url || ""}
                    target={item.target || ""}
                    className="flex items-center gap-1"
                  >
                    {item.title}
                    {item.icon && (
                      <Icon name={item.icon} className="size-4 shrink-0" />
                    )}
                  </Link>
                </Button>
              );
            })}
            {header.show_sign && <SignToggle />}
          </div>
        </nav>

        <div className="block lg:hidden">
          <div className="flex items-center justify-between min-h-[48px]">
            <Link
              href={header.brand?.url || "/"}
              className="flex items-center gap-2 min-w-0 flex-1"
            >
              {header.brand?.logo?.src && (
                <img
                  src={header.brand.logo.src}
                  alt={header.brand.logo.alt || header.brand.title}
                  className="w-8 h-8 flex-shrink-0"
                />
              )}
              {header.brand?.title && (
                <span className="text-lg sm:text-xl font-bold truncate">
                  {header.brand?.title || ""}
                </span>
              )}
            </Link>
            <div className="flex-shrink-0">
              <Sheet>
                <SheetTrigger asChild>
                  <Button variant="default" size="icon" className="h-10 w-10">
                    <Menu className="size-4" />
                  </Button>
                </SheetTrigger>
              <SheetContent className="overflow-y-auto">
                <SheetHeader>
                  <SheetTitle>
                    <div className="flex items-center gap-2">
                      {header.brand?.logo?.src && (
                        <img
                          src={header.brand.logo.src}
                          alt={header.brand.logo.alt || header.brand.title}
                          className="w-8"
                        />
                      )}
                      {header.brand?.title && (
                        <span className="text-xl font-bold">
                          {header.brand?.title || ""}
                        </span>
                      )}
                    </div>
                  </SheetTitle>
                </SheetHeader>
                <div className="mb-8 mt-8 flex flex-col gap-4">
                  <Accordion type="single" collapsible className="w-full">
                    {header.nav?.items?.map((item, i) => {
                      if (item.children && item.children.length > 0) {
                        return (
                          <AccordionItem
                            key={i}
                            value={item.title || ""}
                            className="border-b-0"
                          >
                            <AccordionTrigger className="mb-4 py-0 font-semibold hover:no-underline text-left">
                              {item.title}
                            </AccordionTrigger>
                            <AccordionContent className="mt-2">
                              {item.children.map((iitem, ii) => {
                                // 判断是否为外部链接
                                if (iitem.target === '_blank' || iitem.url?.startsWith('http') || iitem.url?.startsWith('mailto:') || iitem.url?.startsWith('tel:')) {
                                  return (
                                    <a
                                      key={ii}
                                      className={cn(
                                        "flex select-none gap-4 rounded-md p-3 leading-none outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                                      )}
                                      href={iitem.url}
                                      target={iitem.target}
                                    >
                                      {iitem.icon && (
                                        <Icon
                                          name={iitem.icon}
                                          className="size-4 shrink-0"
                                        />
                                      )}
                                      <div>
                                        <div className="text-sm font-semibold">
                                          {iitem.title}
                                        </div>
                                      </div>
                                    </a>
                                  );
                                }

                                // 内部链接使用国际化Link组件
                                return (
                                  <Link
                                    key={ii}
                                    className={cn(
                                      "flex select-none gap-4 rounded-md p-3 leading-none outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                                    )}
                                    href={iitem.url || ""}
                                  >
                                    {iitem.icon && (
                                      <Icon
                                        name={iitem.icon}
                                        className="size-4 shrink-0"
                                      />
                                    )}
                                    <div>
                                      <div className="text-sm font-semibold">
                                        {iitem.title}
                                      </div>
                                    </div>
                                  </Link>
                                );
                              })}
                            </AccordionContent>
                          </AccordionItem>
                        );
                      }
                      // 判断是否为外部链接
                      if (item.target === '_blank' || item.url?.startsWith('http') || item.url?.startsWith('mailto:') || item.url?.startsWith('tel:')) {
                        return (
                          <a
                            key={i}
                            href={item.url}
                            target={item.target}
                            className="font-semibold my-4 flex items-center gap-2"
                          >
                            {item.title}
                            {item.icon && (
                              <Icon
                                name={item.icon}
                                className="size-4 shrink-0"
                              />
                            )}
                          </a>
                        );
                      }

                      // 内部链接使用国际化Link组件
                      return (
                        <Link
                          key={i}
                          href={item.url || ""}
                          className="font-semibold my-4 flex items-center gap-2"
                        >
                          {item.title}
                          {item.icon && (
                            <Icon
                              name={item.icon}
                              className="size-4 shrink-0"
                            />
                          )}
                        </Link>
                      );
                    })}
                  </Accordion>
                </div>
                <div className="flex-1"></div>
                <div className="border-t pt-4">
                  <div className="mt-2 flex flex-col gap-3">
                    {header.buttons?.map((item, i) => {
                      return (
                        <Button key={i} variant={item.variant}>
                          <Link
                            href={item.url || ""}
                            target={item.target || ""}
                            className="flex items-center gap-1"
                          >
                            {item.title}
                            {item.icon && (
                              <Icon
                                name={item.icon}
                                className="size-4 shrink-0"
                              />
                            )}
                          </Link>
                        </Button>
                      );
                    })}

                    {header.show_sign && <SignToggle />}
                  </div>

                  <div className="mt-4 flex items-center gap-2">
                    {header.show_locale && <LocaleToggle />}
                    <div className="flex-1"></div>

                    {header.show_theme && <ThemeToggle />}
                  </div>
                </div>
              </SheetContent>
              </Sheet>
            </div>
          </div>
        </div>
        </div>
      </section>
    </>
  );
}
