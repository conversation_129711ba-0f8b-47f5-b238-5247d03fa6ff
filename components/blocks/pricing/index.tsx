"use client";

import { <PERSON>, Loader, X, Clock } from "lucide-react";
import { PricingItem, Pricing as PricingType } from "@/types/blocks/pricing";
import { ActivityBanner as ActivityBannerType } from "@/types/blocks/header";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useEffect, useState } from "react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import Icon from "@/components/icon";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { useAppContext } from "@/contexts/app";
import { useTranslations } from "next-intl";

interface TimeLeft {
  hours: number;
  minutes: number;
  seconds: number;
}

// 生成唯一的存储键，基于banner配置
const getStorageKey = (banner: ActivityBannerType) => {
  const configString = JSON.stringify({
    countdown_hours: banner.countdown_hours,
    main_text: banner.main_text,
    countdown_text: banner.countdown_text
  });

  // 简单的字符串哈希函数
  let hash = 0;
  for (let i = 0; i < configString.length; i++) {
    const char = configString.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }

  return `activity_banner_${Math.abs(hash).toString(36)}`;
};

export default function Pricing({
  pricing,
  activityBanner
}: {
  pricing: PricingType;
  activityBanner?: ActivityBannerType;
}) {
  if (pricing.disabled) {
    return null;
  }

  const { user, setShowSignModal } = useAppContext();
  const t = useTranslations('pricing');

  // 默认选择年付
  const [group, setGroup] = useState("yearly");
  const [isLoading, setIsLoading] = useState(false);
  const [loadingText, setLoadingText] = useState<string>("Processing...");
  const [productId, setProductId] = useState<string | null>(null);
  const [timeLeft, setTimeLeft] = useState<TimeLeft>({ hours: 0, minutes: 0, seconds: 0 });

  const handleCheckout = async (item: PricingItem) => {
    try {
      if (!user) {
        setShowSignModal(true);
        return;
      }

      const params = {
        product_id: item.product_id,
        product_name: item.product_name,
        credits: item.credits,
        interval: item.interval,
        amount: item.amount,
        currency: item.currency,
        valid_months: item.valid_months,
      };

      setIsLoading(true);
      setLoadingText("Processing...");
      setProductId(item.product_id);

      // 判断是否为订阅类型（有interval且不为空）
      const isSubscription = item.interval && (item.interval === 'month' || item.interval === 'year');

      // 根据类型选择不同的API
      const apiEndpoint = isSubscription ? "/api/paypal-subscription" : "/api/checkout-paypal";

      const response = await fetch(apiEndpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(params),
      });

      if (response.status === 401) {
        setIsLoading(false);
        setLoadingText("Processing...");
        setProductId(null);

        setShowSignModal(true);
        return;
      }

      const result = await response.json();

      const { code, message, data } = result;
      if (code !== 0) {
        toast.error(message);
        return;
      }

      // PayPal支付处理
      const { approvalUrl } = data;
      if (approvalUrl) {
        // 更新loading文本，显示正在跳转
        setLoadingText("Redirecting to PayPal...");

        // 添加一个短暂延迟，确保用户能看到loading状态变化
        setTimeout(() => {
          window.location.href = approvalUrl;
        }, 500);

        // 不在这里重置loading状态，让用户看到持续的loading直到页面跳转
        return;
      } else {
        toast.error(t('checkout_failed_missing_url'));
        setIsLoading(false);
        setLoadingText("Processing...");
        setProductId(null);
      }
    } catch (e) {
      console.log("checkout failed: ", e);
      toast.error(t('checkout_failed'));
      setIsLoading(false);
      setLoadingText("Processing...");
      setProductId(null);
    }
  };

  // 初始化倒计时
  useEffect(() => {
    if (!activityBanner?.countdown_hours || activityBanner.countdown_hours <= 0) return;

    const storageKey = getStorageKey(activityBanner);
    let endTime: number;

    // 尝试从localStorage获取已存储的结束时间
    try {
      const storedEndTime = localStorage.getItem(storageKey);
      if (storedEndTime) {
        endTime = parseInt(storedEndTime, 10);
        // 检查存储的结束时间是否已过期
        if (endTime <= new Date().getTime()) {
          // 已过期，移除存储并重新计算
          localStorage.removeItem(storageKey);
          endTime = new Date().getTime() + (activityBanner.countdown_hours * 60 * 60 * 1000);
          localStorage.setItem(storageKey, endTime.toString());
        }
      } else {
        // 没有存储的结束时间，创建新的
        endTime = new Date().getTime() + (activityBanner.countdown_hours * 60 * 60 * 1000);
        localStorage.setItem(storageKey, endTime.toString());
      }
    } catch (error) {
      // localStorage不可用时的降级处理
      console.warn('localStorage not available, countdown will reset on page refresh');
      endTime = new Date().getTime() + (activityBanner.countdown_hours * 60 * 60 * 1000);
    }

    const calculateTimeLeft = () => {
      const now = new Date().getTime();
      const difference = endTime - now;

      if (difference > 0) {
        const hours = Math.floor(difference / (1000 * 60 * 60));
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        setTimeLeft({ hours, minutes, seconds });
      } else {
        // 倒计时结束
        setTimeLeft({ hours: 0, minutes: 0, seconds: 0 });
        try {
          localStorage.removeItem(storageKey);
        } catch (error) {
          console.warn('Failed to remove expired countdown from localStorage');
        }
      }
    };

    // 立即计算一次
    calculateTimeLeft();

    // 每秒更新
    const timer = setInterval(calculateTimeLeft, 1000);

    return () => clearInterval(timer);
  }, [activityBanner?.countdown_hours, activityBanner?.main_text, activityBanner?.countdown_text]);

  useEffect(() => {
    if (pricing.items) {
      // 查找年付的Pro套餐作为默认选中的产品
      const yearlyProItem = pricing.items.find(item =>
        item.group === "yearly" && (
          item.title?.toLowerCase().includes('pro') ||
          item.title?.toLowerCase().includes('pro版')
        )
      );

      if (yearlyProItem) {
        setProductId(yearlyProItem.product_id);
      } else {
        // 如果没有找到年付Pro套餐，选择第一个年付套餐
        const firstYearlyItem = pricing.items.find(item => item.group === "yearly");
        if (firstYearlyItem) {
          setProductId(firstYearlyItem.product_id);
        }
      }

      setIsLoading(false);
    }
  }, [pricing.items]);

  return (
    <section id={pricing.name} className="py-12 bg-gradient-to-br from-sky-50 via-white to-sky-100">
      <div className="container max-w-7xl mx-auto px-4">
        <div className="mx-auto mb-12 text-center">
          <h2 className="mb-6 text-4xl font-bold text-gray-900 lg:text-5xl">
            {t('simple_transparent_pricing')}
          </h2>

          {/* 限时优惠倒计时 - 使用activity配置 */}
          {activityBanner?.show && activityBanner.main_text && (
            <div className="mb-8 flex items-center justify-center">
              <div
                className="px-4 py-2 rounded-full text-sm font-medium flex items-center gap-2"
                style={{
                  background: activityBanner.background_color || "linear-gradient(135deg, #0ea5e9 0%, #0284c7 50%, #0369a1 100%)",
                  color: activityBanner.text_color || "#ffffff"
                }}
              >
                <Clock className="size-4 shrink-0" />
                <span>{activityBanner.main_text}</span>
                {activityBanner.countdown_hours && activityBanner.countdown_hours > 0 && (
                  <div className="flex items-center gap-1 ml-2">
                    <div className="flex gap-1">
                      <span className="bg-white/20 backdrop-blur-sm px-2 py-1 rounded text-xs font-mono font-bold border border-white/30">
                        {timeLeft.hours.toString().padStart(2, '0')}
                      </span>
                      <span className="text-white/80">:</span>
                      <span className="bg-white/20 backdrop-blur-sm px-2 py-1 rounded text-xs font-mono font-bold border border-white/30">
                        {timeLeft.minutes.toString().padStart(2, '0')}
                      </span>
                      <span className="text-white/80">:</span>
                      <span className="bg-white/20 backdrop-blur-sm px-2 py-1 rounded text-xs font-mono font-bold border border-white/30">
                        {timeLeft.seconds.toString().padStart(2, '0')}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
        <div className="flex flex-col items-center gap-16">
          <div className="flex flex-col items-center gap-4">
            {/* 月度/年度切换按钮 */}
            {pricing.groups && pricing.groups.filter(item => item.name !== "one-time").length > 0 && (
              <div className="flex h-12 items-center rounded-full bg-white shadow-sm border border-gray-200 p-1 text-sm font-medium">
                <RadioGroup
                  value={group === "one-time" ? "" : group}
                  className="h-full grid grid-cols-2 gap-1"
                  onValueChange={(value) => {
                    setGroup(value);
                  }}
                >
                  {pricing.groups.filter(item => item.name !== "one-time").map((item, i) => {
                    const isYearly = item.name === "yearly";
                    return (
                      <div
                        key={i}
                        className='h-full rounded-full transition-all duration-200 has-[button[data-state="checked"]]:bg-gradient-to-r has-[button[data-state="checked"]]:from-sky-500 has-[button[data-state="checked"]]:to-sky-600 has-[button[data-state="checked"]]:shadow-sm'
                      >
                        <RadioGroupItem
                          value={item.name || ""}
                          id={item.name}
                          className="peer sr-only"
                        />
                        <Label
                          htmlFor={item.name}
                          className="flex h-full cursor-pointer items-center justify-center px-4 font-medium text-gray-600 peer-data-[state=checked]:text-white transition-colors duration-200 hover:text-gray-800 text-sm"
                        >
                          {isYearly ? t('billing_period.yearly') : t('billing_period.monthly')}
                          {item.label && (
                            <Badge
                              variant="outline"
                              className="border-emerald-500 bg-emerald-500 px-1.5 py-0.5 ml-2 text-xs text-white font-medium rounded-full"
                            >
                              {t('save_badge')}
                            </Badge>
                          )}
                        </Label>
                      </div>
                    );
                  })}
                </RadioGroup>
              </div>
            )}

            {/* 一次性购买选项 */}
            <div className="flex justify-center">
              <button
                onClick={() => setGroup("one-time")}
                className="text-gray-600 hover:text-sky-600 hover:underline font-medium text-sm transition-all duration-200"
              >
                {t('one_time_purchase_link')}
              </button>
            </div>
          </div>
          <div className="w-full mx-auto grid gap-6 md:gap-8 md:grid-cols-2 lg:grid-cols-3 max-w-5xl justify-items-center">
            {pricing.items?.map((item, index) => {
              if (item.group && item.group !== group) {
                return null;
              }

              const isSelected = productId === item.product_id;

              return (
                <div
                  key={index}
                  className={`relative rounded-2xl p-4 bg-white border transition-all duration-300 hover:shadow-xl cursor-pointer w-full ${
                    item.is_featured
                      ? "border-sky-300 ring-2 ring-sky-100 shadow-lg bg-gradient-to-br from-sky-50/50 to-sky-100/50"
                      : "border-gray-200 hover:border-gray-300 shadow-sm"
                  } ${isSelected && !item.is_featured ? 'ring-1 ring-gray-300 border-gray-400' : ''}`}
                  onClick={() => setProductId(item.product_id)}
                >
                  {item.is_featured && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <Badge className="bg-gradient-to-r from-sky-500 to-sky-600 text-white px-3 py-1 text-xs font-medium rounded-full shadow-lg">
                        {t('most_popular')}
                      </Badge>
                    </div>
                  )}
                  <div className="flex h-full flex-col gap-2">
                    {/* 标题 */}
                    <div className="text-center">
                      {item.title && (
                        <h3 className="text-2xl font-semibold text-gray-900 mb-1">
                          {item.title}
                        </h3>
                      )}
                    </div>

                    {/* 价格 */}
                    <div className="text-center mb-2">
                      <div className="flex items-baseline justify-center gap-2 mb-1">
                        {item.original_price && (
                          <span className="text-lg text-gray-400 line-through">
                            {item.original_price}
                          </span>
                        )}
                        <span className="text-4xl font-bold text-gray-900">
                          {item.price}
                        </span>
                        <span className="text-base text-gray-600 font-medium">
                          {item.unit}
                        </span>
                      </div>
                      {item.tip && (item.interval === "year" || item.interval === "one-time") && (
                        <p className="text-sm text-sky-600 font-semibold">
                          {item.tip}
                        </p>
                      )}
                    </div>

                    {/* 生成次数和页面数 */}
                    <div className="text-center mb-2">
                      <div className="text-xl font-semibold text-gray-900 mb-0.5">
                        {item.features?.[0]}
                      </div>
                      {/* <div className="text-sm text-gray-500">
                        {item.features?.[1]}
                      </div> */}
                    </div>

                    {/* 订阅按钮 */}
                    <div className="mb-3">
                      {item.button && (
                        <Button
                          className={`w-full h-12 flex items-center justify-center gap-2 font-semibold text-lg transition-all duration-300 rounded-full hover:scale-105 ${
                            item.is_featured
                              ? "bg-gradient-to-r from-sky-500 to-sky-600 hover:from-sky-600 hover:to-sky-700 text-white shadow-lg hover:shadow-xl"
                              : "bg-white hover:bg-gray-50 text-gray-700 border-2 border-gray-300 hover:border-gray-400"
                          }`}
                          disabled={isLoading}
                          onClick={() => {
                            if(isLoading){
                              return;
                            }
                            handleCheckout(item);
                          }}
                        >
                          {isLoading && productId === item.product_id ? (
                            <>
                              <Loader className="h-5 w-5 animate-spin" />
                              <span>{loadingText === "Processing..." ? t('processing') : loadingText}</span>
                            </>
                          ) : (
                            <>
                              <span>{item.button.title}</span>
                              {item.button.icon && (
                                <Icon name={item.button.icon} className="size-5" />
                              )}
                            </>
                          )}
                        </Button>
                      )}
                    </div>

                    {/* 描述 */}
                    {item.description && (
                      <p className="text-gray-600 mb-3 text-sm leading-relaxed text-left px-1 line-clamp-2">
                        {item.description}
                      </p>
                    )}

                    {/* 功能列表 */}
                    {item.features && (
                      <ul className="flex flex-col gap-2 mb-2">
                        {/* 先显示支持的功能 */}
                        {item.features.slice(2).filter(feature => !feature.startsWith('×')).map((feature, fi) => (
                          <li className="flex gap-2 items-center" key={`feature-supported-${fi}`}>
                            <Check className="size-3 shrink-0 text-emerald-500" />
                            <span className="text-base leading-tight whitespace-nowrap text-gray-700">
                              {feature}
                            </span>
                          </li>
                        ))}
                        {/* 再显示不支持的功能 */}
                        {item.features.slice(2).filter(feature => feature.startsWith('×')).map((feature, fi) => (
                          <li className="flex gap-2 items-center" key={`feature-unsupported-${fi}`}>
                            <X className="size-3 shrink-0 text-gray-400" />
                            <span className="text-base leading-tight whitespace-nowrap text-gray-400">
                              {feature.replace(/^×\s*/, '')}
                            </span>
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
}
