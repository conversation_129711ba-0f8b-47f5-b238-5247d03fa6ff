import { Footer as FooterType } from "@/types/blocks/footer";
import Icon from "@/components/icon";
import { Link } from "@/i18n/routing";

export default function Footer({ footer }: { footer: FooterType }) {
  if (footer.disabled) {
    return null;
  }

  return (
    <section id={footer.name} className="py-12 bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 dark:from-gray-900/50 dark:via-blue-900/20 dark:to-purple-900/20 border-t border-gray-200/50 dark:border-gray-700/50">
      <div className="max-w-7xl mx-auto px-4">
        <footer>
          <div className="flex flex-col gap-10 text-center lg:text-left">
            <div className="flex flex-col lg:flex-row lg:justify-between gap-12">
              <div className="flex w-full lg:max-w-md shrink flex-col items-center justify-between gap-8 lg:items-start">
              {footer.brand && (
                <div>
                  <div className="flex items-center justify-center gap-2 lg:justify-start">
                    {footer.brand.logo && (
                      <img
                        src={footer.brand.logo.src}
                        alt={footer.brand.logo.alt || footer.brand.title}
                        className="h-11"
                      />
                    )}
                    {footer.brand.title && (
                      <p className="text-3xl font-semibold">
                        {footer.brand.title}
                      </p>
                    )}
                  </div>
                  {footer.brand.description && (
                    <p className="mt-6 text-md text-muted-foreground">
                      {footer.brand.description}
                    </p>
                  )}
                </div>
              )}
              {footer.social && (
                <ul className="flex items-center space-x-6 text-muted-foreground">
                  {footer.social.items?.map((item, i) => (
                    <li key={i} className="font-medium hover:text-primary">
                      <a href={item.url} target={item.target}>
                        {item.icon && (
                          <Icon name={item.icon} className="size-4" />
                        )}
                      </a>
                    </li>
                  ))}
                </ul>
              )}
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-12 flex-1">
                {footer.nav?.items?.map((item, i) => (
                  <div key={i} className="space-y-6">
                    <p className="mb-6 font-bold text-lg">{item.title}</p>
                    <ul className="space-y-5 text-sm text-muted-foreground">
                      {item.children?.map((iitem, ii) => (
                        <li key={ii} className="font-medium hover:text-primary transition-colors">
                          {/* 判断是否为外部链接 */}
                          {iitem.target === '_blank' || iitem.url?.startsWith('http') || iitem.url?.startsWith('mailto:') || iitem.url?.startsWith('tel:') ? (
                            <a href={iitem.url} target={iitem.target}>
                              {iitem.title}
                            </a>
                          ) : (
                            <Link href={iitem.url || ""}>
                              {iitem.title}
                            </Link>
                          )}
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </div>
          </div>
          <div className="mt-8 flex flex-col justify-between gap-4 border-t pt-8 text-center text-sm font-medium text-muted-foreground lg:flex-row lg:items-center lg:text-left">
            {footer.copyright && (
              <p>
                {footer.copyright}
              </p>
            )}

            {footer.agreement && (
              <ul className="flex justify-center gap-4 lg:justify-start">
                {footer.agreement.items?.map((item, i) => (
                  <li key={i} className="hover:text-primary">
                    {/* 判断是否为外部链接 */}
                    {item.target === '_blank' || item.url?.startsWith('http') || item.url?.startsWith('mailto:') || item.url?.startsWith('tel:') ? (
                      <a href={item.url} target={item.target}>
                        {item.title}
                      </a>
                    ) : (
                      <Link href={item.url || ""}>
                        {item.title}
                      </Link>
                    )}
                  </li>
                ))}
              </ul>
            )}
          </div>
        </footer>
      </div>
    </section>
  );
}
