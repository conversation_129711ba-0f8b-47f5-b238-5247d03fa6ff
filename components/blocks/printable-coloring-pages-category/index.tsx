"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import OptimizedImage from "@/components/ui/optimized-image";
import { ColoringCategory } from "@/types/coloring-category";
import { ChevronRight } from "lucide-react";

// 临时的简单AspectRatio组件
const SimpleAspectRatio = ({ ratio, children, className }: { ratio: number; children: React.ReactNode; className?: string }) => (
  <div className={`relative overflow-hidden ${className || ''}`} style={{ aspectRatio: ratio }}>
    {children}
  </div>
);

interface PrintableColoringPagesCategoryProps {
  category: ColoringCategory;
  coloringPages: any[];
  totalPages: number;
  currentPage: number;
}

export default function PrintableColoringPagesCategory({
  category,
  coloringPages: initialPages,
  totalPages: initialTotalPages,
  currentPage: initialCurrentPage,
}: PrintableColoringPagesCategoryProps) {
  const [coloringPages, setColoringPages] = useState(initialPages);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(initialCurrentPage);
  const [totalPages, setTotalPages] = useState(initialTotalPages);

  const itemsPerPage = 20;

  const handlePageChange = async (page: number) => {
    if (page === currentPage || page < 1 || page > totalPages) return;

    setCurrentPage(page);
    await fetchColoringPages(page);

    // 滚动到页面顶部
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const fetchColoringPages = async (page: number) => {
    setLoading(true);
    try {
      const response = await fetch(
        `/api/coloring-pages?category=${category.id}&page=${page}&limit=${itemsPerPage}`
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        setColoringPages(data.pages);
        setTotalPages(data.total_pages);
      } else {
        throw new Error(data.message || "Failed to fetch coloring pages");
      }
    } catch (error) {
      console.error("Error fetching coloring pages:", error);
      setColoringPages([]);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (currentPage !== initialCurrentPage) {
      fetchColoringPages(currentPage);
    }
  }, [currentPage, category.id, initialCurrentPage]);

  return (
    <main className="min-h-screen bg-background">
      {/* Breadcrumb Navigation */}
      <div className="bg-muted/30 py-3">
        <div className="container mx-auto px-4">
          <nav className="flex items-center space-x-2 text-sm" aria-label="Breadcrumb">
            <Link href="/printable-coloring-pages" className="text-primary hover:underline font-medium">
              ALL Coloring Pages
            </Link>
            <ChevronRight className="h-4 w-4 text-muted-foreground" />
            <span className="text-foreground font-medium">{category.name} Coloring Page</span>
          </nav>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">

        {/* Page Title Section */}
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4">
            {coloringPages.length} {category.name} Coloring Pages Online For 100% Free (No Limits)
          </h1>
          <div className="max-w-4xl mx-auto">
            <p className="text-lg text-muted-foreground mb-6 leading-relaxed">
              {category.description} We have carefully designed {coloringPages.length} {category.name.toLowerCase()} coloring pages for you!
              For children and fans to color and create.
            </p>
            <p className="text-base text-muted-foreground mb-8">
              Enhance concentration and patience, cultivate creativity and imagination. Exercise children's hand-eye coordination and fine motor skills.
              All can be downloaded for free in PDF and PNG formats.
            </p>
          </div>

          {/* CTA Button */}
          <Button asChild size="lg" className="mb-8">
            <Link href="/text-to-coloring-page">
              Try to Create Own {category.name} Coloring Page
            </Link>
          </Button>

          {/* Featured Image */}
          {coloringPages.length > 0 && (
            <div className="max-w-md mx-auto mb-8">
              <SimpleAspectRatio ratio={1} className="rounded-lg overflow-hidden shadow-lg">
                <OptimizedImage
                  src={coloringPages[0].image_url}
                  alt={`Featured ${category.name.toLowerCase()} coloring page`}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, 400px"
                  quality={85}
                />
              </SimpleAspectRatio>
            </div>
          )}
        </div>

        {/* Coloring Pages Grid */}
        {coloringPages.length > 0 && (
          <section className="max-w-7xl mx-auto px-4 py-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Browse All {category.name} Coloring Pages
              </h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                Discover our complete collection of {coloringPages.length} {category.name.toLowerCase()} coloring pages.
                Click on any image to view details and download for free!
              </p>
            </div>

            {loading ? (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 md:gap-6">
                {Array.from({ length: 20 }).map((_, index) => (
                  <Card key={index} className="overflow-hidden">
                    <CardContent className="p-0">
                      <Skeleton className="w-full aspect-square" />
                      <div className="p-3">
                        <Skeleton className="h-4 w-full mb-2" />
                        <Skeleton className="h-3 w-2/3" />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 md:gap-6">
                {coloringPages.map((page) => (
                  <Card key={page.id} className="group overflow-hidden hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                    <CardContent className="p-0">
                      <Link href={`/${page.seo_slug}`} className="block">
                        <SimpleAspectRatio ratio={1} className="overflow-hidden">
                          <OptimizedImage
                            src={page.thumbnail_url || page.image_url}
                            alt={page.alt_text || page.title}
                            fill
                            className="object-cover group-hover:scale-105 transition-transform duration-300"
                            sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, (max-width: 1024px) 25vw, 20vw"
                            quality={80}
                          />
                        </SimpleAspectRatio>
                        <div className="p-3">
                          <h3 className="font-medium text-sm mb-1 group-hover:text-primary transition-colors overflow-hidden"
                              style={{
                                display: '-webkit-box',
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: 'vertical'
                              }}>
                            {page.title}
                          </h3>
                          <div className="flex items-center justify-between text-xs text-muted-foreground">
                            <span className="capitalize">{page.difficulty_level}</span>
                            {page.is_featured && (
                              <span className="bg-primary/10 text-primary px-2 py-1 rounded-full text-xs font-medium">
                                Featured
                              </span>
                            )}
                          </div>
                        </div>
                      </Link>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center items-center gap-2 mt-12">
                <Button
                  variant="outline"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage <= 1 || loading}
                  className="px-4 py-2"
                >
                  Previous
                </Button>

                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                    const pageNum = currentPage <= 3
                      ? i + 1
                      : currentPage >= totalPages - 2
                        ? totalPages - 4 + i
                        : currentPage - 2 + i;

                    if (pageNum < 1 || pageNum > totalPages) return null;

                    return (
                      <Button
                        key={pageNum}
                        variant={currentPage === pageNum ? "default" : "outline"}
                        onClick={() => handlePageChange(pageNum)}
                        disabled={loading}
                        className="w-10 h-10 p-0"
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                </div>

                <Button
                  variant="outline"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage >= totalPages || loading}
                  className="px-4 py-2"
                >
                  Next
                </Button>
              </div>
            )}
          </section>
        )}

        {/* Content Sections */}
        <div className="space-y-16">
          {/* What is [Category] Section */}
          <section className="max-w-4xl mx-auto">
            <h2 className="text-2xl md:text-3xl font-bold mb-6">
              What is {category.name.toLowerCase()} and how about the {coloringPages.length} coloring pages you provided?
            </h2>
            <div className="prose prose-lg max-w-none text-muted-foreground">
              <p className="mb-4">
                <strong>{category.name}</strong> {category.description?.toLowerCase() || 'coloring pages'}.
                These characters have captured the hearts of fans across all age groups—especially children and teens.
              </p>
              <p className="mb-4">
                <strong>We offer {coloringPages.length} high-quality</strong>, free {category.name.toLowerCase()} coloring pages that are perfect for both printing and digital coloring.
                These black-and-white line art illustrations feature detailed designs and creative scenes.
              </p>
              <p>
                <strong>Coloring these pages</strong> is a fun and engaging activity that helps children develop fine motor skills, color recognition, and creativity.
                Whether you're a parent, teacher, or coloring enthusiast, these {category.name.toLowerCase()} coloring sheets are ideal for kid's activities, classroom resources, or party printables.
              </p>
            </div>

            {/* Featured Image in Content */}
            {coloringPages.length > 1 && (
              <div className="mt-8 max-w-sm mx-auto">
                <SimpleAspectRatio ratio={1} className="rounded-lg overflow-hidden shadow-md">
                  <OptimizedImage
                    src={coloringPages[1].image_url}
                    alt={`${category.name} coloring page example`}
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, 300px"
                    quality={85}
                  />
                </SimpleAspectRatio>
              </div>
            )}
          </section>

          {/* How to Create Section */}
          <section className="max-w-4xl mx-auto">
            <h2 className="text-2xl md:text-3xl font-bold mb-6">
              How to create the funniest {category.name} coloring pages?
            </h2>
            <div className="space-y-6">
              {/* Collapsible FAQ Items */}
              <details className="group border border-border rounded-lg">
                <summary className="flex items-center justify-between p-4 cursor-pointer hover:bg-muted/50 transition-colors">
                  <h3 className="text-lg font-semibold">How to make and color a cool {category.name} coloring page?</h3>
                  <ChevronRight className="h-5 w-5 transition-transform group-open:rotate-90" />
                </summary>
                <div className="p-4 pt-0 text-muted-foreground">
                  <p className="mb-4">
                    If you want to create an amazing {category.name.toLowerCase()} coloring page, it helps to first understand the unique characteristics—both in personality and appearance.
                    {category.name} is one of the most recognizable characters of all time.
                  </p>
                  <p className="mb-4">When coloring your {category.name.toLowerCase()} coloring sheet, here are a few tips to get started:</p>
                  <ul className="list-disc list-inside space-y-2 mb-4">
                    <li>Use appropriate colors that match the character's classic design</li>
                    <li>Pay attention to details and distinctive features</li>
                    <li>Add backgrounds to bring your scene to life</li>
                    <li>Use your imagination and creativity</li>
                  </ul>
                  <p>
                    Whether you're a child or an adult fan, the coloring process is a great way to relax, get creative, and bring your favorite characters to life.
                    Use your imagination and enjoy the fun of adding your own artistic flair to these free printable {category.name.toLowerCase()} coloring pages.
                  </p>
                </div>
              </details>

              <details className="group border border-border rounded-lg">
                <summary className="flex items-center justify-between p-4 cursor-pointer hover:bg-muted/50 transition-colors">
                  <h3 className="text-lg font-semibold">Difficulties faced: What are the difficulties and points to pay attention to in the process of coloring {category.name} coloring pages?</h3>
                  <ChevronRight className="h-5 w-5 transition-transform group-open:rotate-90" />
                </summary>
                <div className="p-4 pt-0 text-muted-foreground">
                  <p className="mb-4">
                    Coloring {category.name.toLowerCase()} can be both fun and rewarding, but to make your artwork stand out, here are a few key things to keep in mind:
                  </p>
                  <div className="space-y-4">
                    <div>
                      <strong>1. Highlight Important Features</strong>
                      <p>Pay attention to the most defining features that make the character recognizable and give them personality.</p>
                    </div>
                    <div>
                      <strong>2. Handle Small or Open Line Areas with Care</strong>
                      <p>Some coloring pages may include tiny or open-lined areas. Use fine-tipped tools for better precision.</p>
                    </div>
                    <div>
                      <strong>3. Balance Character and Background Colors</strong>
                      <p>Use contrasting but complementary colors to make the character pop without clashing with the surroundings.</p>
                    </div>
                  </div>
                  <p className="mt-4">
                    By paying attention to these details, your {category.name.toLowerCase()} coloring pages will look more vivid, professional, and visually appealing.
                    Don't be afraid to experiment—coloring is a great way to express your creativity!
                  </p>
                </div>
              </details>

              <details className="group border border-border rounded-lg">
                <summary className="flex items-center justify-between p-4 cursor-pointer hover:bg-muted/50 transition-colors">
                  <h3 className="text-lg font-semibold">Benefits of Coloring Pages: What are the advantages of making a series of cool {category.name} coloring pages?</h3>
                  <ChevronRight className="h-5 w-5 transition-transform group-open:rotate-90" />
                </summary>
                <div className="p-4 pt-0 text-muted-foreground">
                  <p>
                    Coloring pages offer more than just fun—they help children develop fine motor skills, spark creativity, and enhance their understanding of color combinations.
                    As kids explore different shades and techniques, they not only enjoy the relaxing process but also find a creative outlet to express their imagination and emotions.
                  </p>
                </div>
              </details>
            </div>
          </section>

          {/* FAQ Section */}
          <section className="max-w-4xl mx-auto">
            <div className="bg-muted/30 rounded-lg p-6 mb-8">
              <h2 className="text-xl font-bold mb-4 text-center">Help Center</h2>
              <h3 className="text-2xl font-bold mb-6 text-center">Frequently asked questions</h3>
            </div>

            <div className="space-y-6">
              <div className="border-b border-border pb-6">
                <h4 className="text-lg font-semibold mb-3">What is {category.name.toLowerCase()} coloring page?</h4>
                <p className="text-muted-foreground leading-relaxed">
                  {category.name} coloring pages are black-and-white line drawings featuring {category.name.toLowerCase()} and related scenes, designed for children to color either on paper or digitally.
                  These pages not only offer a fun and engaging activity but also help improve fine motor skills, color recognition, and creativity, making them a popular choice for at-home play, classroom use, and printable art projects.
                </p>
              </div>

              <div className="border-b border-border pb-6">
                <h4 className="text-lg font-semibold mb-3">Can I download unlimited {category.name.toLowerCase()} coloring pages for free?</h4>
                <p className="text-muted-foreground leading-relaxed">
                  Of course, the series of {category.name} coloring pages we prepared are all free to download without any restrictions.
                </p>
              </div>

              <div className="border-b border-border pb-6">
                <h4 className="text-lg font-semibold mb-3">Can I use the {category.name.toLowerCase()} coloring pages I create for commercial activities?</h4>
                <p className="text-muted-foreground leading-relaxed">
                  Of course, we are a commercial application tool based on open source technology, and the generated {category.name} coloring pages can be used for commercial purposes.
                </p>
              </div>

              <div className="pb-6">
                <h4 className="text-lg font-semibold mb-3">Can I download it directly as a PDF file?</h4>
                <p className="text-muted-foreground leading-relaxed">
                  You can download image files directly. If you need to download PDF files, you can directly use our image to PDF converter to convert images into printable PDF files in one second.
                </p>
              </div>
            </div>
          </section>
        </div>
      </div>
    </main>
  );
}
