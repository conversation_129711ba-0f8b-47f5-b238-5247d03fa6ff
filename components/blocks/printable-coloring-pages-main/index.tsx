import Link from "next/link";
import { Card, CardContent } from "@/components/ui/card";
import OptimizedImage from "@/components/ui/optimized-image";
import { ColoringCategory } from "@/types/coloring-category";
import { Button } from "@/components/ui/button";

// 临时的简单AspectRatio组件
const SimpleAspectRatio = ({ ratio, children, className }: { ratio: number; children: React.ReactNode; className?: string }) => (
  <div className={`relative overflow-hidden ${className || ''}`} style={{ aspectRatio: ratio }}>
    {children}
  </div>
);

interface PrintableColoringPagesMainProps {
  categories: ColoringCategory[];
  featuredPages: any[];
}

export default function PrintableColoringPagesMain({
  categories,
  featuredPages
}: PrintableColoringPagesMainProps) {
  return (
    <main className="container mx-auto px-4 py-8">
      {/* Hero Section */}
      <header className="text-center mb-12">
        <h1 className="text-4xl md:text-5xl font-bold mb-4">
          1000+ Free Printable Coloring Pages Online
        </h1>
        <p className="text-lg text-muted-foreground max-w-3xl mx-auto mb-8">
          Download and print free coloring pages, color them in, and bring your imagination to life.
          Fun for kids, helping boost cognitive skills and hand-eye coordination.
        </p>
        <p className="text-sm text-muted-foreground mb-8">
          100% free, no watermark, no login required.
        </p>
        <Button asChild size="lg">
          <Link href="#categories">
            Browse Coloring Pages &gt;&gt;
          </Link>
        </Button>
      </header>

      {/* Featured Categories Grid */}
      <section id="categories" className="mb-16">
        <h2 className="text-2xl font-bold mb-8 text-center">Popular Coloring Page Categories</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
          {categories.slice(0, 10).map((category) => (
            <Card key={category.id} className="overflow-hidden group hover:shadow-lg transition-all duration-300">
              <Link href={`/printable-coloring-pages/${category.seo_slug.replace(/^\//, '')}`}>
                <SimpleAspectRatio ratio={1}>
                  <div className="w-full h-full bg-gradient-to-br from-primary/10 to-primary/20 flex items-center justify-center">
                    <span className="text-4xl">{category.icon}</span>
                  </div>
                </SimpleAspectRatio>
                <CardContent className="p-4 text-center">
                  <h3 className="font-semibold text-sm mb-1 group-hover:text-primary transition-colors">
                    {category.name}
                  </h3>
                  <p className="text-xs text-muted-foreground">
                    {category.count} pages
                  </p>
                </CardContent>
              </Link>
            </Card>
          ))}
        </div>
      </section>



      {/* Featured Pages */}
      {featuredPages.length > 0 && (
        <section className="mb-16">
          <h2 className="text-2xl font-bold mb-8">Featured Coloring Pages</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            {featuredPages.map((page) => (
              <Card key={page.id} className="overflow-hidden group hover:shadow-lg transition-all duration-300">
                <Link href={`/${page.seo_slug}`}>
                  <SimpleAspectRatio ratio={1}>
                    <OptimizedImage
                      src={page.image_url}
                      alt={page.alt_text || page.title}
                      fill
                      className="object-cover transition-transform group-hover:scale-105"
                      sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 20vw"
                      quality={75}
                    />
                  </SimpleAspectRatio>
                  <CardContent className="p-3">
                    <h3 className="font-medium text-sm hover:text-primary transition-colors line-clamp-2">
                      {page.title}
                    </h3>
                  </CardContent>
                </Link>
              </Card>
            ))}
          </div>
        </section>
      )}

      {/* Tools Section */}
      <section className="mb-16">
        <h2 className="text-2xl font-bold mb-8 text-center">
          Want to Customize Your Own Free Printable Coloring Pages?
        </h2>
        <p className="text-center text-muted-foreground mb-8">
          It will be fun to generate coloring pages from photos or text and then color them.
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <Card className="text-center">
            <CardContent className="p-6">
              <div className="mb-4 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg p-8 flex items-center justify-center">
                <div className="text-4xl">📷</div>
              </div>
              <h3 className="text-lg font-semibold mb-2">Photo to Coloring Pages Tool</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Turn your photos into coloring pages online.
              </p>
              <Button asChild>
                <Link href="/image-to-coloring-page">
                  Try Photo to Coloring page
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardContent className="p-6">
              <div className="mb-4 bg-gradient-to-br from-green-100 to-green-200 rounded-lg p-8 flex items-center justify-center">
                <div className="text-4xl">✏️</div>
              </div>
              <h3 className="text-lg font-semibold mb-2">Text to Coloring Pages Tool</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Turn your imagination into coloring pages.
              </p>
              <Button asChild>
                <Link href="/text-to-coloring-page">
                  Try Text to Coloring Page
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardContent className="p-6">
              <div className="mb-4 bg-gradient-to-br from-purple-100 to-purple-200 rounded-lg p-8 flex items-center justify-center">
                <div className="text-4xl">🎨</div>
              </div>
              <h3 className="text-lg font-semibold mb-2">Line Art Colorizer Online</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Color your free coloring pages online with just some click.
              </p>
              <Button asChild variant="outline">
                <Link href="/line-art-colorizer">
                  Try Online Coloring
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="mb-16">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold mb-4">
            Enhance Imagination with MakeColoring's Free, Downloadable & Printable Coloring Pages
          </h2>
          <p className="text-muted-foreground max-w-4xl mx-auto">
            Creativity begins with simple lines, and color brings them to life. MakeColoring offers a coloring page platform 
            for all ages to relax, express creativity, and have fun. Whether you're an art enthusiast or an experienced creator, 
            these coloring pages provide an easy and enjoyable way to enhance artistic skills and explore the magical world of colors.
          </p>
        </div>
        <div className="text-center">
          <Button asChild size="lg">
            <Link href="#categories">
              Choose And Download Free Coloring Pages!
            </Link>
          </Button>
        </div>
      </section>
    </main>
  );
}
