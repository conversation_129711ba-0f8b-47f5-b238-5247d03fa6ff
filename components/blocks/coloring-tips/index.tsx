import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Palette, Lightbulb, Heart, Star } from "lucide-react";
import { getColoringTipsTranslation } from "@/lib/i18n/coloring-tips";

interface ColoringTipsProps {
  coloringPage?: {
    title?: string;
    tags?: string[];
    difficulty_level?: 'easy' | 'medium' | 'hard';
    age_group?: 'toddler' | 'preschool' | 'school' | 'teen' | 'adult' | 'all';
  };
  locale?: string;
}

export default function ColoringTips({ coloringPage, locale = 'en' }: ColoringTipsProps) {
  const t = getColoringTipsTranslation(locale);

  // 获取当前难度和年龄组的技巧
  const currentDifficultyTips = coloringPage?.difficulty_level
    ? t.difficultyTips[coloringPage.difficulty_level]
    : t.difficultyTips.easy;

  const currentAgeGroupTips = coloringPage?.age_group
    ? t.ageGroupTips[coloringPage.age_group]
    : t.ageGroupTips.all;

  // 涂色的益处（带图标）
  const benefits = [
    { icon: <Heart className="w-5 h-5" />, title: t.benefits[0].title, description: t.benefits[0].description },
    { icon: <Star className="w-5 h-5" />, title: t.benefits[1].title, description: t.benefits[1].description },
    { icon: <Palette className="w-5 h-5" />, title: t.benefits[2].title, description: t.benefits[2].description },
    { icon: <Lightbulb className="w-5 h-5" />, title: t.benefits[3].title, description: t.benefits[3].description }
  ];

  return (
    <div className="space-y-6">
      {/* 基础涂色技巧 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="w-5 h-5" />
            {t.coloringTips}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-2">
            {t.basicTips.map((tip, index) => (
              <li key={index} className="flex items-start gap-2">
                <span className="text-primary font-semibold">{index + 1}.</span>
                <span>{tip}</span>
              </li>
            ))}
          </ul>
        </CardContent>
      </Card>

      {/* 难度特定技巧 */}
      {coloringPage?.difficulty_level && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Badge variant="outline">{coloringPage.difficulty_level}</Badge>
              {t.difficultyTipsTitle}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {currentDifficultyTips.map((tip, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-primary">•</span>
                  <span>{tip}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* 年龄组建议 */}
      {coloringPage?.age_group && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Badge variant="secondary">{coloringPage.age_group}</Badge>
              {t.ageGroupTipsTitle}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {currentAgeGroupTips.map((tip, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-primary">•</span>
                  <span>{tip}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* 颜色建议 */}
      <Card>
        <CardHeader>
          <CardTitle>{t.colorSuggestionsTitle}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {t.colorSuggestions.map((suggestion, index) => (
              <div key={index} className="p-3 border rounded-lg">
                <h4 className="font-semibold mb-2">{suggestion.name}</h4>
                <div className="flex flex-wrap gap-1 mb-2">
                  {suggestion.colors.map((color, colorIndex) => (
                    <Badge key={colorIndex} variant="outline" className="text-xs">
                      {color}
                    </Badge>
                  ))}
                </div>
                <p className="text-sm text-muted-foreground">{suggestion.description}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 涂色的益处 */}
      <Card>
        <CardHeader>
          <CardTitle>{t.coloringBenefits}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {benefits.map((benefit, index) => (
              <div key={index} className="flex items-start gap-3">
                <div className="text-primary mt-1">{benefit.icon}</div>
                <div>
                  <h4 className="font-semibold">{benefit.title}</h4>
                  <p className="text-sm text-muted-foreground">{benefit.description}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle>{t.howToUse}</CardTitle>
        </CardHeader>
        <CardContent>
          <ol className="space-y-2">
            {t.howToUseSteps.map((step, index) => (
              <li key={index} className="flex items-start gap-2">
                <span className="text-primary font-semibold">{index + 1}.</span>
                <span>{step}</span>
              </li>
            ))}
          </ol>
        </CardContent>
      </Card>
    </div>
  );
}
