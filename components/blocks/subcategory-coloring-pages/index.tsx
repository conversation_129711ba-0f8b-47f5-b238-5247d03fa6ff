"use client"

import { useState, useEffect } from "react"
import { useTranslations } from "next-intl"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { AspectRatio } from "@/components/ui/aspect-ratio"
import { Download, Eye } from "lucide-react"
import OptimizedImage from "@/components/ui/optimized-image"
import Link from "next/link"
import { ColoringCategory, ColoringSubcategory, ColoringPage } from "@/types/coloring-category"
import { ColoringPagesService } from '@/lib/services/coloring-pages-service'

interface SubcategoryColoringPagesProps {
  category: ColoringCategory
  subcategory: ColoringSubcategory
}

export default function SubcategoryColoringPages({ category, subcategory }: SubcategoryColoringPagesProps) {
  const t = useTranslations("free_coloring_pages")
  const [coloringPages, setColoringPages] = useState<ColoringPage[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // 加载子分类的涂色页面数据
    const loadData = async () => {
      setLoading(true)
      try {
        const result = await ColoringPagesService.getColoringPages(
          {
            categoryId: category.id,
            subcategoryId: subcategory.id
          },
          {
            limit: 20,
            offset: 0,
            orderBy: 'created_at',
            orderDirection: 'DESC'
          }
        )
        setColoringPages(result.pages)
      } catch (error) {
        console.error('Failed to load subcategory pages:', error)
        setColoringPages([])
      }
      setLoading(false)
    }

    loadData()
  }, [category.id, subcategory.id])

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 面包屑导航 */}
      <nav className="flex items-center space-x-2 text-sm text-muted-foreground mb-6">
        <Link href="/free-coloring-pages" className="hover:text-primary">
          Free Coloring Pages
        </Link>
        <span>/</span>
        <Link href={`/${category.seo_slug}`} className="hover:text-primary">
          {category.name}
        </Link>
        <span>/</span>
        <span className="text-foreground">{subcategory.name}</span>
      </nav>

      {/* 页面标题和描述 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">{subcategory.name} Coloring Pages</h1>
        <p className="text-lg text-muted-foreground max-w-3xl">
          {subcategory.description}
        </p>
      </div>

      {/* 涂色页面网格 */}
      {loading ? (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, index) => (
            <Card key={index} className="overflow-hidden">
              <AspectRatio ratio={1}>
                <Skeleton className="w-full h-full" />
              </AspectRatio>
              <CardContent className="p-4">
                <Skeleton className="h-4 w-3/4 mb-2" />
                <Skeleton className="h-3 w-1/2" />
              </CardContent>
            </Card>
          ))}
        </div>
      ) : coloringPages.length > 0 ? (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {coloringPages.map((page) => (
            <Card key={page.id} className="overflow-hidden group hover:shadow-lg transition-all duration-300">
              <AspectRatio ratio={1}>
                <OptimizedImage
                  src={page.image_url}
                  alt={page.title}
                  fill
                  className="object-cover transition-transform group-hover:scale-105"
                  sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                  quality={75}
                  placeholder="blur"
                />
              </AspectRatio>
              <CardContent className="p-4">
                <Link href={`/${page.seo_slug}`}>
                  <h3 className="font-medium text-sm hover:text-primary transition-colors line-clamp-2 mb-2">
                    {page.title}
                  </h3>
                </Link>
                <div className="flex items-center justify-between">
                  <span className="text-xs text-muted-foreground">
                    {page.download_count} downloads
                  </span>
                  <div className="flex space-x-1">
                    <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                      <Eye className="h-3 w-3" />
                    </Button>
                    <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                      <Download className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-muted-foreground">No coloring pages found in this category.</p>
        </div>
      )}

      {/* SEO内容区域 */}
      <div className="mt-16 prose prose-sm max-w-none">
        <h2>About {subcategory.name} Coloring Pages</h2>
        <p>
          Our collection of {subcategory.name.toLowerCase()} coloring pages offers a wonderful way to explore creativity 
          while learning about these fascinating subjects. Each coloring page is carefully designed to provide 
          both entertainment and educational value.
        </p>
        
        <h3>Benefits of {subcategory.name} Coloring Pages</h3>
        <ul>
          <li>Develops fine motor skills and hand-eye coordination</li>
          <li>Encourages creativity and artistic expression</li>
          <li>Provides relaxation and stress relief</li>
          <li>Enhances focus and concentration</li>
          <li>Educational learning about {subcategory.name.toLowerCase()}</li>
        </ul>

        <h3>How to Use These Coloring Pages</h3>
        <ol>
          <li>Choose your favorite {subcategory.name.toLowerCase()} coloring page from our collection</li>
          <li>Click the download button to get the high-quality PDF file</li>
          <li>Print the coloring page on standard A4 or Letter size paper</li>
          <li>Gather your coloring supplies - crayons, colored pencils, or markers</li>
          <li>Start coloring and let your creativity flow!</li>
        </ol>
      </div>
    </div>
  )
}
