"use client";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select";
import { useParams } from "next/navigation";
import { usePathname, useRouter } from "@/i18n/routing";
import { useLocale } from "next-intl";

import { MdLanguage } from "react-icons/md";
import { localeNames } from "@/i18n/locale";

export default function ({ isIcon = false }: { isIcon?: boolean }) {
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();

  const handleSwitchLanguage = (value: string) => {
    if (value !== locale) {
      // 使用next-intl的router.replace方法，传入当前路径和新的locale
      router.replace(pathname, { locale: value as any });
    }
  };

  // 如果只有一种语言，直接显示语言名称而不显示下拉选择器
  const localeKeys = Object.keys(localeNames);
  if (localeKeys.length <= 1) {
    return (
      <div className="flex items-center gap-x-2 text-muted-foreground">
        <MdLanguage className="text-xl" />
        {!isIcon && (
          <span className="hidden md:block">{localeNames[locale]}</span>
        )}
      </div>
    );
  }

  return (
    <Select value={locale} onValueChange={handleSwitchLanguage}>
      <SelectTrigger className="flex items-center gap-x-2 border-none bg-transparent text-muted-foreground outline-none hover:bg-accent hover:text-accent-foreground focus:ring-0 focus:ring-offset-0 transition-colors duration-200">
        <MdLanguage className="text-xl" />
        {!isIcon && (
          <span className="hidden md:block">{localeNames[locale]}</span>
        )}
      </SelectTrigger>
      <SelectContent className="z-50">
        {Object.keys(localeNames).map((key: string) => {
          const name = localeNames[key];
          return (
            <SelectItem className="cursor-pointer" key={key} value={key}>
              {name}
            </SelectItem>
          );
        })}
      </SelectContent>
    </Select>
  );
}
