"use client";

import * as React from "react";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Coins, Loader2 } from "lucide-react";

import { Link } from "@/i18n/routing";
import { User } from "@/types/user";
import { signOut } from "next-auth/react";
import { useTranslations } from "next-intl";
import { useUserCredits } from "@/hooks/use-user-credits";

export default function ({ user }: { user: User }) {
  const t = useTranslations();
  const { credits, isLoading } = useUserCredits();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Avatar className="cursor-pointer">
          <AvatarImage src={user.avatar_url} alt={user.nickname} />
          <AvatarFallback>{user.nickname}</AvatarFallback>
        </Avatar>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="mx-4">
        <DropdownMenuLabel className="text-center truncate">
          {user.nickname}
        </DropdownMenuLabel>
        <DropdownMenuSeparator />

        <DropdownMenuLabel className="text-center truncate">
          {user.email}
        </DropdownMenuLabel>
        <DropdownMenuSeparator />

        {/* Credits Display */}
        <DropdownMenuLabel className="text-center">
          <div className="flex items-center justify-center gap-2">
            <Coins className="h-4 w-4 text-yellow-500" />
            {isLoading ? (
              <div className="flex items-center gap-1">
                <Loader2 className="h-3 w-3 animate-spin" />
                <span className="text-xs">Loading...</span>
              </div>
            ) : (
              <Badge variant="secondary" className="text-xs">
                {credits?.left_credits || 0} Credits
              </Badge>
            )}
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />

        <DropdownMenuItem className="flex justify-center cursor-pointer">
          <Link href="/my-orders">{t("user.my_orders")}</Link>
        </DropdownMenuItem>
        <DropdownMenuSeparator />

        <DropdownMenuItem className="flex justify-center cursor-pointer">
          <Link href="/my-credits">{t("my_credits.title")}</Link>
        </DropdownMenuItem>
        <DropdownMenuSeparator />

        {/* API Keys菜单已隐藏 */}
        {/* <DropdownMenuItem className="flex justify-center cursor-pointer">
          <Link href="/api-keys">{t("api_keys.title")}</Link>
        </DropdownMenuItem>
        <DropdownMenuSeparator /> */}

        <DropdownMenuItem
          className="flex justify-center cursor-pointer"
          onClick={() => signOut()}
        >
          {t("user.sign_out")}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
