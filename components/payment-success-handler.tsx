'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';

/**
 * 支付成功处理组件
 * 处理支付成功后的页面刷新和状态管理
 * 使用智能轮询机制确保用户能看到最新的订单状态
 */
export default function PaymentSuccessHandler() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const t = useTranslations();
  const [isPolling, setIsPolling] = useState(false);
  const [pollCount, setPollCount] = useState(0);
  const [lastOrderCheck, setLastOrderCheck] = useState<string | null>(null);

  useEffect(() => {
    const success = searchParams.get('success');
    const paypalOrderId = searchParams.get('paypal_order_id');

    // 如果是支付成功页面，启动智能轮询
    if (success === 'payment_completed' && paypalOrderId && pollCount < 5) {
      console.log('🔄 检测到支付成功，启动订单状态轮询');

      const pollOrderStatus = async () => {
        try {
          setIsPolling(true);

          const response = await fetch(`/api/order-status?paypal_order_id=${paypalOrderId}`);
          const result = await response.json();

          if (result.code === 0 && result.data) {
            const orderData = result.data;

            // 如果订单已处理且是新的订单（避免重复刷新）
            if (orderData.is_processed && orderData.order_no !== lastOrderCheck) {
              setLastOrderCheck(orderData.order_no);
              router.refresh();
              return; // 停止轮询
            }

            // 如果订单还未处理，继续轮询
            if (!orderData.is_processed && pollCount < 4) {
              setPollCount(prev => prev + 1);
              setTimeout(pollOrderStatus, 3000); // 3秒后再次检查
            }
          }
        } catch (error) {
          // 静默处理错误，避免影响用户体验
        } finally {
          setIsPolling(false);
        }
      };

      // 延迟开始轮询，给API处理时间
      const pollTimer = setTimeout(pollOrderStatus, 2000);
      return () => clearTimeout(pollTimer);
    }
  }, [searchParams, router, pollCount, lastOrderCheck]);

  // 如果正在轮询，显示加载提示
  if (isPolling && pollCount > 0) {
    return (
      <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-center">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
          <p className="text-sm text-blue-800">
            {t('payment_success_handler.syncing_order', { current: pollCount, total: 5 })}
          </p>
        </div>
      </div>
    );
  }

  return null;
}
