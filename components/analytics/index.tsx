"use client";

import { lazy, Suspense } from "react";

// 动态导入分析组件以减少初始bundle大小
const GoogleAnalytics = lazy(() => import("./google-analytics"));
const GoogleAdSense = lazy(() => import("./google-adsense"));
const OpenPanelAnalytics = lazy(() => import("./open-panel"));
const MicrosoftClarity = lazy(() => import("./microsoft-clarity"));

export default function Analytics() {
  if (process.env.NODE_ENV !== "production") {
    return null;
  }

  return (
    <Suspense fallback={null}>
      <GoogleAdSense />
      <OpenPanelAnalytics />
      <GoogleAnalytics />
      <MicrosoftClarity />
    </Suspense>
  );
}
