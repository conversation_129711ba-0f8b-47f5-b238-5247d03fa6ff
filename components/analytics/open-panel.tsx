"use client";

import { OpenPanelComponent } from "@openpanel/nextjs";
import { useEffect, useState } from "react";

export default function OpenPanel() {
  const [shouldLoad, setShouldLoad] = useState(false);

  if (process.env.NODE_ENV !== "production") {
    return null;
  }

  const clientId = process.env.NEXT_PUBLIC_OPENPANEL_CLIENT_ID;
  if (!clientId) {
    return null;
  }

  useEffect(() => {
    // 延迟加载以提高性能
    const timer = setTimeout(() => {
      setShouldLoad(true);
    }, 3000); // 延迟3秒加载

    return () => clearTimeout(timer);
  }, []);

  if (!shouldLoad) {
    return null;
  }

  return (
    <OpenPanelComponent
      clientId={clientId}
      trackScreenViews={true}
      trackAttributes={true}
      trackOutgoingLinks={true}
    />
  );
}
