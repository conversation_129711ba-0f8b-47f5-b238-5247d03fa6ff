"use client";

import Image from "next/image";
import { useState, forwardRef } from "react";
import { cn } from "@/lib/utils";

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  fill?: boolean;
  className?: string;
  priority?: boolean;
  quality?: number;
  sizes?: string;
  placeholder?: "blur" | "empty";
  blurDataURL?: string;
  onLoad?: () => void;
  onError?: () => void;
}

const OptimizedImage = forwardRef<HTMLImageElement, OptimizedImageProps>(
  ({
    src,
    alt,
    width,
    height,
    fill = false,
    className,
    priority = false,
    quality = 80,
    sizes = "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",
    placeholder = "empty",
    blurDataURL,
    onLoad,
    onError,
    ...props
  }, ref) => {
    const [isLoading, setIsLoading] = useState(true);
    const [hasError, setHasError] = useState(false);

    const handleLoad = () => {
      setIsLoading(false);
      onLoad?.();
    };

    const handleError = () => {
      setIsLoading(false);
      setHasError(true);
      onError?.();
    };

    // 生成低质量占位符
    const generateBlurDataURL = (w: number, h: number) => {
      const canvas = document.createElement('canvas');
      canvas.width = w;
      canvas.height = h;
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.fillStyle = '#f3f4f6';
        ctx.fillRect(0, 0, w, h);
      }
      return canvas.toDataURL();
    };

    if (hasError) {
      return (
        <div 
          className={cn(
            "flex items-center justify-center bg-gray-100 text-gray-400",
            className
          )}
          style={{ width, height }}
        >
          <span className="text-sm">图片加载失败</span>
        </div>
      );
    }

    const imageProps = {
      src,
      alt,
      quality,
      priority,
      placeholder,
      blurDataURL: blurDataURL || (width && height ? generateBlurDataURL(width, height) : undefined),
      onLoad: handleLoad,
      onError: handleError,
      className: cn(
        "transition-opacity duration-300",
        isLoading ? "opacity-0" : "opacity-100",
        className
      ),
      ...props,
    };

    if (fill) {
      return (
        <Image
          {...imageProps}
          fill
          sizes={sizes}
        />
      );
    }

    return (
      <Image
        {...imageProps}
        width={width}
        height={height}
        sizes={sizes}
      />
    );
  }
);

OptimizedImage.displayName = "OptimizedImage";

export default OptimizedImage;
