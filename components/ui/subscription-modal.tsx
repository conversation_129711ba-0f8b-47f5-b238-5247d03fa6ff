"use client";

import { Check, Loader, Crown, AlertCircle, X } from "lucide-react";
import { PricingItem, Pricing as PricingType } from "@/types/blocks/pricing";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useEffect, useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { useAppContext } from "@/contexts/app";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { getLandingPage } from "@/services/page";
import { useLocale, useTranslations } from "next-intl";



export type SubscriptionModalType = 'insufficient-credits' | 'premium-feature';

interface SubscriptionModalProps {
  isOpen: boolean;
  onClose: () => void;
  type: SubscriptionModalType;
  currentCredits?: number;
  requiredCredits?: number;
  featureName?: string;
}

export default function SubscriptionModal({
  isOpen,
  onClose,
  type,
  currentCredits = 0,
  requiredCredits = 3,
  featureName = ""
}: SubscriptionModalProps) {
  const { user, setShowSignModal } = useAppContext();
  const locale = useLocale();
  const t = useTranslations('subscription_modal');
  const tPricing = useTranslations('pricing');
  const [group, setGroup] = useState<string>("yearly");
  const [productId, setProductId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [loadingText, setLoadingText] = useState<string>("Processing...");
  const [pricing, setPricing] = useState<PricingType | null>(null);
  const [dataLoading, setDataLoading] = useState<boolean>(true);

  // 获取pricing和activity banner数据
  useEffect(() => {
    const fetchData = async () => {
      try {
        setDataLoading(true);
        const landingPageData = await getLandingPage(locale);
        if (landingPageData.pricing) {
          setPricing(landingPageData.pricing);
        }
      } catch (error) {
        console.error('Failed to fetch landing page data:', error);
      } finally {
        setDataLoading(false);
      }
    };

    if (isOpen) {
      fetchData();
    }
  }, [isOpen, locale]);



  // 设置默认选中的产品
  useEffect(() => {
    if (pricing?.items) {
      const yearlyProItem = pricing.items.find(item =>
        item.group === "yearly" && (
          item.title?.toLowerCase().includes('pro') ||
          item.title?.toLowerCase().includes('pro版')
        )
      );

      if (yearlyProItem) {
        setProductId(yearlyProItem.product_id);
      } else {
        const firstYearlyItem = pricing.items.find(item => item.group === "yearly");
        if (firstYearlyItem) {
          setProductId(firstYearlyItem.product_id);
        }
      }
      setIsLoading(false);
    }
  }, [pricing?.items]);

  const handleCheckout = async (item: PricingItem) => {
    try {
      if (!user) {
        setShowSignModal(true);
        return;
      }

      setIsLoading(true);
      setLoadingText("Processing...");
      setProductId(item.product_id);

      // 判断是否为订阅类型
      const isSubscription = item.interval === 'month' || item.interval === 'year';

      if (isSubscription) {
        // 使用PayPal订阅API
        await handlePayPalSubscription(item);
      } else {
        // 使用原有的支付API
        await handleRegularPayment(item);
      }
    } catch (error) {
      console.error("Checkout error:", error);
      toast.error("Payment failed");
      setIsLoading(false);
      setLoadingText("Processing...");
      setProductId(null);
    }
  };

  const handlePayPalSubscription = async (item: PricingItem) => {
    try {
      const response = await fetch("/api/paypal-subscription", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          product_id: item.product_id,
          product_name: item.product_name,
          credits: item.credits,
          interval: item.interval,
          amount: item.amount,
          currency: item.currency,
          valid_months: item.valid_months,
        }),
      });

      if (response.status === 401) {
        setIsLoading(false);
        setLoadingText("Processing...");
        setProductId(null);
        setShowSignModal(true);
        return;
      }

      const result = await response.json();
      console.log("PayPal Subscription API response:", result);

      const { code, message, data } = result;
      if (code !== 0) {
        console.error("PayPal Subscription API error:", message);
        toast.error(message);
        setIsLoading(false);
        setLoadingText("Processing...");
        setProductId(null);
        return;
      }

      if (data?.approvalUrl) {
        console.log("✅ PayPal订阅链接:", data.approvalUrl);
        // 更新loading文本，显示正在跳转
        setLoadingText("Redirecting to PayPal...");

        // 添加一个短暂延迟，确保用户能看到loading状态变化
        setTimeout(() => {
          window.location.href = data.approvalUrl;
        }, 500);

        // 不在这里重置loading状态，让用户看到持续的loading直到页面跳转
        return;
      } else {
        console.error("❌ PayPal订阅: 缺少approvalUrl");
        toast.error("订阅创建失败: 缺少支付链接");
        setIsLoading(false);
        setLoadingText("Processing...");
        setProductId(null);
      }
    } catch (error) {
      console.error("PayPal Subscription error:", error);
      toast.error("订阅创建失败");
      setIsLoading(false);
      setLoadingText("Processing...");
      setProductId(null);
    }
  };

  const handleRegularPayment = async (item: PricingItem) => {
    try {
      const params = {
        product_id: item.product_id,
        product_name: item.product_name,
        credits: item.credits,
        interval: item.interval,
        amount: item.amount,
        currency: item.currency,
        valid_months: item.valid_months,
      };

      const response = await fetch("/api/checkout-paypal", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(params),
      });

      if (response.status === 401) {
        setIsLoading(false);
        setLoadingText("Processing...");
        setProductId(null);
        setShowSignModal(true);
        return;
      }

      const result = await response.json();

      const { code, message, data } = result;
      if (code !== 0) {
        toast.error(message);
        setIsLoading(false);
        setLoadingText("Processing...");
        setProductId(null);
        return;
      }

      const { approvalUrl } = data;

      // PayPal支付处理
      if (approvalUrl) {
        // 更新loading文本，显示正在跳转
        setLoadingText("Redirecting to PayPal...");

        // 添加一个短暂延迟，确保用户能看到loading状态变化
        setTimeout(() => {
          window.location.href = approvalUrl;
        }, 500);

        // 不在这里重置loading状态，让用户看到持续的loading直到页面跳转
        return;
      } else {
        toast.error("Payment failed: missing approval URL");
        setIsLoading(false);
        setLoadingText("Processing...");
        setProductId(null);
      }
    } catch (error) {
      console.error("Regular payment error:", error);
      toast.error("Payment failed");
      setIsLoading(false);
      setLoadingText("Processing...");
      setProductId(null);
    }
  };

  const getModalTitle = () => {
    switch (type) {
      case 'insufficient-credits':
        return t('insufficient_credits_title');
      case 'premium-feature':
        return t('premium_feature_title');
      default:
        return t('upgrade_subscription_title');
    }
  };

  const getModalDescription = () => {
    switch (type) {
      case 'insufficient-credits':
        return t('insufficient_credits_description', { currentCredits, requiredCredits });
      case 'premium-feature':
        return t('premium_feature_description', { featureName });
      default:
        return t('upgrade_subscription_description');
    }
  };

  if (!pricing?.items || dataLoading) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-md">
          <div className="flex items-center justify-center p-8">
            <Loader className="h-8 w-8 animate-spin text-sky-500" />
            <span className="ml-2 text-gray-600">{t('loading')}</span>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  const currentGroupItems = pricing.items.filter(item => item.group === group);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[85vh] overflow-y-auto">
        <DialogHeader className="pb-4">
          <DialogTitle className="flex items-center gap-2 text-xl">
            {type === 'insufficient-credits' && <AlertCircle className="h-5 w-5 text-orange-500" />}
            {type === 'premium-feature' && <Crown className="h-5 w-5 text-yellow-500" />}
            {getModalTitle()}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* 描述信息 */}
          <div className="text-center">
            <p className="text-gray-600 text-base">{getModalDescription()}</p>
          </div>



          <div className="flex flex-col items-center gap-4">
            {/* 按年/按月切换 */}
            {pricing?.groups && pricing.groups.filter(item => item.name !== "one-time").length > 0 && (
              <div className="flex h-10 items-center rounded-full bg-white shadow-sm border border-gray-200 p-1 text-sm font-medium">
                <RadioGroup
                  value={group === "one-time" ? "" : group}
                  className="h-full grid grid-cols-2 gap-1"
                  onValueChange={(value) => {
                    setGroup(value);
                  }}
                >
                  {pricing.groups.filter(item => item.name !== "one-time").map((item, i) => {
                    const isYearly = item.name === "yearly";
                    return (
                      <div
                        key={i}
                        className='h-full rounded-full transition-all duration-200 has-[button[data-state="checked"]]:bg-gradient-to-r has-[button[data-state="checked"]]:from-sky-500 has-[button[data-state="checked"]]:to-sky-600 has-[button[data-state="checked"]]:shadow-sm'
                      >
                        <RadioGroupItem
                          value={item.name || ""}
                          id={item.name}
                          className="peer sr-only"
                        />
                        <Label
                          htmlFor={item.name}
                          className="flex h-full cursor-pointer items-center justify-center px-4 font-medium text-gray-600 peer-data-[state=checked]:text-white transition-colors duration-200 hover:text-gray-800 text-sm"
                        >
                          {isYearly ? tPricing('billing_period.yearly') : tPricing('billing_period.monthly')}
                          {item.label && (
                            <Badge
                              variant="outline"
                              className="border-emerald-500 bg-emerald-500 px-1.5 py-0.5 ml-2 text-xs text-white font-medium rounded-full"
                            >
                              {tPricing('save_badge')}
                            </Badge>
                          )}
                        </Label>
                      </div>
                    );
                  })}
                </RadioGroup>
              </div>
            )}

            {/* 一次性购买选项切换 */}
            <div className="flex justify-center">
              <button
                onClick={() => setGroup(group === "one-time" ? "yearly" : "one-time")}
                className="text-gray-600 hover:text-sky-600 hover:underline font-medium text-sm transition-all duration-200"
              >
                {group === "one-time" ? t('back_to_subscription_plans') : t('or_make_one_time_purchase')}
              </button>
            </div>
          </div>

          {/* 价格卡片 */}
          <div className="w-full mx-auto grid gap-6 md:gap-8 md:grid-cols-2 lg:grid-cols-3 max-w-5xl justify-items-center">
            {currentGroupItems.map((item, index) => {
              const isSelected = productId === item.product_id;

              return (
                <div
                  key={index}
                  className={`relative rounded-2xl p-4 bg-white border transition-all duration-300 hover:shadow-xl cursor-pointer w-full ${
                    item.is_featured
                      ? "border-sky-300 ring-2 ring-sky-100 shadow-lg bg-gradient-to-br from-sky-50/50 to-sky-100/50"
                      : "border-gray-200 hover:border-gray-300 shadow-sm"
                  } ${isSelected && !item.is_featured ? 'ring-1 ring-gray-300 border-gray-400' : ''}`}
                  onClick={() => setProductId(item.product_id)}
                >
                  {item.is_featured && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <Badge className="bg-gradient-to-r from-sky-500 to-sky-600 text-white px-3 py-1 text-xs font-medium rounded-full shadow-lg">
                        {t('most_popular')}
                      </Badge>
                    </div>
                  )}
                  <div className="flex h-full flex-col gap-2">
                    {/* 标题 */}
                    <div className="text-center">
                      {item.title && (
                        <h3 className="text-2xl font-semibold text-gray-900 mb-1">
                          {item.title}
                        </h3>
                      )}
                    </div>

                    {/* 价格 */}
                    <div className="text-center mb-2">
                      <div className="flex items-baseline justify-center gap-2 mb-1">
                        {item.original_price && (
                          <span className="text-lg text-gray-400 line-through">
                            {item.original_price}
                          </span>
                        )}
                        <span className="text-4xl font-bold text-gray-900">
                          {item.price}
                        </span>
                        <span className="text-base text-gray-600 font-medium">
                          {item.unit}
                        </span>
                      </div>
                      {item.tip && (item.interval === "year" || item.interval === "one-time") && (
                        <p className="text-sm text-sky-600 font-semibold">
                          {item.tip}
                        </p>
                      )}
                    </div>

                    {/* 生成次数和页面数 */}
                    <div className="text-center mb-2">
                      <div className="text-xl font-semibold text-gray-900 mb-0.5">
                        {item.features?.[0]}
                      </div>
                    </div>

                    {/* 订阅按钮 */}
                    <div className="mb-3">
                      {item.button && (
                        <Button
                          className={`w-full h-12 flex items-center justify-center gap-2 font-semibold text-lg transition-all duration-300 rounded-full hover:scale-105 ${
                            item.is_featured
                              ? "bg-gradient-to-r from-sky-500 to-sky-600 hover:from-sky-600 hover:to-sky-700 text-white shadow-lg hover:shadow-xl"
                              : "bg-white hover:bg-gray-50 text-gray-700 border-2 border-gray-300 hover:border-gray-400"
                          }`}
                          disabled={isLoading}
                          onClick={() => {
                            if (isLoading) {
                              return;
                            }
                            handleCheckout(item);
                          }}
                        >
                          {isLoading && productId === item.product_id ? (
                            <>
                              <Loader className="h-5 w-5 animate-spin" />
                              <span>{loadingText === "Processing..." ? "Processing..." : loadingText}</span>
                            </>
                          ) : (
                            <>
                              <span>{item.button.title}</span>
                            </>
                          )}
                        </Button>
                      )}
                    </div>

                    {/* 描述 */}
                    {item.description && (
                      <p className="text-gray-600 mb-3 text-sm leading-relaxed text-left px-1 line-clamp-2">
                        {item.description}
                      </p>
                    )}

                    {/* 功能列表 */}
                    {item.features && (
                      <ul className="flex flex-col gap-2 mb-2">
                        {/* 先显示支持的功能 */}
                        {item.features.slice(2).filter(feature => !feature.startsWith('×')).map((feature, fi) => (
                          <li className="flex gap-2 items-center" key={`feature-supported-${fi}`}>
                            <Check className="size-3 shrink-0 text-emerald-500" />
                            <span className="text-base leading-tight whitespace-nowrap text-gray-700">
                              {feature}
                            </span>
                          </li>
                        ))}
                        {/* 再显示不支持的功能 */}
                        {item.features.slice(2).filter(feature => feature.startsWith('×')).map((feature, fi) => (
                          <li className="flex gap-2 items-center" key={`feature-unsupported-${fi}`}>
                            <X className="size-3 shrink-0 text-gray-400" />
                            <span className="text-base leading-tight whitespace-nowrap text-gray-400">
                              {feature.replace(/^×\s*/, '')}
                            </span>
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
