"use client";

import * as React from "react";
import { ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { Link } from "@/i18n/routing";

interface DropdownProps {
  trigger: React.ReactNode;
  children: React.ReactNode;
  className?: string;
}

const DropdownContext = React.createContext<{
  closeDropdown: () => void;
}>({
  closeDropdown: () => {},
});

// 全局事件管理器，用于处理多个下拉菜单之间的交互
class DropdownManager {
  private static instance: DropdownManager;
  private dropdowns: Set<() => void> = new Set();

  static getInstance() {
    if (!DropdownManager.instance) {
      DropdownManager.instance = new DropdownManager();
    }
    return DropdownManager.instance;
  }

  register(closeCallback: () => void) {
    this.dropdowns.add(closeCallback);
  }

  unregister(closeCallback: () => void) {
    this.dropdowns.delete(closeCallback);
  }

  closeAll() {
    this.dropdowns.forEach(close => close());
  }

  closeOthers(currentCallback: () => void) {
    this.dropdowns.forEach(close => {
      if (close !== currentCallback) {
        close();
      }
    });
  }
}

export function CustomDropdown({ trigger, children, className }: DropdownProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const dropdownRef = React.useRef<HTMLDivElement>(null);
  const manager = DropdownManager.getInstance();

  const closeDropdown = React.useCallback(() => {
    setIsOpen(false);
  }, []);

  // 注册和注销下拉菜单
  React.useEffect(() => {
    manager.register(closeDropdown);
    return () => {
      manager.unregister(closeDropdown);
    };
  }, [closeDropdown, manager]);

  React.useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    function handleScroll() {
      // 更强的滚动检测，包括所有可能的滚动容器
      setIsOpen(false);
    }

    function handleKeyDown(event: KeyboardEvent) {
      if (event.key === 'Escape') {
        setIsOpen(false);
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      // 使用 passive: false 确保能捕获所有滚动事件
      document.addEventListener("scroll", handleScroll, { capture: true, passive: true });
      window.addEventListener("scroll", handleScroll, { passive: true });
      document.addEventListener("keydown", handleKeyDown);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("scroll", handleScroll, { capture: true });
      window.removeEventListener("scroll", handleScroll);
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [isOpen]);

  const handleToggle = () => {
    if (!isOpen) {
      // 打开当前下拉菜单前，先关闭其他所有下拉菜单
      manager.closeOthers(closeDropdown);
    }
    setIsOpen(!isOpen);
  };

  return (
    <DropdownContext.Provider value={{ closeDropdown }}>
      <div className="relative" ref={dropdownRef}>
        <button
          className={cn(
            "group inline-flex h-10 w-max items-center justify-center rounded-md bg-transparent px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50",
            isOpen ? "bg-accent/50" : "",
            className
          )}
          onClick={handleToggle}
          aria-expanded={isOpen}
        >
          {trigger}
          <ChevronDown
            className={cn(
              "relative top-[1px] ml-1 h-3 w-3 transition duration-200",
              isOpen && "rotate-180"
            )}
            aria-hidden="true"
          />
        </button>

        {isOpen && (
          <div className="absolute left-0 top-full z-50 mt-1.5 min-w-[200px] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-lg animate-in fade-in-0 zoom-in-95">
            {children}
          </div>
        )}
      </div>
    </DropdownContext.Provider>
  );
}

// 导出全局关闭所有下拉菜单的函数，供其他组件使用
export function closeAllDropdowns() {
  DropdownManager.getInstance().closeAll();
}

interface DropdownItemProps {
  children: React.ReactNode;
  href?: string;
  target?: string;
  className?: string;
  onClick?: () => void;
}

export function CustomDropdownItem({ children, href, target, className, onClick }: DropdownItemProps) {
  const { closeDropdown } = React.useContext(DropdownContext);

  const handleClick = () => {
    closeDropdown();
    if (onClick) {
      onClick();
    }
  };

  if (href) {
    // 如果是外部链接或有target属性，使用普通的a标签
    if (target || href.startsWith('http') || href.startsWith('mailto:') || href.startsWith('tel:')) {
      return (
        <a
          href={href}
          target={target}
          className={cn(
            "block select-none rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
            className
          )}
          onClick={handleClick}
        >
          {children}
        </a>
      );
    }

    // 内部链接使用Next.js Link组件
    return (
      <Link
        href={href}
        className={cn(
          "block select-none rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
          className
        )}
        onClick={handleClick}
      >
        {children}
      </Link>
    );
  }

  return (
    <div
      className={cn(
        "block select-none rounded-md p-3 leading-none outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground cursor-pointer",
        className
      )}
      onClick={handleClick}
    >
      {children}
    </div>
  );
}
