"use client";

import React, { useState, useRef, useCallback, useEffect } from 'react';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { cn } from '@/lib/utils';

interface BeforeAfterSliderProps {
  beforeImage: {
    src: string;
    alt: string;
    title?: string;
  };
  afterImage: {
    src: string;
    alt: string;
    title?: string;
  };
  className?: string;
  aspectRatio?: string; // 支持自定义宽高比
  showLabels?: boolean; // 是否显示Before/After标签
  initialPosition?: number; // 初始滑块位置 (0-100)
}

const BeforeAfterSlider = ({
  beforeImage,
  afterImage,
  className,
  aspectRatio = "4/3",
  showLabels = true,
  initialPosition = 50
}: BeforeAfterSliderProps) => {
  const t = useTranslations('before_after_slider');
  const [sliderPosition, setSliderPosition] = useState(initialPosition); // 百分比位置
  const [isDragging, setIsDragging] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const updateSliderPosition = useCallback((clientX: number) => {
    if (!containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const x = clientX - rect.left;
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
    setSliderPosition(percentage);
  }, []);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    setIsDragging(true);
    updateSliderPosition(e.clientX);
  }, [updateSliderPosition]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging) return;
    updateSliderPosition(e.clientX);
  }, [isDragging, updateSliderPosition]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    setIsDragging(true);
    updateSliderPosition(e.touches[0].clientX);
  }, [updateSliderPosition]);

  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (!isDragging) return;
    e.preventDefault();
    updateSliderPosition(e.touches[0].clientX);
  }, [isDragging, updateSliderPosition]);

  const handleTouchEnd = useCallback(() => {
    setIsDragging(false);
  }, []);

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.addEventListener('touchmove', handleTouchMove, { passive: false });
      document.addEventListener('touchend', handleTouchEnd);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
    };
  }, [isDragging, handleMouseMove, handleMouseUp, handleTouchMove, handleTouchEnd]);

  return (
    <div
      ref={containerRef}
      className={cn(
        "relative w-full overflow-hidden cursor-ew-resize select-none rounded-lg",
        className
      )}
      style={{
        aspectRatio,
        maxHeight: "100%"
      }}
      onMouseDown={handleMouseDown}
      onTouchStart={handleTouchStart}
    >
      {/* After Image (背景) */}
      <div className="absolute inset-0">
        <Image
          src={afterImage.src}
          alt={afterImage.alt}
          fill
          sizes="(max-width: 768px) 100vw, 50vw"
          className="object-cover"
          priority
        />
      </div>

      {/* Before Image (前景，通过 clip-path 裁剪) */}
      <div
        className="absolute inset-0"
        style={{
          clipPath: `inset(0 ${100 - sliderPosition}% 0 0)`
        }}
      >
        <Image
          src={beforeImage.src}
          alt={beforeImage.alt}
          fill
          sizes="(max-width: 768px) 100vw, 50vw"
          className="object-cover"
          priority
        />
      </div>

      {/* 分割线 */}
      <div
        className="absolute top-0 bottom-0 w-1 bg-white shadow-2xl z-10 pointer-events-none"
        style={{ left: `${sliderPosition}%`, transform: 'translateX(-50%)' }}
      >
        {/* 拖拽手柄 */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-10 h-10 bg-white rounded-full shadow-2xl border-4 border-white pointer-events-auto cursor-ew-resize flex items-center justify-center hover:scale-110 transition-transform duration-200">
          {/* 左右拖拽图标 */}
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" className="text-gray-600">
            <path d="M8 18L4 12L8 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M16 6L20 12L16 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </div>
      </div>

      {/* Before 标签 (只在滑块覆盖时隐藏) */}
      {showLabels && (
        <div
          className={`absolute bottom-4 left-4 bg-black/40 backdrop-blur-sm text-white px-2.5 py-1 rounded-full text-xs font-medium transition-opacity duration-200 ${
            sliderPosition < 15 ? 'opacity-0' : 'opacity-60'
          }`}
        >
          {beforeImage.title || t('before')}
        </div>
      )}

      {/* After 标签 (只在滑块覆盖时隐藏) */}
      {showLabels && (
        <div
          className={`absolute bottom-4 right-4 bg-black/40 backdrop-blur-sm text-white px-2.5 py-1 rounded-full text-xs font-medium transition-opacity duration-200 ${
            sliderPosition > 85 ? 'opacity-0' : 'opacity-60'
          }`}
        >
          {afterImage.title || t('after')}
        </div>
      )}
    </div>
  );
};

export default BeforeAfterSlider;
