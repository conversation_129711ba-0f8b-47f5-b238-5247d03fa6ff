import { BaseProvider, ImageGenerationOptions, ImageGenerationResult } from '../base-provider';
import { getDefaultModel, getModelById, ModelConfig } from '../config';

export interface APIcoreProviderOptions {
  baseURL?: string;
  timeout?: number;
  maxRetries?: number;
}

export class APIcoreProvider implements BaseProvider {
  name = 'apicore';
  private apiKey: string;
  private baseURL: string;
  private timeout: number;
  private maxRetries: number;

  constructor(apiKey: string, options?: APIcoreProviderOptions) {
    this.apiKey = apiKey;
    this.baseURL = options?.baseURL || 'https://api.apicore.ai';
    this.timeout = options?.timeout || 120000; // 增加到2分钟，应对慢响应
    this.maxRetries = options?.maxRetries || 3; // 增加重试次数
  }

  async initialize(): Promise<void> {
    if (!this.apiKey) {
      throw new Error('APICore API key is required');
    }

    // 验证API密钥格式
    if (!this.apiKey.startsWith('sk-')) {
      console.warn('⚠️ APICore API key should start with "sk-"');
    }

    console.log('✅ APICore Provider initialized successfully');
  }

  // 获取默认模型 - 使用统一配置
  getDefaultModel(type: 'text' | 'image' = 'text'): string {
    const model = getDefaultModel('apicore', type);
    return model ? model.id : 'gpt-4o-image-text';
  }

  // 获取模型配置 - 使用统一配置
  getModelConfig(modelId: string): ModelConfig | null {
    return getModelById(modelId);
  }

  async generateImage(options: ImageGenerationOptions): Promise<ImageGenerationResult> {
    const {
      prompt,
      model: modelId = this.getDefaultModel('text'),
      size = '2:3', // 直接使用size参数，默认2:3
      aspectRatio, // 保留aspectRatio作为备用
      n = 1,
      inputImage,
      negativePrompt,
      ...otherOptions
    } = options;

    // 使用统一配置获取模型信息
    const config = this.getModelConfig(modelId);
    if (!config) {
      throw new Error(`Unsupported model: ${modelId}`);
    }

    // 使用config.model字段作为实际的模型名称
    const actualModel = config.model;

    // 固定使用hd质量
    const quality = 'hd';

    // 优先使用size参数，如果没有则使用aspectRatio转换
    const finalSize = size || this.convertAspectRatioToSize(aspectRatio || '1:1');

    // 验证参数
    this.validateGenerationOptions(prompt, n, finalSize);

    try {
      // 判断是文生图还是图生图
      const isImageToImage = !!inputImage;
      const endpoint = isImageToImage ? '/v1/images/edits' : '/v1/images/generations';

      console.log('🎨 APICore API request:', {
        endpoint,
        model: actualModel,
        promptLength: prompt.length,
        isImageToImage,
        size: finalSize,
        quality,
        imageCount: n,
        hasNegativePrompt: !!negativePrompt
      });

      if (isImageToImage) {
        // 图生图模式 - 使用 multipart/form-data
        return await this.generateImageEdit(prompt, inputImage, actualModel, finalSize, n, otherOptions);
      } else {
        // 文生图模式 - 使用 JSON
        return await this.generateImageFromText(prompt, actualModel, finalSize, n, { ...otherOptions, quality });
      }

    } catch (error) {
      console.error('❌ APICore image generation failed:', error);

      // 增强错误信息
      if (error instanceof Error) {
        if (error.message.includes('401')) {
          throw new Error('APICore API key is invalid or expired');
        } else if (error.message.includes('429')) {
          throw new Error('APICore API rate limit exceeded. Please try again later');
        } else if (error.message.includes('400')) {
          throw new Error(`APICore API request error: ${error.message}`);
        }
      }

      throw new Error(`APICore image generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // 转换aspectRatio到APICore支持的size格式 - 根据官方文档使用比例格式
  private convertAspectRatioToSize(aspectRatio: string): string {
    // APICore文档支持的比例格式：1:1, 2:3, 3:2
    const sizeMap: { [key: string]: string } = {
      '1:1': '1:1',       // 正方形
      '2:3': '2:3',       // 竖向
      '3:2': '3:2'        // 横向
    };

    return sizeMap[aspectRatio] || '1:1';
  }

  // 验证生成选项
  private validateGenerationOptions(prompt: string, n: number, size: string): void {
    if (!prompt || prompt.trim().length === 0) {
      throw new Error('Prompt is required and cannot be empty');
    }

    if (prompt.length > 1000) {
      throw new Error('Prompt must be 1000 characters or less');
    }

    if (n < 1 || n > 8) {
      throw new Error('Number of images must be between 1 and 8');
    }

    // APICore支持的比例格式
    const validSizes = ['1:1', '2:3', '3:2'];
    if (!validSizes.includes(size)) {
      throw new Error(`Invalid size: ${size}. Supported sizes: ${validSizes.join(', ')}`);
    }
  }

  private async generateImageFromText(
    prompt: string,
    model: string,
    size: string,
    n: number,
    otherOptions: any
  ): Promise<ImageGenerationResult> {
    // 根据APICore文档构建请求体
    const requestBody: any = {
      prompt,
      model,
      size, // 使用比例格式如"1:1", "2:3", "3:2"
      n: Math.min(n, 8), // APICore限制最多8张图
      response_format: 'url' // 明确指定返回URL格式
    };

    // 过滤掉不支持的参数，添加quality支持
    const supportedOptions = ['user', 'quality'];
    Object.entries(otherOptions).forEach(([key, value]) => {
      if (supportedOptions.includes(key) && value !== undefined && value !== null) {
        requestBody[key] = value;
      }
    });

    console.log('📤 APICore text-to-image request:', requestBody);

    const response = await this.makeAPIRequest('/v1/images/generations', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    let result;
    try {
      result = await response.json();
    } catch (error) {
      throw new Error(`Failed to parse APICore response: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    console.log('📥 APICore text-to-image response:', {
      success: !!result.data,
      imageCount: result.data?.length || 0,
      hasUsage: !!result.usage,
      created: result.created
    });

    if (result.data && result.data.length > 0) {
      return {
        images: result.data.map((item: any) => ({
          base64: '', // APICore返回URL，不是base64
          url: item.url || ''
        })),
        warnings: []
      };
    }

    throw new Error('No images generated by APICore API');
  }

  // 通用API请求方法，支持重试机制
  private async makeAPIRequest(endpoint: string, options: RequestInit): Promise<Response> {
    const url = `${this.baseURL}${endpoint}`;
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        console.log(`🔄 APICore API request attempt ${attempt}/${this.maxRetries}: ${endpoint}`);

        // 为每次请求创建新的options对象，避免body被重复使用
        const requestOptions = this.cloneRequestOptions(options);

        const response = await fetch(url, {
          ...requestOptions,
          signal: AbortSignal.timeout(this.timeout)
        });

        if (!response.ok) {
          let errorText = '';
          try {
            // 克隆响应以避免body被重复读取的问题
            const responseClone = response.clone();
            errorText = await responseClone.text();
          } catch (readError) {
            errorText = `Failed to read error response: ${readError}`;
          }
          const error = new Error(`APICore API error (${response.status}): ${errorText}`);

          // 对于某些错误不重试
          if (response.status === 401 || response.status === 400) {
            throw error;
          }

          lastError = error;
          if (attempt < this.maxRetries) {
            const delay = Math.pow(2, attempt) * 1000; // 指数退避
            console.log(`⏳ Retrying in ${delay}ms...`);
            await new Promise(resolve => setTimeout(resolve, delay));
            continue;
          }
        }

        return response;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        if (attempt < this.maxRetries) {
          const delay = Math.pow(2, attempt) * 1000;
          console.log(`⏳ Request failed, retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError || new Error('All retry attempts failed');
  }

  // 克隆请求选项，确保每次重试都使用新的body
  private cloneRequestOptions(options: RequestInit): RequestInit {
    const cloned: RequestInit = {
      ...options,
      headers: options.headers ? { ...options.headers } : undefined
    };

    // 对于不同类型的body，需要不同的处理方式
    if (options.body) {
      if (typeof options.body === 'string') {
        // 字符串可以直接复用
        cloned.body = options.body;
      } else if (options.body instanceof FormData) {
        // FormData需要重新创建，使用forEach方法避免兼容性问题
        const newFormData = new FormData();
        options.body.forEach((value, key) => {
          newFormData.append(key, value);
        });
        cloned.body = newFormData;
      } else {
        // 其他类型的body（如Blob、ArrayBuffer等）
        cloned.body = options.body;
      }
    }

    return cloned;
  }

  private async generateImageEdit(
    prompt: string,
    inputImage: string,
    model: string,
    size: string,
    n: number,
    otherOptions: any
  ): Promise<ImageGenerationResult> {
    console.log('📤 APICore image-to-image request:', {
      model,
      size,
      imageCount: n,
      promptLength: prompt.length,
      inputImageType: inputImage.startsWith('data:') ? 'base64' : 'url'
    });

    // 创建FormData
    const formData = new FormData();

    // 处理输入图像 - 如果是base64，需要转换为Blob
    let imageBlob: Blob;
    try {
      if (inputImage.startsWith('data:')) {
        // base64格式
        const base64Data = inputImage.split(',')[1];
        const mimeType = inputImage.split(',')[0].split(':')[1].split(';')[0];
        const byteCharacters = atob(base64Data);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        imageBlob = new Blob([byteArray], { type: mimeType || 'image/png' });
      } else if (inputImage.startsWith('http')) {
        // URL格式，需要先下载
        console.log('📥 Downloading input image from URL...');
        const imageResponse = await fetch(inputImage);
        if (!imageResponse.ok) {
          throw new Error(`Failed to download input image: ${imageResponse.status}`);
        }
        imageBlob = await imageResponse.blob();
      } else {
        throw new Error('Unsupported input image format. Must be base64 data URL or HTTP URL');
      }
    } catch (error) {
      throw new Error(`Failed to process input image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // 构建FormData - 根据APICore文档格式
    formData.append('image', imageBlob, 'input.png');
    formData.append('prompt', prompt);
    formData.append('n', Math.min(n, 8).toString()); // APICore限制最多8张图
    formData.append('size', size); // 使用比例格式如"1:1", "2:3", "3:2"
    formData.append('response_format', 'url');

    // 添加模型参数
    if (model) {
      formData.append('model', model);
    }

    // 添加其他支持的参数
    const supportedOptions = ['user', 'mask'];
    Object.entries(otherOptions).forEach(([key, value]) => {
      if (supportedOptions.includes(key) && value !== undefined && value !== null) {
        if (key === 'mask' && typeof value === 'string') {
          // 处理mask图像
          if (value.startsWith('data:')) {
            const maskBase64Data = value.split(',')[1];
            const maskBinaryString = atob(maskBase64Data);
            const maskBytes = new Uint8Array(maskBinaryString.length);
            for (let i = 0; i < maskBinaryString.length; i++) {
              maskBytes[i] = maskBinaryString.charCodeAt(i);
            }
            const maskBlob = new Blob([maskBytes], { type: 'image/png' });
            formData.append('mask', maskBlob, 'mask.png');
          }
        } else {
          formData.append(key, value.toString());
        }
      }
    });

    const response = await this.makeAPIRequest('/v1/images/edits', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        // 不设置Content-Type，让浏览器自动设置multipart/form-data边界
      },
      body: formData,
    });

    let result;
    try {
      result = await response.json();
    } catch (error) {
      throw new Error(`Failed to parse APICore response: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    console.log('📥 APICore image-to-image response:', {
      success: !!result.data,
      imageCount: result.data?.length || 0,
      hasUsage: !!result.usage
    });

    if (result.data && result.data.length > 0) {
      return {
        images: result.data.map((item: any) => ({
          base64: '', // APICore返回URL，不是base64
          url: item.url || ''
        })),
        warnings: []
      };
    }

    throw new Error('No images generated by APICore API');
  }

  // 获取Provider状态信息
  async getStatus(): Promise<{ status: string; message: string; details?: any }> {
    try {
      // 简单的API密钥验证
      if (!this.apiKey || !this.apiKey.startsWith('sk-')) {
        throw new Error('Invalid API key format');
      }

      return {
        status: 'healthy',
        message: 'APICore provider is configured correctly',
        details: {
          baseURL: this.baseURL,
          maxRetries: this.maxRetries,
          timeout: this.timeout,
          hasValidApiKey: !!this.apiKey
        }
      };
    } catch (error) {
      return {
        status: 'error',
        message: `APICore provider error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: {
          baseURL: this.baseURL,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      };
    }
  }

  // 获取支持的功能
  getSupportedFeatures(): string[] {
    return [
      'text-to-image',
      'image-to-image',
      'dalle-compatible',
      'high-quality',
      'batch-generation',
      'image-editing',
      'quality-control',
      'retry-mechanism'
    ];
  }

  // 获取支持的图像尺寸 - 按照APICore文档使用比例格式
  getSupportedSizes(): string[] {
    return ['1:1', '2:3', '3:2'];
  }

  // 获取支持的质量选项
  getSupportedQualities(): string[] {
    return ['standard', 'hd'];
  }



}
