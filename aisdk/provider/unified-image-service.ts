// 统一的图像生成服务
// 简化provider的使用，提供统一的调用接口

import { ProviderFactory } from './index';
import { simpleConfig, simpleProviderSelector, ProviderType } from './simple-config';
import { ImageGenerationOptions, ImageGenerationResult } from './base-provider';

export interface UnifiedImageGenerationOptions {
  prompt: string;
  mode: 'text-to-image' | 'image-to-image';
  size?: string; // 添加size参数支持
  aspectRatio?: string;
  n?: number;
  inputImage?: string;
  negativePrompt?: string;
  preferredProvider?: ProviderType;
  excludeProviders?: ProviderType[];
  [key: string]: any;
}

export interface UnifiedImageGenerationResult extends ImageGenerationResult {
  provider: string;
  model: string;
  cost: number;
}

export class UnifiedImageService {
  private static instance: UnifiedImageService;

  private constructor() {}

  static getInstance(): UnifiedImageService {
    if (!UnifiedImageService.instance) {
      UnifiedImageService.instance = new UnifiedImageService();
    }
    return UnifiedImageService.instance;
  }

  /**
   * 统一的图像生成接口
   * 自动选择最佳的provider并生成图像
   */
  async generateImage(options: UnifiedImageGenerationOptions): Promise<UnifiedImageGenerationResult> {
    const {
      prompt,
      mode,
      size, // 提取size参数
      aspectRatio = '1:1',
      n = 1,
      inputImage,
      negativePrompt,
      preferredProvider,
      excludeProviders,
      ...otherOptions
    } = options;

    // 选择provider
    const selectedProvider = await simpleProviderSelector.selectProvider(mode, {
      preferredProvider,
      excludeProviders
    });



    // 获取API密钥
    const apiKey = this.getProviderApiKey(selectedProvider);
    if (!apiKey) {
      throw new Error(`${selectedProvider} API key not configured`);
    }

    // 获取provider实例（使用默认配置）
    const provider = await ProviderFactory.getProvider(selectedProvider, apiKey);

    // 获取默认模型
    const model = simpleConfig.getDefaultModel(mode, selectedProvider);

    // 构建生成选项 - 移除quality参数
    const { quality: _, ...filteredOtherOptions } = otherOptions;
    const generateOptions: ImageGenerationOptions = {
      prompt,
      model,
      mode, // 传递mode参数
      size, // 传递size参数
      aspectRatio,
      n,
      inputImage,
      negativePrompt,
      ...filteredOtherOptions
    };

    // 开发模式：打印详细的生成参数
    if (process.env.NODE_ENV === 'development') {
      console.log('🔧 [DEV] Unified Image Service - Generation Options:', {
        provider: selectedProvider,
        model,
        mode,
        size,
        aspectRatio,
        n,
        prompt: prompt,
        hasInputImage: !!inputImage,
        negativePrompt,
        otherOptions: filteredOtherOptions
      });
    }

    // 生成图像
    const result = await provider.generateImage(generateOptions);

    // 计算积分消耗
    const cost = this.calculateCost(selectedProvider, n);

    return {
      ...result,
      provider: selectedProvider,
      model,
      cost
    };
  }

  /**
   * 获取provider状态信息
   */
  async getProviderStatus(provider?: ProviderType): Promise<{ [key: string]: any }> {
    const targetProviders = provider ? [provider] : simpleConfig.getAvailableProviders();
    const statusMap: { [key: string]: any } = {};

    for (const providerType of targetProviders) {
      try {
        const apiKey = this.getProviderApiKey(providerType);
        if (!apiKey) {
          statusMap[providerType] = {
            status: 'error',
            message: 'API key not configured'
          };
          continue;
        }

        const providerInstance = await ProviderFactory.getProvider(providerType, apiKey);
        if ('getStatus' in providerInstance && typeof providerInstance.getStatus === 'function') {
          statusMap[providerType] = await (providerInstance as any).getStatus();
        } else {
          statusMap[providerType] = {
            status: 'healthy',
            message: 'Provider configured correctly'
          };
        }
      } catch (error) {
        statusMap[providerType] = {
          status: 'error',
          message: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    }

    return statusMap;
  }

  /**
   * 获取配置摘要
   */
  getConfigSummary(): string {
    return simpleConfig.getConfigSummary();
  }

  /**
   * 获取可用的providers
   */
  getAvailableProviders(): ProviderType[] {
    return simpleConfig.getAvailableProviders();
  }

  /**
   * 获取当前活跃的provider
   */
  getActiveProvider(): ProviderType {
    return simpleConfig.getActiveProvider();
  }

  // 私有辅助方法

  private getProviderApiKey(provider: ProviderType): string | undefined {
    const envVars = {
      'replicate': 'REPLICATE_API_TOKEN',
      'apicore': 'APICORE_API_TOKEN',
      'kie': 'KIE_API_TOKEN'
    };
    return process.env[envVars[provider]];
  }

  // 移除了特定provider的配置方法，使用默认配置

  private calculateCost(_provider: ProviderType, imageCount: number): number {
    // 基础积分消耗
    const baseCost = simpleConfig.getConfig().defaultCredits;
    return baseCost * imageCount;
  }
}

// 导出单例实例
export const unifiedImageService = UnifiedImageService.getInstance();

// 便捷的导出函数
export async function generateImage(options: UnifiedImageGenerationOptions): Promise<UnifiedImageGenerationResult> {
  return unifiedImageService.generateImage(options);
}

export async function getProviderStatus(provider?: ProviderType) {
  return unifiedImageService.getProviderStatus(provider);
}

export function getImageGenerationConfig() {
  return {
    summary: unifiedImageService.getConfigSummary(),
    availableProviders: unifiedImageService.getAvailableProviders(),
    activeProvider: unifiedImageService.getActiveProvider()
  };
}
