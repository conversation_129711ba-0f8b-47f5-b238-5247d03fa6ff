import { BaseProvider, ImageGenerationOptions, ImageGenerationResult } from '../base-provider';
import { getDefaultModel, getModelById } from '../config';
import { insertAsyncTask, AsyncTaskStatus } from '@/models/async-task';
import { getUuid } from '@/lib/hash';
import { getIsoTimestr } from '@/lib/time';
import { newStorage } from '@/lib/storage';
import { generateStorageKey } from '@/lib/file-naming';

export interface KieProviderOptions {
  baseURL?: string;
  timeout?: number;
  callbackUrl?: string;
}

export class KieProvider implements BaseProvider {
  name = 'kie';
  private apiKey: string;
  private baseURL: string;
  private timeout: number;
  private callbackUrl: string;

  constructor(apiKey: string, options?: KieProviderOptions) {
    this.apiKey = apiKey;
    this.baseURL = options?.baseURL || 'https://api.kie.ai';
    this.timeout = options?.timeout || 180000; // 3分钟超时
    // 优先使用配置的callback URL，然后是传入的选项，最后是默认值
    this.callbackUrl = options?.callbackUrl ||
                      process.env.KIE_CALLBACK_URL ||
                      `${process.env.NEXT_PUBLIC_WEB_URL}/api/kie-callback`;
  }

  async initialize(): Promise<void> {
    if (!this.apiKey) {
      throw new Error('Kie.ai API key is required');
    }


  }

  async generateImage(options: ImageGenerationOptions): Promise<ImageGenerationResult> {
    const {
      prompt,
      model = 'gpt-4o-image',
      size = '1:1',
      inputImage,
      mode, // 不设置默认值，使用传入的mode
      n = 1,
      ...otherOptions
    } = options;

    // 确定实际的mode，如果没有传入则根据是否有inputImage来判断
    const actualMode = mode || (inputImage ? 'image-to-image' : 'text-to-image');



    // 验证image-to-image模式的必需参数
    if (actualMode === 'image-to-image' && !inputImage) {
      throw new Error('inputImage is required for image-to-image mode');
    }

    let taskUuid: string | null = null;

    try {
      // 创建异步任务记录
      taskUuid = getUuid();
      const userUuid = otherOptions.userUuid || 'anonymous';

      // 处理inputImage - 如果是base64格式，需要先上传到R2
      let processedInputImageUrl: string | undefined;
      if (inputImage && actualMode === 'image-to-image') {
        processedInputImageUrl = await this.processInputImage(inputImage, taskUuid);
      }

      // 准备API调用参数
      const apiParams = {
        prompt,
        size,
        filesUrl: processedInputImageUrl ? [processedInputImageUrl] : undefined, // filesUrl应该是数组格式
        callBackUrl: `${this.callbackUrl}?taskUuid=${taskUuid}`,
        enableFallback: true,
        uploadCn: false
      };

      // 调用kie.ai API获取task_id
      const result = await this.callKieAPI(actualMode, apiParams);

      // 计算过期时间（24小时后）
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString();

      // 创建异步任务记录（包含从API获取的task_id）
      const asyncTask = {
        uuid: taskUuid,
        user_uuid: userUuid,
        provider: 'kie' as const,
        task_id: result.taskId,
        task_type: actualMode,
        status: AsyncTaskStatus.Generating as any,
        prompt,
        model,
        size,
        input_image: inputImage,
        created_at: getIsoTimestr(),
        updated_at: getIsoTimestr(),
        expires_at: expiresAt
      };

      try {
        await insertAsyncTask(asyncTask);

      } catch (dbError) {
        // 数据库错误，记录但不阻止流程
        // 不抛出异常，允许流程继续
      }

      // 只依赖回调机制，不启动轮询服务

      // 返回异步任务信息
      return {
        images: [], // 异步生成，暂时返回空数组
        warnings: [],
        taskId: taskUuid, // 返回我们的任务UUID
        status: 'generating'
      };

    } catch (error) {
      // 记录错误到数据库（如果任务已创建）
      if (taskUuid) {
        try {
          await this.updateAsyncTaskStatus(taskUuid, {
            status: AsyncTaskStatus.Failed as any,
            error_message: error instanceof Error ? error.message : 'Unknown error',
            completed_at: getIsoTimestr()
          });
        } catch (updateError) {
          // 忽略更新错误
        }
      }

      throw new Error(`Kie.ai generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async callKieAPI(mode: string, params: any) {
    const endpoint = '/api/v1/gpt4o-image/generate';

    const requestBody = mode === 'text-to-image'
      ? {
          prompt: params.prompt,
          size: params.size,
          callBackUrl: params.callBackUrl,
          enableFallback: params.enableFallback,
          uploadCn: params.uploadCn
        }
      : {
          filesUrl: params.filesUrl, // 现在是数组格式
          prompt: params.prompt,
          size: params.size,
          callBackUrl: params.callBackUrl,
          enableFallback: params.enableFallback,
          uploadCn: params.uploadCn
        };



    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify(requestBody),
        signal: AbortSignal.timeout(this.timeout)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Kie.ai API error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const result = await response.json();

      // 处理 Kie.ai API 的响应格式：{ code: 200, msg: "success", data: { taskId: "..." } }
      const isSuccess = result.code === 200 || result.success === true;
      const taskId = result.data?.taskId;

      if (!isSuccess || !taskId) {
        throw new Error(`Kie.ai API error: ${result.msg || result.message || 'Invalid response format'}`);
      }

      return {
        taskId: taskId,
        message: result.msg || result.message,
        fullResponse: result
      };

    } catch (error) {
      throw error;
    }
  }

  private async updateAsyncTaskStatus(taskUuid: string, updates: any) {
    try {
      const { updateAsyncTask } = await import('@/models/async-task');
      await updateAsyncTask(taskUuid, updates);
    } catch (error) {
      // 忽略更新错误
    }
  }

  /**
   * 处理输入图像 - 将base64格式的图像上传到R2并返回URL
   * @param inputImage - base64格式的图像数据或HTTP URL
   * @param taskUuid - 任务UUID，用于生成文件名
   * @returns 可访问的HTTP URL
   */
  private async processInputImage(inputImage: string, taskUuid: string): Promise<string> {
    // 如果已经是HTTP URL，直接返回
    if (inputImage.startsWith('http://') || inputImage.startsWith('https://')) {
      return inputImage;
    }

    // 如果是base64格式，需要上传到R2
    if (inputImage.startsWith('data:')) {
      try {
        const storage = newStorage();

        // 解析base64数据
        const base64Data = inputImage.split(',')[1];
        const mimeType = inputImage.split(',')[0].split(':')[1].split(';')[0];
        const buffer = Buffer.from(base64Data, 'base64');

        // 生成临时文件key，使用tmp前缀
        const key = `tmp/${generateStorageKey('kie_input', taskUuid, 0)}`;

        // 上传到R2
        const uploadResult = await storage.uploadFile({
          body: buffer,
          key,
          contentType: mimeType || 'image/png',
          disposition: 'inline'
        });

        if (!uploadResult.url) {
          throw new Error('Upload succeeded but no URL returned');
        }

        return uploadResult.url;
      } catch (error) {
        throw new Error(`Failed to process input image: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    throw new Error('Unsupported input image format. Must be base64 data URL or HTTP URL');
  }

  // 获取模型配置
  getModelConfig(modelId?: string) {
    const model = modelId ? getModelById(modelId) : null;
    return model || {
      id: 'gpt-4o-image',
      name: 'GPT-4O Image',
      costCredits: 4,
      maxImages: 1
    };
  }

  // 获取默认模型
  getDefaultModel(mode: 'text' | 'image' = 'text') {
    return getDefaultModel('kie', mode) || 'gpt-4o-image';
  }
}
