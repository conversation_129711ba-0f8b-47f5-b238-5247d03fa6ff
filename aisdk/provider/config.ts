// 提供商配置文件
export interface ModelConfig {
  id: string;
  name: string;
  provider: 'replicate' | 'apicore' | 'kie';
  model: string;
  costCredits: number;
  maxImages?: number;
  maxTokens?: number;
  supportedModes: ('text-to-image' | 'image-to-image')[];
  defaultForText?: boolean;
  defaultForImage?: boolean;
  description?: string;
  capabilities?: string[];
}

// Replicate 模型配置
export const REPLICATE_MODELS: ModelConfig[] = [
  {
    id: 'flux-schnell',
    name: 'FLUX Schnell',
    provider: 'replicate',
    model: 'black-forest-labs/flux-schnell',
    costCredits: 3,
    maxImages: 3,
    supportedModes: ['text-to-image'],
    defaultForText: false,
    defaultForImage: false,
    description: '快速生成高质量图像，适合快速原型制作',
    capabilities: ['fast-generation', 'high-quality']
  },
  {
    id: 'flux-dev',
    name: 'FLUX Dev',
    provider: 'replicate',
    model: 'black-forest-labs/flux-dev',
    costCredits: 3, // 降低积分消耗，适合涂色页生成
    maxImages: 1,
    supportedModes: ['text-to-image', 'image-to-image'],
    defaultForText: false,
    defaultForImage: false,
    description: '平衡质量和速度的通用模型，推荐用于涂色页生成',
    capabilities: ['balanced', 'versatile', 'recommended', 'coloring-pages']
  },
  {
    id: 'flux-kontext-dev',
    name: 'FLUX Kontext Dev',
    provider: 'replicate',
    model: 'black-forest-labs/flux-kontext-dev',
    costCredits: 3,
    maxImages: 1,
    supportedModes: ['text-to-image', 'image-to-image'],
    defaultForText: false,
    defaultForImage: false,
    description: '开源版本的图像编辑模型，通过文本指令编辑图像',
    capabilities: ['open-source', 'context-aware', 'image-editing', 'text-guided-editing']
  },
  {
    id: 'flux-kontext-pro',
    name: 'FLUX Kontext Pro',
    provider: 'replicate',
    model: 'black-forest-labs/flux-kontext-pro',
    costCredits: 3,
    maxImages: 1,
    supportedModes: ['text-to-image', 'image-to-image'],
    defaultForText: true,
    defaultForImage: true,
    description: '专业级图像生成，具有更好的上下文理解能力',
    capabilities: ['professional', 'context-aware', 'premium']
  },
  {
    id: 'imagen-4',
    name: 'Imagen 4',
    provider: 'replicate',
    model: 'google/imagen-4',
    costCredits: 3,
    maxImages: 1,
    supportedModes: ['text-to-image'],
    defaultForText: false,
    defaultForImage: false,
    description: 'Google的最新图像生成模型，具有出色的细节表现',
    capabilities: ['google', 'detailed', 'latest']
  }
];



// APICore 模型配置
export const APICORE_MODELS: ModelConfig[] = [
  {
    id: 'gpt-4o-image-text',
    name: 'GPT-4O Image (Text-to-Image)',
    provider: 'apicore',
    model: 'gpt-4o-image',
    costCredits: 3,
    maxImages: 8, // APICore支持最多8张图
    supportedModes: ['text-to-image'],
    defaultForText: true,
    defaultForImage: false,
    description: 'GPT-4O图像生成模型，支持文生图，DALL-E兼容格式',
    capabilities: ['dalle-compatible', 'high-quality', 'batch-generation']
  },
  {
    id: 'gpt-4o-image-edit',
    name: 'GPT-4O Image (Image-to-Image)',
    provider: 'apicore',
    model: 'gpt-4o-image',
    costCredits: 3,
    maxImages: 8, // APICore支持最多8张图
    supportedModes: ['image-to-image'],
    defaultForText: false,
    defaultForImage: true,
    description: 'GPT-4O图像编辑模型，支持图生图和图像编辑',
    capabilities: ['dalle-compatible', 'high-quality', 'image-editing', 'mask-support']
  }
];
// Kie.ai 模型配置
export const KIE_MODELS: ModelConfig[] = [
  {
    id: 'gpt-4o-image-text',
    name: 'GPT-4O Image (Text-to-Image)',
    provider: 'kie',
    model: 'gpt-4o-image',
    costCredits: 4,
    maxImages: 1,
    supportedModes: ['text-to-image'],
    defaultForText: true,
    defaultForImage: false,
    description: 'Kie.ai GPT-4O图像生成模型，支持文生图',
    capabilities: ['async-generation', 'callback-support', 'high-quality']
  },
  {
    id: 'gpt-4o-image-edit',
    name: 'GPT-4O Image (Image-to-Image)',
    provider: 'kie',
    model: 'gpt-4o-image',
    costCredits: 4,
    maxImages: 1,
    supportedModes: ['image-to-image'],
    defaultForText: false,
    defaultForImage: true,
    description: 'Kie.ai GPT-4O图像编辑模型，支持图生图',
    capabilities: ['async-generation', 'callback-support', 'image-editing']
  }
];





// 所有模型配置
export const ALL_MODELS: ModelConfig[] = [
  ...REPLICATE_MODELS,
  ...APICORE_MODELS,
  ...KIE_MODELS
];

// 根据提供商获取模型配置
export function getModelsByProvider(provider: 'replicate' | 'apicore' | 'kie'): ModelConfig[] {
  switch (provider) {
    case 'replicate':
      return REPLICATE_MODELS;
    case 'apicore':
      return APICORE_MODELS;
    case 'kie':
      return KIE_MODELS;
    default:
      return [];
  }
}

// 根据ID获取模型配置
export function getModelById(id: string): ModelConfig | null {
  return ALL_MODELS.find(model => model.id === id) || null;
}

// 根据提供商和模式获取默认模型
export function getDefaultModel(
  provider: 'replicate' | 'apicore' | 'kie',
  mode: 'text' | 'image' = 'text'
): ModelConfig | null {
  const models = getModelsByProvider(provider);
  const defaultKey = mode === 'text' ? 'defaultForText' : 'defaultForImage';

  return models.find(model => model[defaultKey]) || models[0] || null;
}

// 根据模式获取支持的模型
export function getModelsByMode(
  provider: 'replicate' | 'apicore' | 'kie',
  mode: 'text-to-image' | 'image-to-image'
): ModelConfig[] {
  const models = getModelsByProvider(provider);
  return models.filter(model => model.supportedModes.includes(mode));
}

// 验证模型是否支持指定模式
export function validateModelMode(
  modelId: string,
  mode: 'text-to-image' | 'image-to-image'
): boolean {
  const model = getModelById(modelId);
  return model ? model.supportedModes.includes(mode) : false;
}

// 获取模型的积分消耗
export function getModelCost(modelId: string): number {
  const model = getModelById(modelId);
  return model ? model.costCredits : 5; // 默认5积分
}

// 提供商配置
export interface ProviderConfig {
  id: 'replicate' | 'apicore' | 'kie';
  name: string;
  description: string;
  apiKeyEnvVar: string;
  supportedModes: ('text-to-image' | 'image-to-image')[];
  status: 'active' | 'beta' | 'coming-soon';
  features: string[];
}

export const PROVIDER_CONFIGS: ProviderConfig[] = [
  {
    id: 'replicate',
    name: 'Replicate',
    description: '强大的AI模型平台，支持多种图像生成模型',
    apiKeyEnvVar: 'REPLICATE_API_TOKEN',
    supportedModes: ['text-to-image', 'image-to-image'],
    status: 'active',
    features: ['flux-models', 'imagen-4', 'high-quality', 'fast-generation']
  },
  {
    id: 'apicore',
    name: 'APICore',
    description: 'APICore AI服务平台，支持gpt-4o-image模型，DALL-E兼容API',
    apiKeyEnvVar: 'APICORE_API_TOKEN',
    supportedModes: ['text-to-image', 'image-to-image'],
    status: 'active',
    features: [
      'gpt-4o-image',
      'dalle-compatible',
      'high-quality',
      'batch-generation',
      'image-editing',
      'mask-support',
      'quality-control',
      'retry-mechanism'
    ]
  },
  {
    id: 'kie',
    name: 'Kie.ai',
    description: 'Kie.ai AI服务平台，支持GPT-4O图像生成，异步回调机制',
    apiKeyEnvVar: 'KIE_API_TOKEN',
    supportedModes: ['text-to-image', 'image-to-image'],
    status: 'active',
    features: [
      'gpt-4o-image',
      'async-generation',
      'callback-support',
      'high-quality',
      'image-editing'
    ]
  }
];

// 获取提供商配置
export function getProviderConfig(providerId: 'replicate' | 'apicore' | 'kie'): ProviderConfig | null {
  return PROVIDER_CONFIGS.find(config => config.id === providerId) || null;
}

// 获取活跃的提供商
export function getActiveProviders(): ProviderConfig[] {
  return PROVIDER_CONFIGS.filter(config => config.status === 'active');
}

// 获取全局默认模型（优先选择Replicate）
export function getGlobalDefaultModel(mode: 'text-to-image' | 'image-to-image' = 'text-to-image'): ModelConfig | null {
  // 首先尝试获取Replicate的默认模型
  const replicateModels = getModelsByMode('replicate', mode);
  const replicateDefault = replicateModels.find(model =>
    mode === 'text-to-image' ? model.defaultForText : model.defaultForImage
  );
  if (replicateDefault) return replicateDefault;

  // 如果没有Replicate默认模型，返回第一个Replicate模型
  if (replicateModels.length > 0) return replicateModels[0];

  // 如果没有Replicate模型，查找其他提供商的默认模型
  const allModels = ALL_MODELS.filter(model => model.supportedModes.includes(mode));
  const globalDefault = allModels.find(model =>
    mode === 'text-to-image' ? model.defaultForText : model.defaultForImage
  );
  if (globalDefault) return globalDefault;

  // 最后返回第一个支持该模式的模型
  return allModels.length > 0 ? allModels[0] : null;
}

// 获取默认提供商（Replicate）
export function getDefaultProvider(): 'replicate' {
  return 'replicate';
}
