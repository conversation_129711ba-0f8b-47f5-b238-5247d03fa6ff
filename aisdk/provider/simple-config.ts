// 简化的图像生成Provider配置
// 统一管理replicate、apicore、kie三个provider的配置和调用

import { getDefaultModel as getProviderDefaultModel } from './config';

export type ProviderType = 'replicate' | 'apicore' | 'kie';

// 简化的Provider配置接口
export interface SimpleProviderConfig {
  // 当前活跃的Provider
  activeProvider: ProviderType;
  // 可用的Provider列表（有API密钥且启用的）
  availableProviders: ProviderType[];
  // 默认积分消耗
  defaultCredits: number;
}

// 默认配置 - 根据用户偏好默认使用APICore
export const DEFAULT_SIMPLE_CONFIG: SimpleProviderConfig = {
  activeProvider: 'apicore',
  availableProviders: [],
  defaultCredits: 3
};

// 简化的配置管理器
export class SimpleConfigManager {
  private static instance: SimpleConfigManager;
  private config: SimpleProviderConfig;

  private constructor() {
    this.config = this.loadConfig();
  }

  static getInstance(): SimpleConfigManager {
    if (!SimpleConfigManager.instance) {
      SimpleConfigManager.instance = new SimpleConfigManager();
    }
    return SimpleConfigManager.instance;
  }

  // 从环境变量加载配置 - 简化版本
  private loadConfig(): SimpleProviderConfig {
    const config = { ...DEFAULT_SIMPLE_CONFIG };

    // 从环境变量读取活跃Provider，默认使用APICore
    const activeProvider = process.env.ACTIVE_IMAGE_PROVIDER as ProviderType;
    if (activeProvider && ['replicate', 'apicore', 'kie'].includes(activeProvider)) {
      config.activeProvider = activeProvider;
    }

    // 自动检测可用的Provider（只需要配置API密钥即可自动启用）
    const availableProviders: ProviderType[] = [];

    // 检查每个provider是否配置了API密钥
    if (process.env.REPLICATE_API_TOKEN) {
      availableProviders.push('replicate');
    }
    if (process.env.APICORE_API_TOKEN) {
      availableProviders.push('apicore');
    }
    if (process.env.KIE_API_TOKEN) {
      availableProviders.push('kie');
    }

    config.availableProviders = availableProviders;

    // 从环境变量读取默认积分
    if (process.env.DEFAULT_CREDITS_COST) {
      const defaultCredits = parseInt(process.env.DEFAULT_CREDITS_COST);
      if (!isNaN(defaultCredits)) {
        config.defaultCredits = defaultCredits;
      }
    }

    return config;
  }

  // 获取当前配置
  getConfig(): SimpleProviderConfig {
    return this.config;
  }

  // 获取活跃Provider
  getActiveProvider(): ProviderType {
    return this.config.activeProvider;
  }

  // 获取默认模型 - 返回模型ID而不是实际的模型名称
  getDefaultModel(mode: 'text-to-image' | 'image-to-image', provider?: ProviderType): string {
    const targetProvider = provider || this.config.activeProvider;
    const modeType = mode === 'text-to-image' ? 'text' : 'image';
    const model = getProviderDefaultModel(targetProvider, modeType);

    // 返回模型ID（id字段），而不是实际的模型名称
    if (model) {
      return model.id;
    }

    // 如果没有找到模型配置，返回默认值
    return mode === 'text-to-image' ? 'imagen-4' : 'flux-kontext-pro';
  }

  // 检查Provider是否可用（配置了API密钥且启用）
  isProviderAvailable(provider: ProviderType): boolean {
    return this.config.availableProviders.includes(provider);
  }

  // 检查Provider是否配置了API密钥
  isProviderConfigured(provider: ProviderType): boolean {
    const envVars = {
      'replicate': 'REPLICATE_API_TOKEN',
      'apicore': 'APICORE_API_TOKEN',
      'kie': 'KIE_API_TOKEN'
    };
    return !!process.env[envVars[provider]];
  }

  // 获取可用的Provider列表
  getAvailableProviders(): ProviderType[] {
    return this.config.availableProviders;
  }

  // 获取配置摘要
  getConfigSummary(): string {
    const config = this.config;

    // 获取每个provider的默认模型ID和实际模型名称
    const getModelDisplayName = (mode: 'text-to-image' | 'image-to-image', provider: ProviderType): string => {
      const modelId = this.getDefaultModel(mode, provider);
      const model = getProviderDefaultModel(provider, mode === 'text-to-image' ? 'text' : 'image');
      return model ? `${modelId} (${model.model})` : modelId;
    };

    const replicateTextModel = getModelDisplayName('text-to-image', 'replicate');
    const replicateImageModel = getModelDisplayName('image-to-image', 'replicate');
    const apicoreTextModel = getModelDisplayName('text-to-image', 'apicore');
    const apicoreImageModel = getModelDisplayName('image-to-image', 'apicore');

    return `
🔧 AI Image Generation Configuration:
  Active Provider: ${config.activeProvider}
  Available Providers: ${config.availableProviders.join(', ') || 'None'}

  Default Models:
  - Replicate: ${replicateTextModel} / ${replicateImageModel}
  - APICore: ${apicoreTextModel} / ${apicoreImageModel}

  Default Credits: ${config.defaultCredits}
    `.trim();
  }
}

// 简化的Provider选择器
export class SimpleProviderSelector {
  private configManager: SimpleConfigManager;

  constructor() {
    this.configManager = SimpleConfigManager.getInstance();
  }

  // 选择最佳Provider - 简化逻辑，直接使用可用的provider
  async selectProvider(
    _mode: 'text-to-image' | 'image-to-image', // 保留参数但不使用，因为所有provider都支持两种模式
    options?: {
      preferredProvider?: ProviderType;
      excludeProviders?: ProviderType[];
    }
  ): Promise<ProviderType> {

    // 如果指定了首选Provider且可用，使用它
    if (options?.preferredProvider &&
        this.configManager.isProviderAvailable(options.preferredProvider) &&
        !(options.excludeProviders || []).includes(options.preferredProvider)) {
      return options.preferredProvider;
    }

    // 否则使用当前活跃的Provider
    const activeProvider = this.configManager.getActiveProvider();
    if (this.configManager.isProviderAvailable(activeProvider) &&
        !(options?.excludeProviders || []).includes(activeProvider)) {
      return activeProvider;
    }

    // 如果活跃Provider不可用，尝试使用第一个可用的provider
    const availableProviders = this.configManager.getAvailableProviders();
    const filteredProviders = availableProviders.filter(provider =>
      !(options?.excludeProviders || []).includes(provider)
    );

    if (filteredProviders.length > 0) {
      console.warn(`⚠️ Active provider '${activeProvider}' not available, using '${filteredProviders[0]}' instead`);
      return filteredProviders[0];
    }

    // 如果没有可用的provider，抛出错误
    throw new Error(`No available image generation providers. Please check your API keys and configuration.`);
  }
}

// 导出单例实例
export const simpleConfig = SimpleConfigManager.getInstance();
export const simpleProviderSelector = new SimpleProviderSelector();

// 在开发环境中打印配置摘要
if (process.env.NODE_ENV === 'development') {
  console.log(simpleConfig.getConfigSummary());
}
