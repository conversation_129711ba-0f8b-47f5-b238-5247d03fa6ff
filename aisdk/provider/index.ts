import { BaseProvider, ProviderType } from './base-provider';
import { ReplicateProvider } from './replicate';
import { APIcoreProvider } from './apicore';
import { KieProvider } from './kie';
import { getProviderConfig, getModelsByProvider, getDefaultModel, getModelById } from './config';

export class ProviderFactory {
  private static providers: Map<string, BaseProvider> = new Map();

  static async getProvider(type: ProviderType, apiKey: string, options?: any): Promise<BaseProvider> {
    const cacheKey = `${type}_${apiKey.substring(0, 8)}`;

    if (this.providers.has(cacheKey)) {
      return this.providers.get(cacheKey)!;
    }

    let provider: BaseProvider;

    switch (type) {
      case 'replicate':
        provider = new ReplicateProvider(apiKey);
        break;
      case 'apicore':
        provider = new APIcoreProvider(apiKey, options);
        break;
      case 'kie':
        provider = new KieProvider(apiKey, options);
        break;
      default:
        throw new Error(`Unsupported provider type: ${type}`);
    }

    await provider.initialize();
    this.providers.set(cacheKey, provider);
    return provider;
  }

  // 获取提供商配置信息
  static getProviderInfo(type: ProviderType) {
    return getProviderConfig(type);
  }

  // 获取提供商支持的模型
  static getProviderModels(type: ProviderType) {
    return getModelsByProvider(type);
  }

  // 获取提供商的默认模型
  static getProviderDefaultModel(type: ProviderType, mode: 'text' | 'image' = 'text') {
    return getDefaultModel(type, mode);
  }

  // 根据ID获取模型信息
  static getModelInfo(modelId: string) {
    return getModelById(modelId);
  }

  // 清除缓存的提供商实例
  static clearCache() {
    this.providers.clear();
  }

  // 清除特定提供商的缓存
  static clearProviderCache(type: ProviderType) {
    const keysToDelete = Array.from(this.providers.keys()).filter(key => key.startsWith(type));
    keysToDelete.forEach(key => this.providers.delete(key));
  }
}

export * from './video-model/index';
export * from './base-provider';
export * from './replicate';
export * from './apicore';
export * from './kie';

// 从config导出，避免命名冲突
export type { ModelConfig, ProviderConfig } from './config';
export {
  REPLICATE_MODELS as REPLICATE_MODEL_CONFIGS,
  ALL_MODELS,
  PROVIDER_CONFIGS,
  getModelsByProvider,
  getModelById,
  getDefaultModel,
  getModelsByMode,
  validateModelMode,
  getModelCost,
  getProviderConfig,
  getActiveProviders
} from './config';
