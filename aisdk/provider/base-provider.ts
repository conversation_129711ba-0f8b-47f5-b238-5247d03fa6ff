export interface BaseProvider {
  name: string;
  initialize(): Promise<void>;
  generateImage(options: ImageGenerationOptions): Promise<ImageGenerationResult>;
}

export interface ImageGenerationOptions {
  prompt: string;
  model?: string;
  size?: string;
  aspectRatio?: string;
  n?: number;
  style?: string;
  negativePrompt?: string;
  mode?: 'text-to-image' | 'image-to-image'; // 添加mode参数
  inputImage?: string; // 添加inputImage参数
  [key: string]: any;
}

export interface ImageGenerationResult {
  images: Array<{
    base64: string;
    url?: string;
  }>;
  warnings?: Array<{
    type: string;
    message: string;
  }>;
  // 异步任务支持
  taskId?: string;
  status?: 'generating' | 'completed' | 'failed';
}



export type ProviderType = 'replicate' | 'apicore' | 'kie';
