import { BaseProvider, ImageGenerationOptions, ImageGenerationResult } from '../base-provider';
import { experimental_generateImage as generateImage } from "ai";
import { replicate } from "@ai-sdk/replicate";
import { getDefaultModel, getModelById, ModelConfig } from '../config';

export class ReplicateProvider implements BaseProvider {
  name = 'replicate';
  private apiKey: string;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async initialize(): Promise<void> {
    if (!this.apiKey) {
      throw new Error('Replicate API key is required');
    }
  }

  // 获取默认模型 - 使用统一配置
  getDefaultModel(type: 'text' | 'image' = 'text'): string {
    const model = getDefaultModel('replicate', type);
    return model ? model.id : (type === 'image' ? 'flux-kontext-pro' : 'imagen-4');
  }

  // 获取模型配置 - 使用统一配置
  getModelConfig(modelId: string): ModelConfig | null {
    return getModelById(modelId);
  }

  // 验证模型是否支持指定模式 - 使用统一配置
  validateModelMode(modelId: string, mode: 'text-to-image' | 'image-to-image'): boolean {
    const config = this.getModelConfig(modelId);
    return config ? config.supportedModes.includes(mode) : false;
  }

  // 映射比例到Replicate支持的格式
  private mapAspectRatio(aspectRatio: string): string {
    const aspectRatioMap: { [key: string]: string } = {
      '1:1': '1:1',
      '2:3': '2:3',  // 保持原始比例，Replicate应该支持
      '3:2': '3:2',  // 保持原始比例，Replicate应该支持
      '9:16': '9:16',
      '16:9': '16:9',
      '3:4': '3:4',
      '4:3': '4:3'
    };

    console.log(`🔄 Mapping aspect ratio: ${aspectRatio} → ${aspectRatioMap[aspectRatio] || '1:1'}`);
    return aspectRatioMap[aspectRatio] || '1:1';
  }

  async generateImage(options: ImageGenerationOptions): Promise<ImageGenerationResult> {
    const {
      prompt,
      model: modelId = this.getDefaultModel('text'),
      aspectRatio = '1:1',
      n = 1,
      inputImage,
      ...otherOptions
    } = options;

    // 固定使用90质量
    const quality = 90;

    // 映射比例到Replicate支持的格式
    const mappedAspectRatio = this.mapAspectRatio(aspectRatio);

    // 使用统一配置获取模型信息
    const config = this.getModelConfig(modelId);
    if (!config) {
      throw new Error(`Unsupported model: ${modelId}`);
    }

    try {
      // 使用config.model字段作为实际的模型名称
      const imageModel = replicate.image(config.model);

      // 构建 providerOptions
      const replicateOptions: any = {
        output_quality: quality,
        ...otherOptions
      };

      // 对于图生图模式，需要将输入图像添加到 providerOptions 中
      if (inputImage) {
        // 根据不同模型使用不同的参数名称
        if (config.model.includes('flux-kontext')) {
          // FLUX Kontext 模型使用 'input_image' 参数
          replicateOptions.input_image = inputImage;
        } else if (config.model.includes('flux-fill')) {
          // FLUX Fill 模型使用 'image' 参数
          replicateOptions.image = inputImage;
        } else if (config.model.includes('flux-canny')) {
          // FLUX Canny 模型使用 'control_image' 参数
          replicateOptions.control_image = inputImage;
        } else if (config.model.includes('flux-depth')) {
          // FLUX Depth 模型使用 'control_image' 参数
          replicateOptions.control_image = inputImage;
        } else {
          // 默认使用 'input_image' 参数
          replicateOptions.input_image = inputImage;
        }

        const imageParamName = Object.keys(replicateOptions).find(key =>
          key.includes('image') && key !== 'output_quality'
        );
        console.log(`🖼️ Using image-to-image mode with ${config.model}, input image parameter: ${imageParamName}`);
      }

      // 构建生成选项
      const generateOptions: any = {
        model: imageModel as any,
        prompt: prompt,
        n: Math.min(n, config.maxImages || 1), // 使用默认值1防止undefined
        providerOptions: {
          replicate: replicateOptions,
        },
        aspectRatio: mappedAspectRatio as `${number}:${number}`,
      };

      console.log('🔧 Replicate generation options:', {
        model: config.model,
        promptLength: prompt.length,
        aspectRatio: mappedAspectRatio,
        hasInputImage: !!inputImage,
        imageCount: generateOptions.n,
        replicateOptionsKeys: Object.keys(replicateOptions)
      });

      const result = await generateImage(generateOptions);

      return {
        images: result.images.map(img => ({
          base64: img.base64,
          url: undefined // Replicate通常返回base64，url可能不可用
        })),
        warnings: result.warnings?.map(warning => ({
          type: warning.type || 'unknown',
          message: warning.type === 'unsupported-setting'
            ? `Unsupported setting: ${warning.setting}${warning.details ? ` - ${warning.details}` : ''}`
            : 'Unknown warning'
        })) || []
      };
    } catch (error) {
      console.error('❌ Replicate generation error:', {
        model: config.model,
        error: error instanceof Error ? error.message : error,
        hasInputImage: !!inputImage,
        promptLength: prompt.length
      });
      throw new Error(`Replicate image generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }


}
