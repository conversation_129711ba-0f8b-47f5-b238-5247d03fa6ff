export const locales = ["en", "ru", "hi", "vi", "pt", "fr", "de", "es", "it"] as const;

export const localeNames: Record<string, string> = {
  en: "English",
  ru: "Русский",
  hi: "हिन्दी",
  vi: "Tiếng Việt",
  pt: "Português",
  fr: "Français",
  de: "Deutsch",
  es: "Español",
  it: "Italiano",
};

export const defaultLocale = "en";

export const localePrefix = "as-needed";

export const localeDetection =
  process.env.NEXT_PUBLIC_LOCALE_DETECTION === "true";

// 暂时移除 pathnames 配置以避免中间件错误
// 所有路径将使用默认的共享路径名
export const pathnames = {};
