# -----------------------------------------------------------------------------
# Web Information
# -----------------------------------------------------------------------------
NEXT_PUBLIC_WEB_URL = "http://localhost:3000"
NEXT_PUBLIC_SITE_URL = "http://localhost:3000"
NEXT_PUBLIC_PROJECT_NAME = "Make Coloring"

# -----------------------------------------------------------------------------
# Database with Supabase
# -----------------------------------------------------------------------------
# https://supabase.com/docs/guides/getting-started/quickstarts/nextjs
# Set your Supabase URL and Anon Key
SUPABASE_URL = "https://rreijnbyecydgwlbgnqo.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJyZWlqbmJ5ZWN5ZGd3bGJnbnFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM5MjM4NzMsImV4cCI6MjA1OTQ5OTg3M30.hA45MnJh6EWB4TQeW3sbr9FeOOPSbNs5tgw9ukw19WA"
SUPABASE_SERVICE_ROLE_KEY = ""

# -----------------------------------------------------------------------------
# Auth with next-auth
# https://authjs.dev/getting-started/installation?framework=Next.js
# Set your Auth URL and Secret
# Secret can be generated with `openssl rand -base64 32`
# -----------------------------------------------------------------------------
AUTH_SECRET = "dBwWQAp5Siy7hfBVmdNpz3N20bFw7KKVuDa3yziIbFQ="

# Google Auth
# https://authjs.dev/getting-started/providers/google
AUTH_GOOGLE_ID = "************-1n0bht3j22rsab8mr1fbh94628tre8an.apps.googleusercontent.com"
AUTH_GOOGLE_SECRET = "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"
NEXT_PUBLIC_AUTH_GOOGLE_ID = "************-1n0bht3j22rsab8mr1fbh94628tre8an.apps.googleusercontent.com"
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED = "true"
NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED = "true"

# Github Auth
# https://authjs.dev/getting-started/providers/github
AUTH_GITHUB_ID = ""
AUTH_GITHUB_SECRET = ""
NEXT_PUBLIC_AUTH_GITHUB_ENABLED = "false"

# -----------------------------------------------------------------------------
# Analytics with Google Analytics
# https://analytics.google.com
# -----------------------------------------------------------------------------
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID = "G-3BETRH8DXH"

# -----------------------------------------------------------------------------
# Analytics with Microsoft Clarity
# https://clarity.microsoft.com
# -----------------------------------------------------------------------------
NEXT_PUBLIC_MICROSOFT_CLARITY_ID = ""

# -----------------------------------------------------------------------------
# Google AdSense
# https://www.google.com/adsense
# -----------------------------------------------------------------------------
NEXT_PUBLIC_GOOGLE_ADSENSE_CLIENT_ID = "ca-pub-3452333802593381"


# -----------------------------------------------------------------------------
# Analytics with OpenPanel
# https://openpanel.dev
# -----------------------------------------------------------------------------
NEXT_PUBLIC_OPENPANEL_CLIENT_ID = ""



# -----------------------------------------------------------------------------
# Payment with PayPal
# https://developer.paypal.com/docs/checkout/
# -----------------------------------------------------------------------------
PAYPAL_CLIENT_ID = "AWqo04IFO6W1G19rI7Z23mOR4GJzRWh-U5bYMJN_6dquYg3m3r_SUPqRWCn4-0YrWI6Duu4J7z1cYYtf"
PAYPAL_CLIENT_SECRET = "EJkvTjGCSqHc6o15uQcPwxQcSAdoT9eIL91o93D483yF7stv-Q_gCLXMRdUOshYHKT4xm2Q9CI54Neqe"
PAYPAL_ENVIRONMENT = "sandbox"
PAYPAL_WEBHOOK_ID = "9E17020211004503U"

# PayPal订阅计划ID (价格已与前端配置同步)
# 月度计划
PAYPAL_BASIC_MONTHLY_PLAN_ID="P-5FD58755TD061853KNBWLVFI"
PAYPAL_PRO_MONTHLY_PLAN_ID="P-5F581393KA7389747NBWLVFY"
PAYPAL_PREMIUM_MONTHLY_PLAN_ID="P-9A827887U8023215TNBWLVGQ"

# 年度计划
PAYPAL_BASIC_YEARLY_PLAN_ID="P-2AP566212M1062836NBWLVHA"
PAYPAL_PRO_YEARLY_PLAN_ID="P-48J03473EW062015UNBWLVHI"
PAYPAL_PREMIUM_YEARLY_PLAN_ID="P-82E41577GU889211YNBWLVHY"

NEXT_PUBLIC_PAY_SUCCESS_URL = "http://localhost:3000/my-orders"
NEXT_PUBLIC_PAY_CANCEL_URL = "http://localhost:3000/pricing"

# -----------------------------------------------------------------------------
# Internationalization Configuration
# -----------------------------------------------------------------------------
# Enable automatic locale detection based on browser settings
NEXT_PUBLIC_LOCALE_DETECTION = "true"

ADMIN_EMAILS = "<EMAIL>"

NEXT_PUBLIC_DEFAULT_THEME = "light"

# -----------------------------------------------------------------------------
# Storage with aws s3 sdk
# https://docs.aws.amazon.com/s3/index.html
# -----------------------------------------------------------------------------
STORAGE_ENDPOINT = "https://c13548c13c8dd5f82821f6af4e65ff1b.r2.cloudflarestorage.com"
STORAGE_REGION = "auto"
STORAGE_ACCESS_KEY = "20c457b86478c9c089f73e308af70f66"
STORAGE_SECRET_KEY = "fcb69b6324a232f6d627a884177ec394e69218573424e6ea2aabf3c0aaa45018"
STORAGE_BUCKET = "makecoloring"
STORAGE_DOMAIN = "https://bucket.makecoloring.com"

# -----------------------------------------------------------------------------
# AI Image Generation Configuration
# -----------------------------------------------------------------------------
# 选择主要的图像生成provider: replicate | apicore | kie
ACTIVE_IMAGE_PROVIDER = "replicate"

# Provider API Keys - 配置你要使用的provider的API密钥即可自动启用
REPLICATE_API_TOKEN = "****************************************"
APICORE_API_TOKEN = "sk-YvWn0CIgzqwZEzZ5yO4qCHqtlZUPyWQwdqMesnlggMV4ZwnL"
KIE_API_TOKEN = "30bcbdb0173bc0231e0ff35f5c11e10f"

# -----------------------------------------------------------------------------
# API Callback URLs
# -----------------------------------------------------------------------------
# Kie.ai callback URL for async image generation
KIE_CALLBACK_URL = "https://6da74b7fc577.ngrok-free.app/api/kie-callback"

# 默认积分消耗
DEFAULT_CREDITS_COST = "3"

# -----------------------------------------------------------------------------
# Development Testing Configuration
# -----------------------------------------------------------------------------
# 开发环境测试配置
# 设置为'true'在开发模式下启用无限积分测试
UNLIMITED_CREDITS_FOR_TESTING = "true"
# 设置为'true'强制非会员状态以测试会员功能限制
FORCE_NON_PREMIUM = "false"

# -----------------------------------------------------------------------------
# Email Service with Resend
# -----------------------------------------------------------------------------
RESEND_API_KEY = "re_ZtNN9N1d_LWDvXvtPmA8r7PpQCVFkKmxn"




