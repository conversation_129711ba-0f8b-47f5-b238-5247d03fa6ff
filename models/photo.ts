import { Photo } from "@/types/photo";
import { getSupabaseClient } from "@/models/db";

export async function insertPhotos(photos: Photo[]) {
    const supabase = getSupabaseClient();
    const { data, error } = await supabase.from("photos").insert(photos);

    if (error) throw error;

    return data;
  }

export async function getPhotos(
    page: number = 1,
    limit: number = 50
  ): Promise<Photo[] | undefined> {
    try {
      console.log(`Fetching photos with page=${page}, limit=${limit}`);

      const supabase = getSupabaseClient();

      // 使用最简单的查询，不使用任何过滤或排序
      const { data, error } = await supabase
        .from("photos")
        .select()
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching photos:", error);
        return undefined;
      }

      console.log(`Successfully fetched ${data?.length || 0} photos`);
      if (data && data.length > 0) {
        console.log("First photo:", JSON.stringify(data[0]));
      } else {
        console.log("No photos found in database");
      }

      return data;
    } catch (e) {
      console.error("Unexpected error in getPhotos:", e);
      return undefined;
    }
  }

export async function getPhotosByUser(
    userUuid: string,
    page: number = 1,
    limit: number = 50
  ): Promise<Photo[] | undefined> {
    try {
      console.log(`Fetching photos for user ${userUuid} with page=${page}, limit=${limit}`);

      const supabase = getSupabaseClient();
      const offset = (page - 1) * limit;

      const { data, error } = await supabase
        .from("photos")
        .select()
        .eq("user_uuid", userUuid)
        .order("created_at", { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        console.error("Error fetching user photos:", error);
        return undefined;
      }

      console.log(`Successfully fetched ${data?.length || 0} photos for user ${userUuid}`);
      return data;
    } catch (e) {
      console.error("Unexpected error in getPhotosByUser:", e);
      return undefined;
    }
  }
