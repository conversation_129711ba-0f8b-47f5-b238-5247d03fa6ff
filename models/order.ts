import { Order } from "@/types/order";
import { getSupabaseClient } from "@/models/db";

export enum OrderStatus {
  Created = "created",
  Paid = "paid",
  Deleted = "deleted",
}

export async function insertOrder(order: Order) {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase.from("orders").insert(order);

  if (error) {
    throw error;
  }

  return data;
}

export async function findOrderByOrderNo(
  order_no: string
): Promise<Order | undefined> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("orders")
    .select("*")
    .eq("order_no", order_no)
    .single();

  if (error) {
    return undefined;
  }

  return data;
}

export async function getFirstPaidOrderByUserUuid(
  user_uuid: string
): Promise<Order | undefined> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("orders")
    .select("*")
    .eq("user_uuid", user_uuid)
    .eq("status", "paid")
    .order("created_at", { ascending: true })
    .limit(1)
    .single();

  if (error) {
    return undefined;
  }

  return data;
}

export async function getFirstPaidOrderByUserEmail(
  user_email: string
): Promise<Order | undefined> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("orders")
    .select("*")
    .eq("user_email", user_email)
    .eq("status", "paid")
    .order("created_at", { ascending: true })
    .limit(1)
    .single();

  if (error) {
    return undefined;
  }

  return data;
}

export async function updateOrderStatus(
  order_no: string,
  status: string,
  paid_at: string,
  paid_email: string,
  paid_detail: string
) {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("orders")
    .update({ status, paid_at, paid_detail, paid_email })
    .eq("order_no", order_no);

  if (error) {
    throw error;
  }

  return data;
}



export async function updateOrderPayPalId(
  order_no: string,
  paypal_order_id: string
) {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("orders")
    .update({ paypal_order_id })
    .eq("order_no", order_no);

  if (error) {
    throw error;
  }

  return data;
}

export async function findOrderByPayPalId(
  paypal_order_id: string
): Promise<Order | undefined> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("orders")
    .select("*")
    .eq("paypal_order_id", paypal_order_id)
    .single();

  if (error) {
    return undefined;
  }

  return data;
}





// 此函数已移除 - 使用PayPal专用的updateOrderSubscriptionStatus函数

// PayPal订阅相关函数
export async function findOrderBySubscriptionId(subscriptionId: string): Promise<Order | undefined> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from('orders')
    .select('*')
    .eq('paypal_subscription_id', subscriptionId)
    .single();

  if (error) {
    return undefined;
  }
  return data;
}

// 更新订单的PayPal订阅ID
export async function updateOrderSubscriptionId(
  order_no: string,
  subscription_id: string
) {
  const supabase = getSupabaseClient();

  const { data, error } = await supabase
    .from('orders')
    .update({
      paypal_subscription_id: subscription_id,
      updated_at: new Date().toISOString()
      // 不强制设置order_type，保持订单创建时的类型
    })
    .eq('order_no', order_no);

  if (error) {
    throw new Error(`更新订单PayPal订阅ID失败: ${error.message}`);
  }

  return data;
}

export async function updateOrderSubscriptionStatus(
  order_no: string,
  subscription_id: string,
  subscription_status: string,
  next_billing_time?: string,
  paid_detail?: string,
  paypal_plan_id?: string
) {
  const supabase = getSupabaseClient();

  // 首先查询订单的当前信息
  const { data: currentOrder, error: queryError } = await supabase
    .from("orders")
    .select("order_type, paypal_subscription_id")
    .eq("order_no", order_no)
    .single();

  if (queryError || !currentOrder) {
    throw new Error(`查询订单失败: ${queryError?.message}`);
  }

  const updateData: any = {
    paypal_subscription_id: subscription_id,
    subscription_status,
    status: subscription_status === 'ACTIVE' ? 'paid' : 'created',
    paid_at: subscription_status === 'ACTIVE' ? new Date().toISOString() : undefined,
    updated_at: new Date().toISOString()
  };

  // 智能判断：如果订单已经有订阅ID或者订阅状态为ACTIVE，则应该是订阅类型
  if (subscription_status === 'ACTIVE' || currentOrder.paypal_subscription_id) {
    updateData.order_type = 'subscription';
  }

  if (next_billing_time) {
    updateData.next_billing_time = next_billing_time;
  }

  if (paid_detail) {
    updateData.paid_detail = paid_detail;
  }

  if (paypal_plan_id) {
    updateData.paypal_plan_id = paypal_plan_id;
  }

  const { data, error } = await supabase
    .from("orders")
    .update(updateData)
    .eq("order_no", order_no);

  if (error) {
    throw error;
  }

  return data;
}

export async function getOrdersByUserUuid(
  user_uuid: string
): Promise<Order[] | undefined> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("orders")
    .select("*")
    .eq("user_uuid", user_uuid)
    .eq("status", "paid")
    .order("created_at", { ascending: false });
  // .gte("expired_at", now);

  if (error) {
    return undefined;
  }

  return data;
}

export async function getOrdersByUserEmail(
  user_email: string
): Promise<Order[] | undefined> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("orders")
    .select("*")
    .eq("user_email", user_email)
    .eq("status", "paid")
    .order("created_at", { ascending: false });
  // .gte("expired_at", now);

  if (error) {
    return undefined;
  }

  return data;
}

export async function getOrdersByPaidEmail(
  paid_email: string
): Promise<Order[] | undefined> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("orders")
    .select("*")
    .eq("paid_email", paid_email)
    .eq("status", "paid")
    .order("created_at", { ascending: false });
  // .gte("expired_at", now);

  if (error) {
    return undefined;
  }

  return data;
}

export async function getPaiedOrders(
  page: number,
  limit: number
): Promise<Order[] | undefined> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("orders")
    .select("*")
    .eq("status", "paid")
    .order("created_at", { ascending: false })
    .range((page - 1) * limit, page * limit);

  if (error) {
    return undefined;
  }

  return data;
}
