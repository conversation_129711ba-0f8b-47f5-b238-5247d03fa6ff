import { AsyncTask } from "@/types/async-task";
import { getSupabaseClient } from "@/models/db";

export enum AsyncTaskStatus {
  Pending = "pending",
  Generating = "generating",
  Success = "success",
  Failed = "failed",
  Expired = "expired",
}

export async function insertAsyncTask(task: AsyncTask) {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase.from("async_tasks").insert(task);

  if (error) throw error;

  return data;
}

export async function updateAsyncTask(uuid: string, updates: Partial<AsyncTask>) {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("async_tasks")
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq("uuid", uuid);

  if (error) throw error;

  return data;
}

export async function getAsyncTask(uuid: string): Promise<AsyncTask | null> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("async_tasks")
    .select("*")
    .eq("uuid", uuid)
    .single();

  if (error) {
    console.error("Error fetching async task:", error);
    return null;
  }

  return data;
}

export async function getAsyncTaskByTaskId(taskId: string): Promise<AsyncTask | null> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("async_tasks")
    .select("*")
    .eq("task_id", taskId)
    .single();

  if (error) {
    console.error("Error fetching async task by task_id:", error);
    return null;
  }

  return data;
}

export async function getUserAsyncTasks(
  userUuid: string,
  status?: AsyncTaskStatus,
  limit: number = 50
): Promise<AsyncTask[]> {
  const supabase = getSupabaseClient();
  
  let query = supabase
    .from("async_tasks")
    .select("*")
    .eq("user_uuid", userUuid)
    .order("created_at", { ascending: false })
    .limit(limit);

  if (status) {
    query = query.eq("status", status);
  }

  const { data, error } = await query;

  if (error) {
    console.error("Error fetching user async tasks:", error);
    return [];
  }

  return data || [];
}

export async function deleteAsyncTask(uuid: string) {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("async_tasks")
    .delete()
    .eq("uuid", uuid);

  if (error) throw error;

  return data;
}

// 清理过期的任务（根据expires_at字段）
export async function cleanupExpiredTasks() {
  const supabase = getSupabaseClient();
  const now = new Date().toISOString();

  const { data, error } = await supabase
    .from("async_tasks")
    .update({
      status: AsyncTaskStatus.Expired,
      error_message: "Task expired",
      updated_at: now,
      completed_at: now
    })
    .in("status", [AsyncTaskStatus.Pending, AsyncTaskStatus.Generating])
    .lt("expires_at", now);

  if (error) {
    console.error("Error cleaning up expired tasks:", error);
    return null;
  }

  return data;
}
