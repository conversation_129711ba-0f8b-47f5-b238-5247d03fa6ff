export type GalleryItemSize = 'small' | 'medium' | 'large' | 'wide' | 'tall';

export interface GalleryItem {
  id: string;
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  size?: GalleryItemSize;
}

export interface ScreenshotItem {
  id: string;
  title: string;
  description?: string;
  beforeImage: {
    src: string;
    alt: string;
  };
  afterImage: {
    src: string;
    alt: string;
  };
}

export type GalleryVariant = 'grid' | 'masonry' | 'screenshot';

export interface GalleryLightbox {
  imageAlt?: string;
  imageLoadError?: string;
  beforeLabel?: string;
  afterLabel?: string;
}

export interface Gallery {
  disabled?: boolean;
  name?: string;
  title?: string;
  description?: string;
  empty_title?: string;
  empty_description?: string;
  variant?: GalleryVariant;
  className?: string;
  items?: GalleryItem[];
  screenshots?: ScreenshotItem[];
  lightbox?: GalleryLightbox;
  max_items?: number;
}
