import { Image, Button } from "@/types/blocks/base";

// 从 hero 类型中导入图片展示相关类型
export type ImageDisplayMode = "single" | "before-after" | "grid" | "cards";

export interface SectionImage extends Image {
  title?: string;
  description?: string;
}

export interface SectionItem {
  title?: string;
  description?: string;
  label?: string;
  icon?: string;
  image?: Image; // 保持向后兼容
  images?: SectionImage[]; // 新的多图支持
  image_display_mode?: ImageDisplayMode; // 图片展示模式

  buttons?: Button[];
  url?: string;
  target?: string;
  children?: SectionItem[];
}

export interface Section {
  disabled?: boolean;
  name?: string;
  title?: string;
  description?: string;
  label?: string;
  icon?: string;
  image?: Image; // 保持向后兼容
  images?: SectionImage[]; // 新的多图支持
  image_display_mode?: ImageDisplayMode; // 图片展示模式
  buttons?: Button[];
  items?: SectionItem[];
}
