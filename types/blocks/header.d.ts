import { But<PERSON>, <PERSON>, Nav } from "@/types/blocks/base";

export interface ActivityBanner {
  show?: boolean;
  countdown_text?: string; // 倒计时前缀文字，如 "Offer Ends in"
  countdown_hours?: number; // 倒计时小时数
  main_text?: string; // 主要文字，如 "Limited-Time Offer: Save 40% on Annual Plans!"
  button?: Button; // 订阅按钮配置
  background_color?: string; // 背景色，支持渐变色 CSS 语法
  text_color?: string;
  button_color?: string;
  closable?: boolean;
  icon?: string;
}

export interface Header {
  disabled?: boolean;
  name?: string;
  brand?: Brand;
  nav?: Nav;
  buttons?: Button[];
  className?: string;
  show_sign?: boolean;
  show_locale?: boolean;
  show_theme?: boolean;
  sticky?: boolean; // 新增：是否固定在页面顶部
  activity_banner?: ActivityBanner; // 新增：活动提示配置
}
