import { Button } from "@/types/blocks/base";

export interface ImageGeneratorContent {
  title?: string;
  description?: string;
  tab_text_to_image?: string;
  tab_image_to_image?: string;
  tab_batch_generation?: string;
  upload_label?: string;
  upload_hint?: string;
  uploaded_image_alt?: string;
  description_label?: string;
  description_placeholder?: string;
  modify_description_label?: string;
  modify_description_placeholder?: string;
  style_label?: string;
  style_no_style?: string;
  style_3d_cute?: string;
  style_cat_trip?: string;
  style_instax?: string;
  style_action_figure?: string;
  style_snoopy?: string;
  style_irasutoya?: string;
  style_chibi_emoji?: string;
  style_4_grid_comics?: string;
  style_ghibli?: string;
  style_animal_crossing?: string;
  style_anime?: string;
  style_pixel_art?: string;
  style_disney?: string;
  style_pixar?: string;
  style_realistic?: string;
  size_label?: string;
  size_square?: string;
  size_portrait?: string;
  size_landscape?: string;
  size_photo?: string;
  size_wide?: string;
  submit_button?: string;
  error_message?: string;
  error_upload_image?: string;
  api_error_message?: string;
  delete_image?: string;
  verification_required?: string;
  verification_failed?: string;
  generating?: string;
  results_title?: string;
  download_button?: string;
  no_results?: string;
  model_label?: string;
  model_dalle3?: string;
  model_flux?: string;
  quality_label?: string;
  quality_standard?: string;
  quality_hd?: string;
}

export interface ImageGenerator {
  disabled?: boolean;
  name?: string;
  content?: ImageGeneratorContent;
  button?: Button;
  className?: string;
}

export type GenerationMode = 'text-to-image' | 'image-to-image' | 'batch-generation';
export type ImageStyle = 'no-style' | '3d-cute' | 'cat-trip' | 'instax' | 'action-figure' | 'snoopy' | 'irasutoya' | 'chibi-emoji' | '4-grid-comics' | 'ghibli' | 'animal-crossing' | 'anime' | 'pixel-art' | 'disney' | 'pixar' | 'realistic';
export type ImageSize = 'square' | 'portrait' | 'landscape' | 'photo' | 'wide';
export type AIModel = 'dalle3' | 'flux';
export type ImageQuality = 'standard' | 'hd';
