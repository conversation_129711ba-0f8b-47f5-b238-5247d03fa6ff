import { But<PERSON>, Image, Announcement } from "@/types/blocks/base";

export interface Announcement {
  title?: string;
  description?: string;
  label?: string;
  url?: string;
  target?: string;
}

export interface HeroTag {
  text: string;
  variant?: "default" | "secondary" | "destructive" | "outline";
  color?: string;
  className?: string;
}

export type HeroLayout = "center" | "split";
export type ImageDisplayMode = "single" | "before-after" | "grid" | "cards";

export interface HeroImage extends Image {
  title?: string;
  description?: string;
}

export interface Hero {
  name?: string;
  disabled?: boolean;
  announcement?: Announcement;
  title?: string;
  highlight_text?: string;
  description?: string;
  tag?: HeroTag[] | string[];
  buttons?: Button[];
  image?: Image; // 保持向后兼容
  images?: HeroImage[]; // 新的多图支持
  layout?: HeroLayout; // 布局模式
  image_display_mode?: ImageDisplayMode; // 图片展示模式
  tip?: string;
  show_happy_users?: boolean;
  show_badge?: boolean;
}
