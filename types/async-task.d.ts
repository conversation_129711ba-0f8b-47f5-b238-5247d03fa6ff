export interface AsyncTask {
  uuid: string;
  user_uuid: string;
  provider: 'kie' | 'replicate' | 'apicore';
  task_id: string; // 第三方API返回的任务ID
  task_type: 'text-to-image' | 'image-to-image';
  status: 'pending' | 'generating' | 'success' | 'failed' | 'expired';
  prompt: string;
  model?: string;
  size?: string;
  input_image?: string; // base64或URL
  result_images?: Array<{
    url: string;
    filename?: string;
  }>;
  error_message?: string;
  callback_data?: Record<string, any>; // 存储回调数据
  created_at: string;
  updated_at: string;
  completed_at?: string;
  expires_at: string;
  duration_seconds?: number; // 任务完成耗时（秒）
}
