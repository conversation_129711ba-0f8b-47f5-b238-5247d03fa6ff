export interface ColoringCategory {
  id: string;
  name: string;
  slug: string;
  seo_slug: string; // SEO友好的URL slug，如 "animals-coloring-pages"
  description?: string;
  icon?: string;
  image?: string;
  count?: number;
  // 分类页面元数据增强字段
  page_title?: string; // 分类页面专用标题
  detailed_description?: string; // 分类页面详细描述
  representative_image?: string; // 分类代表图片URL
  // SEO相关字段
  meta_title?: string;
  meta_description?: string;
  subcategories?: ColoringSubcategory[];
}

export interface ColoringSubcategory {
  id: string;
  name: string;
  seo_slug: string; // SEO友好的URL slug，如 "frog-coloring-pages"
  description: string;
  category_id: string;
  count: number;
  meta_title: string;
  meta_description: string;
}

export interface ColoringPage {
  id: string;
  title: string;
  description?: string;
  seo_slug: string; // SEO友好的URL slug，如 "cute-frog-coloring-page-for-kids"
  image_url: string; // 第三方图片链接
  thumbnail_url?: string;
  alt_text?: string; // 图片alt文本，用于SEO和无障碍访问
  category_id?: string;
  subcategory_id?: string;
  tags?: string[];
  difficulty_level?: 'easy' | 'medium' | 'hard';
  age_group?: 'toddler' | 'preschool' | 'school' | 'teen' | 'adult' | 'all';
  is_featured?: boolean;
  is_premium?: boolean;
  download_count?: number;
  view_count?: number;
  status?: 'draft' | 'published' | 'archived';
  source?: 'static' | 'user_generated' | 'ai_generated';
  created_by?: string;
  created_at: string;
  updated_at?: string;
  print_size?: string; // 打印尺寸：A4/Letter/A3
  coloring_tips?: string[]; // 涂色技巧数组
  color_suggestions?: string[]; // 推荐颜色
  educational_value?: string; // 教育价值描述
  usage_scenarios?: string[]; // 使用场景
  meta_title?: string; // SEO页面标题
  meta_description?: string; // SEO页面描述
}

export interface ColoringPagesResponse {
  pages: ColoringPage[];
  total: number;
  current_page: number;
  total_pages: number;
  has_next: boolean;
  has_prev: boolean;
}
