export interface Order {
  order_no: string;
  created_at: string;
  updated_at?: string;
  user_uuid: string;
  user_email: string;
  amount: number;
  credits: number;
  currency: string;
  status: string;

  // 产品信息
  product_id?: string;
  product_name?: string;
  valid_months?: number;
  expired_at?: string;

  // 支付信息
  paid_at?: string;
  paid_email?: string;
  paid_detail?: string;

  // PayPal专用字段
  paypal_order_id?: string;
  paypal_subscription_id?: string;
  paypal_plan_id?: string;

  // 订阅相关字段
  order_type: 'one_time' | 'subscription';
  subscription_status?: 'PENDING' | 'ACTIVE' | 'SUSPENDED' | 'CANCELLED' | 'EXPIRED';
  next_billing_time?: string;
  parent_order_no?: string;
  billing_cycle_count?: number;
}
