import { useState, useEffect } from 'react';

export interface ProviderConfig {
  activeProvider: 'replicate' | 'apicore' | 'kie';
  availableProviders: string[];
  summary: string;
}

export function useProviderConfig() {
  const [config, setConfig] = useState<ProviderConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchConfig = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/config/provider');
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        
        if (result.code === 0 && result.data) {
          setConfig(result.data);
          setError(null);
        } else {
          throw new Error(result.message || 'Failed to fetch provider config');
        }
      } catch (err) {
        console.error('Failed to fetch provider config:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        // 设置默认配置作为fallback
        setConfig({
          activeProvider: 'apicore', // 默认使用apicore
          availableProviders: ['apicore'],
          summary: 'Default configuration'
        });
      } finally {
        setLoading(false);
      }
    };

    fetchConfig();
  }, []);

  return { config, loading, error };
}
