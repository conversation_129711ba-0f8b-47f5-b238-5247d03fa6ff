"use client"

import { useState, useEffect, useCallback } from "react"
import { UserCredits } from "@/types/user"
import { useAppContext } from "@/contexts/app"

interface UseUserCreditsReturn {
  credits: UserCredits | null
  isLoading: boolean
  error: string | null
  refetch: () => Promise<void>
}

export function useUserCredits(): UseUserCreditsReturn {
  const { user } = useAppContext()
  const [credits, setCredits] = useState<UserCredits | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)

  const fetchCredits = useCallback(async () => {
    if (!user) {
      setCredits(null)
      setError(null)
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/user-credits')
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      
      if (data.code === 0 && data.data?.credits) {
        setCredits(data.data.credits)
      } else {
        throw new Error(data.message || 'Failed to fetch credits')
      }
    } catch (err) {
      console.error('Error fetching user credits:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
      setCredits(null)
    } finally {
      setIsLoading(false)
    }
  }, [user])

  // 初始加载
  useEffect(() => {
    fetchCredits()
  }, [fetchCredits])

  return {
    credits,
    isLoading,
    error,
    refetch: fetchCredits
  }
}
