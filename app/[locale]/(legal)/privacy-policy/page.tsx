import { getTranslations } from "next-intl/server";
import { Metadata } from "next";
import { <PERSON> } from "@/i18n/routing";
import { ArrowLeft } from "lucide-react";

export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: string };
}): Promise<Metadata> {
  const t = await getTranslations("privacy_policy");

  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/privacy-policy`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/privacy-policy`;
  }

  return {
    title: t("title"),
    description: t("description"),
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function PrivacyPolicyPage({
  params: { locale },
}: {
  params: { locale: string };
}) {
  const t = await getTranslations("privacy_policy");

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-4xl mx-auto px-6 py-12">
        <div className="prose prose-lg max-w-none">
          {/* 返回主页按钮 */}
          <div className="mb-6">
            <Link
              href="/"
              className="inline-flex items-center gap-2 text-sky-600 hover:text-sky-700 transition-colors duration-200 text-sm font-medium"
            >
              <ArrowLeft className="h-4 w-4" />
              {t("back_to_home")}
            </Link>
          </div>

          <div className="border-b border-gray-200 pb-6 mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              {t("title")}
            </h1>

            <p className="text-gray-600 text-sm mb-6">
              {t("last_updated")}
            </p>
          </div>

          <div className="space-y-8">
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-3">
                {t("section1.title")}
              </h2>
              <div className="text-gray-700 leading-relaxed">
                <p className="mb-4">{t("section1.intro")}</p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>{t("section1.item1")}</li>
                  <li>{t("section1.item2")}</li>
                  <li>{t("section1.item3")}</li>
                  <li>{t("section1.item4")}</li>
                </ul>
              </div>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-3">
                {t("section2.title")}
              </h2>
              <div className="text-gray-700 leading-relaxed">
                <p className="mb-4">{t("section2.intro")}</p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>{t("section2.item1")}</li>
                  <li>{t("section2.item2")}</li>
                  <li>{t("section2.item3")}</li>
                  <li>{t("section2.item4")}</li>
                </ul>
              </div>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-3">
                {t("section3.title")}
              </h2>
              <div className="text-gray-700 leading-relaxed">
                <p className="mb-4">{t("section3.intro")}</p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>{t("section3.item1")}</li>
                  <li>{t("section3.item2")}</li>
                  <li>{t("section3.item3")}</li>
                  <li>{t("section3.item4")}</li>
                </ul>
              </div>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-3">
                {t("section4.title")}
              </h2>
              <div className="text-gray-700 leading-relaxed">
                <p className="mb-4">{t("section4.intro")}</p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>{t("section4.item1")}</li>
                  <li>{t("section4.item2")}</li>
                  <li>{t("section4.item3")}</li>
                  <li>{t("section4.item4")}</li>
                </ul>
              </div>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-3">
                {t("section5.title")}
              </h2>
              <div className="text-gray-700 leading-relaxed">
                <p className="mb-4">{t("section5.intro")}</p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>{t("section5.item1")}</li>
                  <li>{t("section5.item2")}</li>
                  <li>{t("section5.item3")}</li>
                  <li>{t("section5.item4")}</li>
                </ul>
              </div>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-3">
                {t("section6.title")}
              </h2>
              <p className="text-gray-700 leading-relaxed">
                {t("section6.content")}
              </p>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-3">
                {t("section7.title")}
              </h2>
              <p className="text-gray-700 leading-relaxed">
                {t("section7.content")}
              </p>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-3">
                {t("contact.title")}
              </h2>
              <p className="text-gray-700 leading-relaxed">
                {t("contact.content")}
              </p>
            </section>
          </div>
        </div>
      </div>
    </div>
  );
}
