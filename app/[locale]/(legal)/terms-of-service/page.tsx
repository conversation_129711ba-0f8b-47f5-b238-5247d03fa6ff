import { getTranslations } from "next-intl/server";
import { Metadata } from "next";
import { <PERSON> } from "@/i18n/routing";
import { ArrowLeft } from "lucide-react";

export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: string };
}): Promise<Metadata> {
  const t = await getTranslations("terms_of_service");

  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/terms-of-service`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/terms-of-service`;
  }

  return {
    title: t("title"),
    description: t("description"),
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function TermsOfServicePage({
  params: { locale },
}: {
  params: { locale: string };
}) {
  const t = await getTranslations("terms_of_service");

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-4xl mx-auto px-6 py-12">
        <div className="prose prose-lg max-w-none">
          {/* 返回主页按钮 */}
          <div className="mb-6">
            <Link
              href="/"
              className="inline-flex items-center gap-2 text-sky-600 hover:text-sky-700 transition-colors duration-200 text-sm font-medium"
            >
              <ArrowLeft className="h-4 w-4" />
              {t("back_to_home")}
            </Link>
          </div>

          <div className="border-b border-gray-200 pb-6 mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              {t("title")}
            </h1>

            <p className="text-gray-600 text-sm mb-6">
              {t("last_updated")}
            </p>
          </div>

          <div className="space-y-8">
            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-3">
                {t("section1.title")}
              </h2>
              <p className="text-gray-700 leading-relaxed">
                {t("section1.content")}
              </p>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-3">
                {t("section2.title")}
              </h2>
              <div className="text-gray-700 leading-relaxed">
                <div className="mb-6">
                  <h3 className="text-lg font-medium mb-3">{t("section2.free_users_intro")}</h3>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>{t("section2.free_item1")}</li>
                    <li>{t("section2.free_item2")}</li>
                    <li>{t("section2.free_item3")}</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-3">{t("section2.premium_users_intro")}</h3>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>{t("section2.premium_item1")}</li>
                    <li>{t("section2.premium_item2")}</li>
                    <li>{t("section2.premium_item3")}</li>
                  </ul>
                </div>
              </div>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-3">
                {t("section3.title")}
              </h2>
              <div className="text-gray-700 leading-relaxed">
                <p className="mb-4">{t("section3.intro")}</p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>{t("section3.item1")}</li>
                  <li>{t("section3.item2")}</li>
                  <li>{t("section3.item3")}</li>
                  <li>{t("section3.item4")}</li>
                </ul>
              </div>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-3">
                {t("section4.title")}
              </h2>
              <div className="text-gray-700 leading-relaxed">
                <p className="mb-4">{t("section4.intro")}</p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>{t("section4.item1")}</li>
                  <li>{t("section4.item2")}</li>
                  <li>{t("section4.item3")}</li>
                  <li>{t("section4.item4")}</li>
                </ul>
              </div>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-3">
                {t("section5.title")}
              </h2>
              <div className="text-gray-700 leading-relaxed">
                <p className="mb-4">{t("section5.intro")}</p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>{t("section5.item1")}</li>
                  <li>{t("section5.item2")}</li>
                  <li>{t("section5.item3")}</li>
                  <li>{t("section5.item4")}</li>
                </ul>
              </div>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-3">
                {t("section6.title")}
              </h2>
              <p className="text-gray-700 leading-relaxed">
                {t("section6.content")}
              </p>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-3">
                {t("section7.title")}
              </h2>
              <p className="text-gray-700 leading-relaxed">
                {t("section7.content")}
              </p>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-3">
                {t("section8.title")}
              </h2>
              <div className="text-gray-700 leading-relaxed">
                <p className="mb-4">{t("section8.intro")}</p>
                <ul className="list-disc pl-6 space-y-2 mb-4">
                  <li>{t("section8.item1")}</li>
                  <li>{t("section8.item2")}</li>
                  <li>{t("section8.item3")}</li>
                  <li>{t("section8.item4")}</li>
                  <li>{t("section8.item5")}</li>
                  <li>{t("section8.item6")}</li>
                  <li>{t("section8.item7")}</li>
                </ul>
                <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-md">
                  <strong>Note:</strong> {t("section8.note")}
                </p>
              </div>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-gray-900 mb-3">
                {t("contact.title")}
              </h2>
              <p className="text-gray-700 leading-relaxed">
                {t("contact.content")}
              </p>
            </section>
          </div>
        </div>
      </div>
    </div>
  );
}
