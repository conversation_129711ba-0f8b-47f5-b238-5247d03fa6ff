"use client";

import { useState, useEffect } from "react";
import { TableColumn } from "@/types/blocks/table";
import TableSlot from "@/components/dashboard/slots/table";
import { Table as TableSlotType } from "@/types/slots/table";
import moment from "moment";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";

interface Order {
  order_no: string;
  paid_email: string;
  product_name: string;
  amount: number;
  currency: string;
  status: string;
  payment_provider: string;
  paypal_order_id?: string;
  created_at: string;
  paid_at?: string;
  credits: number;
}

export default function PayPalOrdersPage() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [refundDialogOpen, setRefundDialogOpen] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [refundAmount, setRefundAmount] = useState("");
  const [refundReason, setRefundReason] = useState("");
  const [refundLoading, setRefundLoading] = useState(false);

  // 获取PayPal订单列表
  const fetchOrders = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/paypal-orders');
      if (!response.ok) {
        throw new Error('Failed to fetch orders');
      }
      const data = await response.json();
      setOrders(data.orders || []);
    } catch (error) {
      console.error('获取订单失败:', error);
      toast.error('获取订单失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOrders();
  }, []);

  // 处理退款
  const handleRefund = async () => {
    if (!selectedOrder || !refundAmount) {
      toast.error('请填写退款金额');
      return;
    }

    const amount = parseFloat(refundAmount);
    const maxAmount = selectedOrder.amount / 100;

    if (amount <= 0 || amount > maxAmount) {
      toast.error(`退款金额必须在 0 到 ${maxAmount} 之间`);
      return;
    }

    try {
      setRefundLoading(true);
      const response = await fetch('/api/admin-refund', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderNo: selectedOrder.order_no,
          refundAmount: amount.toString(),
          refundReason: refundReason || '管理员处理退款',
        }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success(`退款成功！退款ID: ${result.refundId}`);
        setRefundDialogOpen(false);
        setSelectedOrder(null);
        setRefundAmount("");
        setRefundReason("");
        // 刷新订单列表
        fetchOrders();
      } else {
        toast.error(result.error || '退款失败');
      }
    } catch (error) {
      console.error('退款失败:', error);
      toast.error('退款失败');
    } finally {
      setRefundLoading(false);
    }
  };

  const columns: TableColumn[] = [
    { name: "order_no", title: "订单号" },
    { name: "paid_email", title: "支付邮箱" },
    { name: "product_name", title: "产品名称" },
    {
      name: "amount",
      title: "金额",
      callback: (row: Order) => {
        const currency = row.currency?.toUpperCase() || 'USD';
        const symbol = currency === 'CNY' ? '¥' : '$';
        return `${symbol} ${(row.amount / 100).toFixed(2)}`;
      },
    },
    {
      name: "status",
      title: "状态",
      callback: (row: Order) => {
        const statusMap: { [key: string]: string } = {
          created: '🟡 已创建',
          paid: '🟢 已支付',
          refunded: '🔴 已退款',
          partial_refunded: '🟠 部分退款',
          failed: '❌ 失败'
        };
        return statusMap[row.status] || row.status;
      },
    },
    { name: "paypal_order_id", title: "PayPal订单ID" },
    {
      name: "created_at",
      title: "创建时间",
      callback: (row: Order) => moment(row.created_at).format("YYYY-MM-DD HH:mm:ss"),
    },
    {
      name: "paid_at",
      title: "支付时间",
      callback: (row: Order) => row.paid_at ? moment(row.paid_at).format("YYYY-MM-DD HH:mm:ss") : '-',
    },
    {
      name: "actions",
      title: "操作",
      callback: (row: Order) => (
        <div className="flex gap-2">
          {row.status === 'paid' && (
            <Dialog open={refundDialogOpen} onOpenChange={setRefundDialogOpen}>
              <DialogTrigger asChild>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => {
                    setSelectedOrder(row);
                    setRefundAmount((row.amount / 100).toString());
                  }}
                >
                  退款
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>退款订单: {selectedOrder?.order_no}</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="refundAmount">退款金额</Label>
                    <Input
                      id="refundAmount"
                      type="number"
                      step="0.01"
                      max={selectedOrder ? selectedOrder.amount / 100 : 0}
                      value={refundAmount}
                      onChange={(e) => setRefundAmount(e.target.value)}
                      placeholder="输入退款金额"
                    />
                    <p className="text-sm text-gray-500 mt-1">
                      最大退款金额: ${selectedOrder ? (selectedOrder.amount / 100).toFixed(2) : '0.00'}
                    </p>
                  </div>
                  <div>
                    <Label htmlFor="refundReason">退款原因</Label>
                    <Textarea
                      id="refundReason"
                      value={refundReason}
                      onChange={(e) => setRefundReason(e.target.value)}
                      placeholder="输入退款原因（可选）"
                    />
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setRefundDialogOpen(false);
                        setSelectedOrder(null);
                        setRefundAmount("");
                        setRefundReason("");
                      }}
                    >
                      取消
                    </Button>
                    <Button
                      onClick={handleRefund}
                      disabled={refundLoading}
                    >
                      {refundLoading ? '处理中...' : '确认退款'}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          )}
        </div>
      ),
    },
  ];

  const table: TableSlotType = {
    title: "PayPal订单管理",
    description: "管理所有PayPal支付订单，包括退款操作",
    toolbar: {
      items: [],
    },
    columns,
    data: orders,
    loading,
    empty_message: "暂无PayPal订单",
  };

  return <TableSlot {...table} />;
}
