'use client';

import { useState, useEffect } from 'react';
import { TableColumn } from "@/types/blocks/table";
import TableSlot from "@/components/dashboard/slots/table";
import { Table as TableSlotType } from "@/types/slots/table";
import { ColoringPage } from "@/types/coloring-category";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { coloringCategories } from "@/lib/coloring-categories";
import { toast } from "sonner";
import moment from "moment";

export default function ColoringPagesAdmin() {
  const [pages, setPages] = useState<ColoringPage[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    image_url: '',
    thumbnail_url: '',
    alt_text: '',
    category_id: '',
    subcategory_id: '',
    seo_slug: '',
    tags: '',
    difficulty_level: 'easy' as 'easy' | 'medium' | 'hard',
    age_group: 'all' as 'toddler' | 'preschool' | 'school' | 'teen' | 'adult' | 'all',
    is_featured: false,
    is_premium: false,
    status: 'published' as 'draft' | 'published' | 'archived',
    source: 'static' as 'static' | 'user_generated' | 'ai_generated',
    meta_title: '',
    meta_description: ''
  });

  // 获取涂色页面列表
  const fetchPages = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/coloring-pages');
      if (response.ok) {
        const data = await response.json();
        setPages(data.pages || []);
      } else {
        toast.error('获取涂色页面失败');
      }
    } catch (error) {
      console.error('获取涂色页面失败:', error);
      toast.error('获取涂色页面失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPages();
  }, []);

  // 生成SEO slug
  const generateSeoSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  // 处理标题变化，自动生成SEO slug
  const handleTitleChange = (title: string) => {
    setFormData(prev => ({
      ...prev,
      title,
      seo_slug: generateSeoSlug(title) + '-coloring-page',
      meta_title: `Free ${title} Coloring Page - Printable Coloring Sheet`,
      meta_description: `Download free ${title.toLowerCase()} coloring page. High-quality printable coloring sheet perfect for creative fun and learning.`
    }));
  };

  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title || !formData.image_url || !formData.category_id) {
      toast.error('请填写必填字段');
      return;
    }

    try {
      const submitData = {
        ...formData,
        tags: formData.tags ? formData.tags.split(',').map(tag => tag.trim()) : [],
        seo_slug: formData.seo_slug.startsWith('/') ? formData.seo_slug : `/${formData.seo_slug}`
      };

      const response = await fetch('/api/admin/coloring-pages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      });

      if (response.ok) {
        toast.success('涂色页面创建成功');
        setDialogOpen(false);
        setFormData({
          title: '',
          description: '',
          image_url: '',
          thumbnail_url: '',
          alt_text: '',
          category_id: '',
          subcategory_id: '',
          seo_slug: '',
          tags: '',
          difficulty_level: 'easy',
          age_group: 'all',
          is_featured: false,
          is_premium: false,
          status: 'published',
          source: 'static',
          meta_title: '',
          meta_description: ''
        });
        fetchPages();
      } else {
        const error = await response.json();
        toast.error(error.error || '创建失败');
      }
    } catch (error) {
      console.error('创建涂色页面失败:', error);
      toast.error('创建失败');
    }
  };

  // 获取选中分类的子分类
  const getSubcategories = () => {
    const category = coloringCategories.find(cat => cat.id === formData.category_id);
    return category?.subcategories || [];
  };

  const columns: TableColumn[] = [
    { name: "title", title: "标题" },
    {
      name: "image_url",
      title: "图片",
      callback: (row: ColoringPage) => (
        <img src={row.image_url} className="w-16 h-16 object-cover rounded" alt={row.alt_text || row.title} />
      ),
    },
    {
      name: "category_id",
      title: "分类",
      callback: (row: ColoringPage) => {
        const category = coloringCategories.find(cat => cat.id === row.category_id);
        return category?.name || row.category_id;
      },
    },
    {
      name: "is_featured",
      title: "特色",
      callback: (row: ColoringPage) => (
        <Badge variant={row.is_featured ? "default" : "secondary"}>
          {row.is_featured ? "是" : "否"}
        </Badge>
      ),
    },
    {
      name: "status",
      title: "状态",
      callback: (row: ColoringPage) => {
        const statusMap = {
          draft: { label: "草稿", variant: "secondary" as const },
          published: { label: "已发布", variant: "default" as const },
          archived: { label: "已归档", variant: "outline" as const }
        };
        const status = statusMap[row.status || 'published'];
        return <Badge variant={status.variant}>{status.label}</Badge>;
      },
    },
    {
      name: "view_count",
      title: "浏览次数",
      callback: (row: ColoringPage) => row.view_count || 0,
    },
    {
      name: "download_count",
      title: "下载次数",
      callback: (row: ColoringPage) => row.download_count || 0,
    },
    {
      name: "created_at",
      title: "创建时间",
      callback: (row: ColoringPage) => moment(row.created_at).format("YYYY-MM-DD HH:mm"),
    },
  ];

  const table: TableSlotType = {
    title: "涂色页面管理",
    description: "管理所有涂色页面，包括添加、编辑和删除操作",
    columns,
    data: pages,
    loading,
    empty_message: "暂无涂色页面",
  };

  return (
    <>
      <div className="mb-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-medium">涂色页面管理</h1>
            <p className="text-sm text-muted-foreground">管理所有涂色页面，包括添加、编辑和删除操作</p>
          </div>
          <Button onClick={() => setDialogOpen(true)} className="flex items-center gap-2">
            <span>添加涂色页面</span>
          </Button>
        </div>
      </div>

      <TableSlot {...table} />

      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>添加涂色页面</DialogTitle>
          </DialogHeader>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="title">标题 *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleTitleChange(e.target.value)}
                  placeholder="例如：可爱的青蛙涂色页"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="seo_slug">SEO Slug *</Label>
                <Input
                  id="seo_slug"
                  value={formData.seo_slug}
                  onChange={(e) => setFormData(prev => ({ ...prev, seo_slug: e.target.value }))}
                  placeholder="cute-frog-coloring-page"
                  required
                />
              </div>
            </div>

            <div>
              <Label htmlFor="description">描述</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="详细描述这个涂色页面..."
                rows={3}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="image_url">图片URL *</Label>
                <Input
                  id="image_url"
                  value={formData.image_url}
                  onChange={(e) => setFormData(prev => ({ ...prev, image_url: e.target.value }))}
                  placeholder="https://example.com/image.jpg"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="thumbnail_url">缩略图URL</Label>
                <Input
                  id="thumbnail_url"
                  value={formData.thumbnail_url}
                  onChange={(e) => setFormData(prev => ({ ...prev, thumbnail_url: e.target.value }))}
                  placeholder="https://example.com/thumbnail.jpg"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="alt_text">Alt文本</Label>
              <Input
                id="alt_text"
                value={formData.alt_text}
                onChange={(e) => setFormData(prev => ({ ...prev, alt_text: e.target.value }))}
                placeholder="图片的alt描述文本"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="category_id">分类 *</Label>
                <Select
                  value={formData.category_id}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, category_id: value, subcategory_id: '' }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择分类" />
                  </SelectTrigger>
                  <SelectContent>
                    {coloringCategories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="subcategory_id">子分类</Label>
                <Select
                  value={formData.subcategory_id}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, subcategory_id: value }))}
                  disabled={!formData.category_id}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择子分类" />
                  </SelectTrigger>
                  <SelectContent>
                    {getSubcategories().map((subcategory) => (
                      <SelectItem key={subcategory.id} value={subcategory.id}>
                        {subcategory.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="tags">标签（用逗号分隔）</Label>
              <Input
                id="tags"
                value={formData.tags}
                onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}
                placeholder="青蛙, 动物, 简单"
              />
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label htmlFor="difficulty_level">难度</Label>
                <Select
                  value={formData.difficulty_level}
                  onValueChange={(value: 'easy' | 'medium' | 'hard') => setFormData(prev => ({ ...prev, difficulty_level: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="easy">简单</SelectItem>
                    <SelectItem value="medium">中等</SelectItem>
                    <SelectItem value="hard">困难</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="age_group">年龄组</Label>
                <Select
                  value={formData.age_group}
                  onValueChange={(value: any) => setFormData(prev => ({ ...prev, age_group: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有年龄</SelectItem>
                    <SelectItem value="toddler">幼儿</SelectItem>
                    <SelectItem value="preschool">学前</SelectItem>
                    <SelectItem value="school">学龄</SelectItem>
                    <SelectItem value="teen">青少年</SelectItem>
                    <SelectItem value="adult">成人</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="status">状态</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value: 'draft' | 'published' | 'archived') => setFormData(prev => ({ ...prev, status: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">草稿</SelectItem>
                    <SelectItem value="published">已发布</SelectItem>
                    <SelectItem value="archived">已归档</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <Switch
                  id="is_featured"
                  checked={formData.is_featured}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_featured: checked }))}
                />
                <Label htmlFor="is_featured">特色页面</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch
                  id="is_premium"
                  checked={formData.is_premium}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_premium: checked }))}
                />
                <Label htmlFor="is_premium">付费页面</Label>
              </div>
            </div>

            <div>
              <Label htmlFor="meta_title">SEO标题</Label>
              <Input
                id="meta_title"
                value={formData.meta_title}
                onChange={(e) => setFormData(prev => ({ ...prev, meta_title: e.target.value }))}
                placeholder="SEO页面标题"
              />
            </div>

            <div>
              <Label htmlFor="meta_description">SEO描述</Label>
              <Textarea
                id="meta_description"
                value={formData.meta_description}
                onChange={(e) => setFormData(prev => ({ ...prev, meta_description: e.target.value }))}
                placeholder="SEO页面描述"
                rows={2}
              />
            </div>

            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={() => setDialogOpen(false)}>
                取消
              </Button>
              <Button type="submit">
                创建
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </>
  );
}
