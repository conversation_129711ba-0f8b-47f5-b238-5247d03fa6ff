import { getTranslations } from "next-intl/server";
import { Metadata } from "next";
import ImageToColoring from "@/components/blocks/image-to-coloring";

export async function generateMetadata({
  params: { locale: _locale },
}: {
  params: { locale: string };
}): Promise<Metadata> {
  const t = await getTranslations("image_to_coloring");

  return {
    title: t("title"),
    description: t("description"),
  };
}

export default function ImageToColoringPage() {
  return (
    <div className="min-h-screen bg-background">
      <ImageToColoring />
    </div>
  );
}