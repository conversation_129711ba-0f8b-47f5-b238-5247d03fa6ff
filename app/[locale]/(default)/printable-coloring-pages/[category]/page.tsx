import { Metadata } from "next";
import { notFound } from "next/navigation";
import { coloringCategories } from "@/lib/coloring-categories";
import { ColoringPagesService } from "@/lib/services/coloring-pages-service";
import PrintableColoringPagesCategory from "@/components/blocks/printable-coloring-pages-category";
import PrintableColoringPagesSEO from "@/components/seo/printable-coloring-pages-seo";

interface PageProps {
  params: {
    locale: string;
    category: string;
  };
}

export async function generateMetadata({
  params: { locale, category },
}: PageProps): Promise<Metadata> {
  // 根据URL参数找到对应的分类
  const categoryData = coloringCategories.find(cat =>
    cat.seo_slug.replace(/^\//, '') === category
  );

  if (!categoryData) {
    return {
      title: "Category Not Found",
      description: "The requested coloring page category was not found.",
    };
  }

  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://makecoloring.com';
  let canonicalUrl = `${baseUrl}/printable-coloring-pages/${category}`;
  if (locale !== "en") {
    canonicalUrl = `${baseUrl}/${locale}/printable-coloring-pages/${category}`;
  }

  return {
    title: categoryData.meta_title || `${categoryData.name} Coloring Pages - Free Printable | MakeColoring`,
    description: categoryData.meta_description || `Download free ${categoryData.name.toLowerCase()} coloring pages. High-quality printable coloring sheets perfect for kids and adults.`,
    keywords: `${categoryData.name.toLowerCase()} coloring pages, free ${categoryData.name.toLowerCase()} coloring sheets, printable ${categoryData.name.toLowerCase()} coloring pages`,
    openGraph: {
      title: categoryData.meta_title || `${categoryData.name} Coloring Pages`,
      description: categoryData.meta_description || categoryData.description,
      type: 'website',
      url: canonicalUrl,
      siteName: 'MakeColoring',
    },
    twitter: {
      card: 'summary_large_image',
      title: categoryData.meta_title || `${categoryData.name} Coloring Pages`,
      description: categoryData.meta_description || categoryData.description,
    },
    alternates: {
      canonical: canonicalUrl,
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

export default async function CategoryPage({ params }: PageProps) {
  const { category } = params;

  // 根据URL参数找到对应的分类
  const categoryData = coloringCategories.find(cat =>
    cat.seo_slug.replace(/^\//, '') === category
  );

  if (!categoryData) {
    notFound();
  }

  // 获取该分类的涂色页和详细信息
  let coloringPages: any[] = [];
  let totalPages = 0;
  let categoryPageCount = 0;

  // 检查数据库配置
  const useDatabaseStr = process.env.USE_DATABASE || 'true';
  const useDatabase = useDatabaseStr.toLowerCase() === 'true';
  const hasDbConfig = !!(process.env.SUPABASE_URL && process.env.SUPABASE_ANON_KEY);

  console.log(`🔍 分类页面 ${categoryData.id} 数据加载:`, {
    useDatabase,
    hasDbConfig,
    categoryId: categoryData.id,
    categoryName: categoryData.name
  });

  if (useDatabase && hasDbConfig) {
    try {
      console.log(`📊 尝试从数据库获取分类 ${categoryData.id} 的数据...`);

      // 获取分类页面数量
      categoryPageCount = await ColoringPagesService.getCategoryPageCount(categoryData.id);
      console.log(`📈 分类 ${categoryData.id} 页面数量: ${categoryPageCount}`);

      // 获取分类页面数据
      const result = await ColoringPagesService.getPagesByCategory(categoryData.id, 1, 50);
      coloringPages = result.pages || [];
      totalPages = result.total_pages || 0;

      console.log(`✅ 从数据库成功获取 ${coloringPages.length} 个页面`);
    } catch (error) {
      console.error(`❌ 数据库查询失败 (分类: ${categoryData.id}):`, error);
      // 错误处理将在下面的逻辑中进行
    }
  } else {
    console.log(`📝 跳过数据库查询:`, {
      reason: !useDatabase ? '数据库未启用' : '数据库配置缺失',
      useDatabase,
      hasDbConfig
    });
  }

  // 如果数据库中没有数据或查询失败，使用静态数据作为回退
  if (coloringPages.length === 0) {
    console.log(`🔄 生成静态回退数据 (分类: ${categoryData.id})`);

    // 生成一些示例数据
    coloringPages = Array.from({ length: Math.min(categoryPageCount || 47, 50) }, (_, i) => ({
      id: `${categoryData.id}-${i + 1}`,
      title: `${categoryData.name} Coloring Page ${i + 1}`,
      description: `Beautiful ${categoryData.name.toLowerCase()} coloring page for kids and adults. This printable coloring sheet features detailed designs perfect for creative activities and learning.`,
      image_url: `https://picsum.photos/400/400?random=${categoryData.id}-${i + 1}`,
      thumbnail_url: `https://picsum.photos/200/200?random=${categoryData.id}-${i + 1}`,
      alt_text: `${categoryData.name} coloring page ${i + 1}`,
      seo_slug: `${categoryData.seo_slug.replace(/^\//, '')}-page-${i + 1}`,
      category_id: categoryData.id,
      tags: [categoryData.name.toLowerCase(), 'coloring', 'printable', 'kids'],
      difficulty_level: ['easy', 'medium', 'hard'][i % 3],
      age_group: ['toddler', 'preschool', 'school', 'teen', 'adult'][i % 5],
      is_featured: i < 3,
      is_premium: false,
      download_count: Math.floor(Math.random() * 500) + 50,
      view_count: Math.floor(Math.random() * 1000) + 100,
      status: 'published',
      source: 'static',
      created_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date().toISOString(),
    }));
    totalPages = Math.ceil(coloringPages.length / 50);
    categoryPageCount = coloringPages.length;

    console.log(`✅ 生成了 ${coloringPages.length} 个静态页面`);
  }

  return (
    <div className="min-h-screen bg-background">
      <PrintableColoringPagesSEO
        category={{...categoryData, count: categoryPageCount}}
        pageCount={categoryPageCount}
      />
      <PrintableColoringPagesCategory
        category={{...categoryData, count: categoryPageCount}}
        coloringPages={coloringPages}
        totalPages={totalPages}
        currentPage={1}
      />
    </div>
  );
}

// 生成静态路径
export async function generateStaticParams() {
  const locales = ['en', 'ru', 'hi', 'vi', 'pt', 'fr', 'de', 'es', 'it'];
  const paths: { locale: string; category: string }[] = [];

  locales.forEach(locale => {
    coloringCategories.forEach(category => {
      const categorySlug = category.seo_slug.replace(/^\//, '');
      paths.push({
        locale,
        category: categorySlug,
      });
    });
  });

  return paths;
}
