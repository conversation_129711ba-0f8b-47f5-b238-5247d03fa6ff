import FAQ from "@/components/blocks/faq";
import Feature4 from "@/components/blocks/feature4";
import Feature from "@/components/blocks/feature";
import Hero from "@/components/blocks/hero";
import { getLandingPage } from "@/services/page";
import CTA from "@/components/blocks/cta";
import Testimonial from "@/components/blocks/testimonial";

export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: string };
}) {
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}`;
  }

  return {
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function LandingPage({
  params: { locale },
}: {
  params: { locale: string };
}) {
  const page = await getLandingPage(locale);

  return (
    <>
      {page.hero && <Hero hero={page.hero} />}
      {page.main_features && <Feature4 section={page.main_features} />}
      {page.usage && <Feature section={page.usage} />}
      {page.faq && <FAQ section={page.faq} />}
      {page.cta && <CTA section={page.cta} />}
    </>
  );
}
