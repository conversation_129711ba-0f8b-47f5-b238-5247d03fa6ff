import { Metadata } from "next";
import { notFound } from "next/navigation";
import { sampleColoringPages } from "@/lib/coloring-pages-data";
import { ColoringPagesService } from "@/lib/services/coloring-pages-service";
import ColoringPageDetail from "@/components/blocks/coloring-page-detail";

interface PageProps {
  params: {
    locale: string;
    "category-slug": string;
  };
}

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const slug = params["category-slug"];

  // 检查是否是单页详情（先从数据库查找）
  const coloringPage = await ColoringPagesService.getColoringPageBySlug(slug);
  if (coloringPage) {
    return {
      title: coloringPage.meta_title || coloringPage.title,
      description: coloringPage.meta_description || coloringPage.description,
      openGraph: {
        title: coloringPage.meta_title || coloringPage.title,
        description: coloringPage.meta_description || coloringPage.description,
        images: [
          {
            url: coloringPage.image_url,
            width: 400,
            height: 400,
            alt: coloringPage.alt_text || coloringPage.title,
          }
        ],
        type: 'website',
      },
      twitter: {
        card: 'summary_large_image',
        title: coloringPage.meta_title || coloringPage.title,
        description: coloringPage.meta_description || coloringPage.description,
        images: [coloringPage.image_url],
      },
    };
  }

  // 如果不是涂色页详情，返回404
  notFound();
}

export default async function DynamicPage({ params }: PageProps) {
  const slug = params["category-slug"];

  // 检查是否是单页详情（先从数据库查找）
  const coloringPage = await ColoringPagesService.getColoringPageBySlug(slug);
  if (coloringPage) {
    // 获取相关页面
    const relatedPages = await ColoringPagesService.getRelatedPages(
      coloringPage.id,
      coloringPage.category_id || '',
      coloringPage.subcategory_id,
      4
    );

    return (
      <div className="min-h-screen bg-background">
        <ColoringPageDetail coloringPage={coloringPage} relatedPages={relatedPages} />
      </div>
    );
  }

  // 如果不是涂色页详情，返回404
  notFound();
}

// 生成静态路径
export async function generateStaticParams() {
  const paths: { locale: string; "category-slug": string }[] = [];

  // 获取所有语言
  const locales = ['en', 'ru', 'hi', 'vi', 'pt', 'fr', 'de', 'es', 'it'];

  // 获取数据库中的所有已发布页面
  let dbPages: any[] = [];
  try {
    dbPages = await ColoringPagesService.getAllPublishedPages();
  } catch (error) {
    console.error('获取数据库页面失败，使用静态数据:', error);
    dbPages = sampleColoringPages;
  }

  // 为每个语言生成所有可能的路径
  locales.forEach(locale => {
    // 添加数据库中的单页详情路径
    dbPages.forEach(page => {
      if (page.seo_slug) {
        paths.push({
          locale,
          "category-slug": page.seo_slug.replace(/^\//, '') // 移除开头的斜杠
        });
      }
    });

    // 添加静态数据中的单页详情路径（作为备份）
    sampleColoringPages.forEach(page => {
      paths.push({
        locale,
        "category-slug": page.seo_slug.replace(/^\//, '') // 移除开头的斜杠
      });
    });
  });

  return paths;
}
