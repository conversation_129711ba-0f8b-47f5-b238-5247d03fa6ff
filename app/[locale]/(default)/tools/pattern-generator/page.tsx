import { getTranslations } from "next-intl/server";
import { Metadata } from "next";

export async function generateMetadata({
  params: { locale: _locale },
}: {
  params: { locale: string };
}): Promise<Metadata> {
  const t = await getTranslations("tools");

  return {
    title: t("pattern_generator.title"),
    description: t("pattern_generator.description"),
  };
}

export default function PatternGeneratorPage() {
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-center mb-8">
            Pattern Coloring Pages Generator
          </h1>
          <div className="bg-card rounded-lg p-8 shadow-sm">
            <p className="text-muted-foreground text-center mb-8">
              Create intricate pattern-based coloring pages and designs.
            </p>
            <div className="text-center">
              <p className="text-lg">Coming Soon...</p>
              <p className="text-sm text-muted-foreground mt-2">
                This tool is currently under development.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
