import { getTranslations } from "next-intl/server";
import { Metadata } from "next";
import TextToColoring from "@/components/blocks/text-to-coloring";

export async function generateMetadata({
  params: { locale: _locale },
}: {
  params: { locale: string };
}): Promise<Metadata> {
  const t = await getTranslations("text_to_coloring");

  return {
    title: t("title"),
    description: t("description"),
  };
}

export default function TextToColoringPage() {
  return (
    <div className="min-h-screen bg-background">
      <TextToColoring />
    </div>
  );
}