import { getTranslations } from "next-intl/server";
import { Metadata } from "next";
import Pricing from "@/components/blocks/pricing";
import FAQ from "@/components/blocks/faq";
import { getLandingPage } from "@/services/page";

export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: string };
}): Promise<Metadata> {
  const t = await getTranslations("pricing");

  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/pricing`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/pricing`;
  }

  return {
    title: t("title"),
    description: t("description"),
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function PricingPage({
  params: { locale },
}: {
  params: { locale: string };
}) {
  const page = await getLandingPage(locale);
  const t = await getTranslations("pricing_faq");

  if (!page.pricing) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Pricing</h1>
          <p className="text-muted-foreground">Pricing information is not available.</p>
        </div>
      </div>
    );
  }

  // 创建专门的pricing FAQ数据，使用国际化翻译
  const faqItems = [];
  for (let index = 0; index < 8; index++) {
    try {
      const title = t(`items.${index}.title`);
      const description = t(`items.${index}.description`);
      faqItems.push({ title, description });
    } catch (error) {
      // 如果没有找到翻译，停止循环
      break;
    }
  }

  const pricingFAQ = {
    name: "pricing-faq",
    title: t("title"),
    description: t("description"),
    items: faqItems
  };

  return (
    <div className="min-h-screen bg-background">
      <Pricing
        pricing={page.pricing}
        activityBanner={page.header?.activity_banner}
      />
      <FAQ section={pricingFAQ} />
    </div>
  );
}
