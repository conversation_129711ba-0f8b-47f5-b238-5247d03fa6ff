import { NextResponse } from 'next/server';
import { locales } from '@/i18n/locale';
import { coloringCategories } from '@/lib/coloring-categories';
import { ColoringPagesService } from '@/lib/services/coloring-pages-service';

export async function GET() {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://makecoloring.com';

    // 基础页面路径
    const routes = [
      '', // 首页
      '/pricing', // 定价页面
      '/privacy-policy', // 隐私政策
      '/terms-of-service', // 服务条款
      '/printable-coloring-pages', // 新的涂色页面主页
      '/text-to-coloring-page', // 文字转涂色页面
      '/image-to-coloring-page', // 图片转涂色页面
    ];

    // 添加新的分类页面路径
    const categoryRoutes = coloringCategories.map(category => `/printable-coloring-pages/${category.seo_slug.replace(/^\//, '')}`);

    // 获取数据库中的所有已发布页面
    let dbPages: any[] = [];
    try {
      dbPages = await ColoringPagesService.getAllPublishedPages();
    } catch (error) {
      console.error('获取数据库页面失败:', error);
      dbPages = [];
    }

    // 添加数据库中的单页详情路径
    const dbPageRoutes = dbPages
      .filter(page => page.seo_slug)
      .map(page => ({
        url: `/${page.seo_slug.replace(/^\//, '')}`,
        lastModified: page.updated_at || page.created_at,
        priority: 0.8,
        changeFrequency: 'monthly'
      }));

    // 保留旧的分类路径以兼容现有链接
    const legacyCategoryRoutes = coloringCategories.map(category => `/free-coloring-pages/${category.slug}`);

    // 生成XML内容
    let xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml">`;

    // 为每个语言和路径生成URL
    locales.forEach(locale => {
      // 基础页面
      routes.forEach(route => {
        const url = locale === 'en'
          ? `${baseUrl}${route}`
          : `${baseUrl}/${locale}${route}`;

        let priority = 0.8;
        let changeFrequency = 'weekly';

        if (route === '') {
          priority = 1;
          changeFrequency = 'daily';
        } else if (route === '/free-coloring-pages' || route.startsWith('/free-coloring-pages/')) {
          priority = 0.9;
          changeFrequency = 'weekly';
        } else if (route === '/text-to-coloring-page' || route === '/image-to-coloring-page') {
          priority = 0.9;
          changeFrequency = 'weekly';
        } else if (route === '/pricing') {
          priority = 0.9;
          changeFrequency = 'monthly';
        } else if (route === '/privacy-policy' || route === '/terms-of-service') {
          priority = 0.5;
          changeFrequency = 'monthly';
        }

        xml += `
  <url>
    <loc>${url}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>${changeFrequency}</changefreq>
    <priority>${priority}</priority>`;

        // 添加hreflang标签
        locales.forEach(hrefLang => {
          const hrefUrl = hrefLang === 'en'
            ? `${baseUrl}${route}`
            : `${baseUrl}/${hrefLang}${route}`;
          xml += `
    <xhtml:link rel="alternate" hreflang="${hrefLang}" href="${hrefUrl}" />`;
        });

        xml += `
  </url>`;
      });

      // 分类页面
      categoryRoutes.forEach(route => {
        const url = locale === 'en'
          ? `${baseUrl}${route}`
          : `${baseUrl}/${locale}${route}`;

        xml += `
  <url>
    <loc>${url}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.9</priority>`;

        locales.forEach(hrefLang => {
          const hrefUrl = hrefLang === 'en'
            ? `${baseUrl}${route}`
            : `${baseUrl}/${hrefLang}${route}`;
          xml += `
    <xhtml:link rel="alternate" hreflang="${hrefLang}" href="${hrefUrl}" />`;
        });

        xml += `
  </url>`;
      });



      // 数据库页面
      dbPageRoutes.forEach(pageInfo => {
        const url = locale === 'en'
          ? `${baseUrl}${pageInfo.url}`
          : `${baseUrl}/${locale}${pageInfo.url}`;

        xml += `
  <url>
    <loc>${url}</loc>
    <lastmod>${pageInfo.lastModified}</lastmod>
    <changefreq>${pageInfo.changeFrequency}</changefreq>
    <priority>${pageInfo.priority}</priority>`;

        locales.forEach(hrefLang => {
          const hrefUrl = hrefLang === 'en'
            ? `${baseUrl}${pageInfo.url}`
            : `${baseUrl}/${hrefLang}${pageInfo.url}`;
          xml += `
    <xhtml:link rel="alternate" hreflang="${hrefLang}" href="${hrefUrl}" />`;
        });

        xml += `
  </url>`;
      });



      // 旧版分类路径
      legacyCategoryRoutes.forEach(route => {
        const url = locale === 'en'
          ? `${baseUrl}${route}`
          : `${baseUrl}/${locale}${route}`;

        xml += `
  <url>
    <loc>${url}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>`;

        locales.forEach(hrefLang => {
          const hrefUrl = hrefLang === 'en'
            ? `${baseUrl}${route}`
            : `${baseUrl}/${hrefLang}${route}`;
          xml += `
    <xhtml:link rel="alternate" hreflang="${hrefLang}" href="${hrefUrl}" />`;
        });

        xml += `
  </url>`;
      });
    });

    xml += `
</urlset>`;

    return new NextResponse(xml, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600', // 缓存1小时
      },
    });

  } catch (error) {
    console.error('生成sitemap失败:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
