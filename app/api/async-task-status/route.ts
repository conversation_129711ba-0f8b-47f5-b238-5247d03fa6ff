import { NextRequest } from 'next/server';
import { respData, respErr } from '@/lib/resp';
import { getAsyncTask } from '@/models/async-task';

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const taskUuid = url.searchParams.get('taskUuid');
    
    if (!taskUuid) {
      return respErr('Missing taskUuid parameter');
    }

    // 获取异步任务状态
    const asyncTask = await getAsyncTask(taskUuid);
    
    if (!asyncTask) {
      return respErr('Task not found');
    }

    // 返回任务状态和结果
    return respData({
      uuid: asyncTask.uuid,
      status: asyncTask.status,
      prompt: asyncTask.prompt,
      user_uuid: asyncTask.user_uuid,
      result_images: asyncTask.result_images || [],
      error_message: asyncTask.error_message,
      completed_at: asyncTask.completed_at,
      created_at: asyncTask.created_at,
      duration_seconds: asyncTask.duration_seconds
    });

  } catch (error) {
    console.error('❌ Error checking async task status:', error);
    return respErr('Failed to check task status');
  }
}
