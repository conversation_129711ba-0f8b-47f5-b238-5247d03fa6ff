import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';
import { verifyPayPalWebhook } from '@/lib/paypal-webhook-verify';
import { paypalLogger, PayPalOperation } from '@/lib/paypal-logger';
import { findOrderByPayPalId, updateOrderStatus, findOrderBySubscriptionId, updateOrderSubscriptionStatus, findOrderByOrderNo, updateOrderSubscriptionId } from '@/models/order';
import { getSupabaseClient } from '@/models/db';
import { increaseCredits, CreditsTransType } from '@/services/credit';
import { handleSubscriptionRecurringPayment, updateSubscriptionStatus, SubscriptionStatus } from '@/services/subscription';

/**
 * PayPal Webhook处理API
 * 处理PayPal发送的各种事件通知
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  const context = paypalLogger.createContext({ requestId: crypto.randomUUID() });

  try {
    paypalLogger.info(PayPalOperation.WEBHOOK_PROCESS, 'PayPal webhook接收开始', undefined, context);

    // 1. 获取请求数据
    const body = await request.text();
    const headers = Object.fromEntries(request.headers.entries());

    // 2. 验证webhook签名
    const webhookId = process.env.PAYPAL_WEBHOOK_ID!;
    if (!webhookId) {
      return NextResponse.json({ error: 'Webhook ID not configured' }, { status: 500 });
    }

    const isValid = await verifyPayPalWebhook(headers as any, body, webhookId);

    if (!isValid) {
      return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
    }

    // 3. 解析事件数据
    const event = JSON.parse(body);

    // 4. 检查事件是否已处理（防重复）
    if (await isEventProcessed(event.id)) {
      return NextResponse.json({
        success: true,
        message: 'Event already processed',
        processingTime: Date.now() - startTime
      });
    }

    // 5. 处理不同类型的事件
    await processWebhookEvent(event);

    // 6. 记录事件已处理
    await markEventAsProcessed(event.id);

    const processingTime = Date.now() - startTime;

    return NextResponse.json({
      success: true,
      processingTime
    });

  } catch (error: any) {
    return NextResponse.json({
      error: 'Webhook processing failed',
      message: error.message
    }, { status: 500 });
  }
}

/**
 * 处理不同类型的PayPal webhook事件
 */
async function processWebhookEvent(event: any) {
  // 处理订阅相关事件
  if (event.event_type.startsWith('BILLING.SUBSCRIPTION.')) {
    await processSubscriptionWebhook(event.event_type, event.resource);
    return;
  }

  // 处理支付相关事件
  if (event.event_type.startsWith('PAYMENT.SALE.')) {
    await processPaymentSaleWebhook(event.event_type, event.resource);
    return;
  }

  // 处理一次性支付事件
  switch (event.event_type) {
    case 'CHECKOUT.ORDER.APPROVED':
      await handleOrderApproved(event);
      break;

    case 'PAYMENT.CAPTURE.COMPLETED':
      await handlePaymentCaptured(event);
      break;

    case 'PAYMENT.CAPTURE.DENIED':
    case 'PAYMENT.CAPTURE.FAILED':
      await handlePaymentFailed(event);
      break;

    default:
      // 记录未处理的事件类型
      paypalLogger.info(PayPalOperation.WEBHOOK_PROCESS, '未处理的事件类型', {
        eventType: event.event_type,
        eventId: event.id
      });
      break;
  }
}

/**
 * 处理订单批准事件
 */
async function handleOrderApproved(event: any) {
  const resource = event.resource;
  const orderId = resource.id;

  if (!orderId) {
    return;
  }

  // 查找本地订单
  const order = await findOrderByPayPalId(orderId);
  if (!order) {
    return;
  }

  // 订单批准事件通常不需要特殊处理，等待CAPTURE事件
}

/**
 * 处理支付捕获完成事件
 */
async function handlePaymentCaptured(event: any) {
  const resource = event.resource;
  const orderId = resource.supplementary_data?.related_ids?.order_id;

  if (!orderId) {
    return;
  }

  // 查找本地订单
  const order = await findOrderByPayPalId(orderId);
  if (!order) {
    return;
  }

  // 检查订单状态，防止重复处理
  if (order.status === 'paid') {
    // 订单已支付，记录重复处理尝试但不抛出错误
    paypalLogger.info(PayPalOperation.WEBHOOK_PROCESS, '订单已支付，跳过重复处理', {
      orderNo: order.order_no,
      currentStatus: order.status
    });
    return;
  }

  // 更新订单状态
  const paidDetail = {
    ...resource,
    event_type: event.event_type,
    event_id: event.id,
    processed_at: new Date().toISOString()
  };

  await updateOrderStatus(
    order.order_no,
    'paid',
    new Date().toISOString(),
    resource.payee?.email_address || '',
    JSON.stringify(paidDetail)
  );

  // 增加用户积分
  if (order.user_uuid && order.credits > 0) {
    await increaseCredits({
      user_uuid: order.user_uuid,
      trans_type: CreditsTransType.OrderPay,
      credits: order.credits,
      expired_at: order.expired_at,
      order_no: order.order_no
    });
  }
}

/**
 * 处理支付失败事件
 */
async function handlePaymentFailed(event: any) {
  const resource = event.resource;
  const orderId = resource.supplementary_data?.related_ids?.order_id;

  if (!orderId) {
    return;
  }

  // 查找本地订单
  const order = await findOrderByPayPalId(orderId);
  if (!order) {
    return;
  }

  // 更新订单状态为失败
  const failedDetail = {
    ...resource,
    event_type: event.event_type,
    event_id: event.id,
    processed_at: new Date().toISOString()
  };

  await updateOrderStatus(
    order.order_no,
    'failed',
    new Date().toISOString(),
    '',
    JSON.stringify(failedDetail)
  );
}

/**
 * 处理订阅相关的webhook事件
 */
async function processSubscriptionWebhook(event_type: string, resource: any) {
  switch (event_type) {
    case 'BILLING.SUBSCRIPTION.CREATED':
      await handleSubscriptionCreated(resource);
      break;
    case 'BILLING.SUBSCRIPTION.ACTIVATED':
      await handleSubscriptionActivated(resource);
      break;
    case 'BILLING.SUBSCRIPTION.UPDATED':
      await handleSubscriptionUpdated(resource);
      break;
    case 'BILLING.SUBSCRIPTION.PAYMENT.COMPLETED':
      await handleSubscriptionPaymentCompleted(resource);
      break;
    case 'BILLING.SUBSCRIPTION.RENEWED':
      await handleSubscriptionRenewed(resource);
      break;
    case 'BILLING.SUBSCRIPTION.PAYMENT.FAILED':
      await handleSubscriptionPaymentFailed(resource);
      break;
    case 'BILLING.SUBSCRIPTION.CANCELLED':
      await handleSubscriptionCancelled(resource);
      break;
    case 'BILLING.SUBSCRIPTION.SUSPENDED':
      await handleSubscriptionSuspended(resource);
      break;
    case 'BILLING.SUBSCRIPTION.RE-ACTIVATED':
      await handleSubscriptionReactivated(resource);
      break;
    case 'BILLING.SUBSCRIPTION.EXPIRED':
      await handleSubscriptionExpired(resource);
      break;
    default:
      // 记录未处理的订阅事件类型
      paypalLogger.info(PayPalOperation.WEBHOOK_PROCESS, '未处理的订阅事件类型', {
        eventType: event_type,
        subscriptionId: resource.id
      });
      break;
  }
}

/**
 * 处理支付相关的webhook事件
 */
async function processPaymentSaleWebhook(event_type: string, resource: any) {
  switch (event_type) {
    case 'PAYMENT.SALE.COMPLETED':
      await handlePaymentSaleCompleted(resource);
      break;
    case 'PAYMENT.SALE.REFUNDED':
      await handlePaymentSaleRefunded(resource);
      break;
    case 'PAYMENT.SALE.REVERSED':
      await handlePaymentSaleReversed(resource);
      break;
    default:
      // 记录未处理的支付事件类型
      paypalLogger.info(PayPalOperation.WEBHOOK_PROCESS, '未处理的支付事件类型', {
        eventType: event_type,
        saleId: resource.id
      });
      break;
  }
}

/**
 * 处理订阅创建事件
 */
async function handleSubscriptionCreated(resource: any) {
  const subscriptionId = resource.id;

  if (!subscriptionId) {
    return;
  }

  // 查找本地订单（通过custom_id或subscription_id）
  let order = null;

  // 首先尝试通过custom_id查找（custom_id应该是我们的order_no）
  if (resource.custom_id) {
    order = await findOrderByOrderNo(resource.custom_id);
  }

  // 如果通过custom_id没找到，尝试通过subscription_id查找
  if (!order) {
    order = await findOrderBySubscriptionId(subscriptionId);
  }

  if (!order) {
    return;
  }

  // 对于CREATED事件，我们只更新订阅ID和状态，不分配积分
  // 因为用户还没有完成支付，订阅状态是APPROVAL_PENDING
  if (resource.status === 'APPROVAL_PENDING') {
    // 更新订单的PayPal订阅ID（如果还没有设置）
    if (!order.paypal_subscription_id) {
      await updateOrderSubscriptionId(order.order_no, subscriptionId);
    }
  } else if (resource.status === 'ACTIVE') {
    // 如果创建时就是ACTIVE状态，按激活处理
    await handleSubscriptionActivated(resource);
  }
}

/**
 * 处理订阅激活事件
 */
async function handleSubscriptionActivated(resource: any) {
  const subscriptionId = resource.id;

  if (!subscriptionId) {
    return;
  }

  // 查找本地订单
  const order = await findOrderBySubscriptionId(subscriptionId);
  if (!order) {
    return;
  }

  // 检查订单状态，防止重复处理
  if (order.status === 'paid') {
    // 订单已激活，记录重复处理尝试但不抛出错误
    paypalLogger.info(PayPalOperation.WEBHOOK_PROCESS, '订阅已激活，跳过重复处理', {
      orderNo: order.order_no,
      subscriptionId,
      currentStatus: order.status
    });
    return;
  }

  // 更新订单状态
  await updateOrderSubscriptionStatus(
    order.order_no,
    subscriptionId,
    'ACTIVE',
    resource.billing_info?.next_billing_time,
    JSON.stringify({
      subscription_activated: true,
      subscription_details: resource,
      processed_at: new Date().toISOString(),
      processed_by: 'webhook'
    }),
    resource.plan_id || order.paypal_plan_id
  );

  // 增加用户积分（首次激活）
  if (order.user_uuid && order.credits > 0) {
    await increaseCredits({
      user_uuid: order.user_uuid,
      trans_type: CreditsTransType.OrderPay,
      credits: order.credits,
      expired_at: resource.billing_info?.next_billing_time || order.expired_at,
      order_no: order.order_no
    });
  }
}

/**
 * 处理订阅更新事件
 */
async function handleSubscriptionUpdated(resource: any) {
  const subscriptionId = resource.id;

  if (!subscriptionId) {
    return;
  }

  // 查找本地订单
  const order = await findOrderBySubscriptionId(subscriptionId);
  if (!order) {
    return;
  }

  // 更新订阅信息
  await updateOrderSubscriptionStatus(
    order.order_no,
    subscriptionId,
    resource.status || 'ACTIVE',
    resource.billing_info?.next_billing_time,
    JSON.stringify({
      subscription_updated: true,
      subscription_details: resource,
      processed_at: new Date().toISOString(),
      processed_by: 'webhook'
    }),
    resource.plan_id || order.paypal_plan_id
  );

  // 同时更新新的订阅状态字段
  await updateSubscriptionStatus(
    subscriptionId,
    resource.status === 'ACTIVE' ? SubscriptionStatus.ACTIVE :
    resource.status === 'SUSPENDED' ? SubscriptionStatus.SUSPENDED :
    resource.status === 'CANCELLED' ? SubscriptionStatus.CANCELLED :
    SubscriptionStatus.ACTIVE,
    resource.billing_info?.next_billing_time
  );
}

/**
 * 处理订阅支付完成事件
 */
async function handleSubscriptionPaymentCompleted(resource: any) {
  // 对于BILLING.SUBSCRIPTION.PAYMENT.COMPLETED事件，订阅ID在resource.id字段
  const subscriptionId = resource.id;

  if (!subscriptionId) {
    return;
  }

  // 查找本地订单（原始订阅订单）
  const originalOrder = await findOrderBySubscriptionId(subscriptionId);
  if (!originalOrder) {
    return;
  }

  // 检查订单状态 - 只有已支付的订阅订单才能处理周期性支付
  if (originalOrder.status !== 'paid') {
    paypalLogger.info(PayPalOperation.WEBHOOK_PROCESS, '订阅订单未激活，跳过支付完成处理', {
      orderNo: originalOrder.order_no,
      subscriptionId,
      currentStatus: originalOrder.status
    });
    return;
  }

  // 检查是否为首次支付还是周期性支付
  // 通过查询是否已有周期性订单来判断
  const supabase = getSupabaseClient();
  const { data: recurringOrders, error: countError } = await supabase
    .from("orders")
    .select("billing_cycle_count")
    .eq("parent_order_no", originalOrder.order_no)
    .order("billing_cycle_count", { ascending: false })
    .limit(1);

  if (countError) {
    throw countError;
  }

  const hasRecurringOrders = recurringOrders && recurringOrders.length > 0;

  if (!hasRecurringOrders) {
    // 首次支付完成 - 更新原订单的支付详情
    const paymentDetail = {
      first_payment_completed: true,
      payment_details: resource,
      event_type: 'BILLING.SUBSCRIPTION.PAYMENT.COMPLETED',
      processed_at: new Date().toISOString(),
      processed_by: 'webhook'
    };

    // 更新订单的支付详情
    const { error: updateError } = await supabase
      .from("orders")
      .update({
        paid_detail: JSON.stringify(paymentDetail),
        next_billing_time: resource.billing_info?.next_billing_time
      })
      .eq("order_no", originalOrder.order_no);

    if (updateError) {
      throw updateError;
    }
  } else {
    // 周期性支付 - 创建新的周期性订单
    await handleSubscriptionRecurringPayment(subscriptionId, {
      event_type: 'BILLING.SUBSCRIPTION.PAYMENT.COMPLETED',
      payment_details: resource,
      processed_at: new Date().toISOString(),
      processed_by: 'webhook'
    });
  }
}

/**
 * 处理订阅续订事件（BILLING.SUBSCRIPTION.RENEWED）
 * 这是周期性扣费的关键事件，表示新一轮扣款
 */
async function handleSubscriptionRenewed(resource: any) {
  const subscriptionId = resource.id;

  if (!subscriptionId) {
    return;
  }

  paypalLogger.info(PayPalOperation.WEBHOOK_PROCESS, '处理订阅续订事件', {
    subscriptionId,
    status: resource.status,
    nextBillingTime: resource.billing_info?.next_billing_time
  });

  // 查找本地订单
  const order = await findOrderBySubscriptionId(subscriptionId);
  if (!order) {
    paypalLogger.info(PayPalOperation.WEBHOOK_PROCESS, '未找到对应的订阅订单', {
      subscriptionId
    });
    return;
  }

  // 检查订单状态 - 只有已支付的订阅订单才能处理续订
  if (order.status !== 'paid') {
    paypalLogger.info(PayPalOperation.WEBHOOK_PROCESS, '订阅订单未激活，跳过续订处理', {
      orderNo: order.order_no,
      subscriptionId,
      currentStatus: order.status
    });
    return;
  }

  // 创建周期性订单记录
  await handleSubscriptionRecurringPayment(subscriptionId, {
    event_type: 'BILLING.SUBSCRIPTION.RENEWED',
    renewal_details: resource,
    processed_at: new Date().toISOString(),
    processed_by: 'webhook'
  });

  // 更新原订单的下次计费时间
  const supabase = getSupabaseClient();
  const { error: updateError } = await supabase
    .from("orders")
    .update({
      next_billing_time: resource.billing_info?.next_billing_time
    })
    .eq("order_no", order.order_no);

  if (updateError) {
    paypalLogger.error(PayPalOperation.WEBHOOK_PROCESS, '更新订单下次计费时间失败', {
      orderNo: order.order_no,
      error: updateError
    });
  }
}

/**
 * 处理订阅支付失败事件
 */
async function handleSubscriptionPaymentFailed(resource: any) {
  const subscriptionId = resource.id;

  if (!subscriptionId) {
    return;
  }

  paypalLogger.error(PayPalOperation.WEBHOOK_PROCESS, '订阅支付失败', {
    subscriptionId,
    status: resource.status,
    failureReason: resource.failure_reason
  });

  // 查找本地订单
  const order = await findOrderBySubscriptionId(subscriptionId);
  if (!order) {
    return;
  }

  // 更新订阅状态为暂停（PayPal通常会暂停失败的订阅）
  await updateSubscriptionStatus(subscriptionId, SubscriptionStatus.SUSPENDED);

  // 更新订单状态信息
  await updateOrderSubscriptionStatus(
    order.order_no,
    subscriptionId,
    'SUSPENDED',
    undefined,
    JSON.stringify({
      subscription_payment_failed: true,
      failure_reason: resource.failure_reason,
      subscription_details: resource,
      processed_at: new Date().toISOString(),
      processed_by: 'webhook'
    }),
    resource.plan_id || order.paypal_plan_id
  );
}

/**
 * 处理订阅重新激活事件
 */
async function handleSubscriptionReactivated(resource: any) {
  const subscriptionId = resource.id;

  if (!subscriptionId) {
    return;
  }

  paypalLogger.info(PayPalOperation.WEBHOOK_PROCESS, '订阅重新激活', {
    subscriptionId,
    status: resource.status
  });

  // 查找本地订单
  const order = await findOrderBySubscriptionId(subscriptionId);
  if (!order) {
    return;
  }

  // 更新订阅状态为活跃
  await updateSubscriptionStatus(subscriptionId, SubscriptionStatus.ACTIVE, resource.billing_info?.next_billing_time);

  // 更新订单状态信息
  await updateOrderSubscriptionStatus(
    order.order_no,
    subscriptionId,
    'ACTIVE',
    resource.billing_info?.next_billing_time,
    JSON.stringify({
      subscription_reactivated: true,
      subscription_details: resource,
      processed_at: new Date().toISOString(),
      processed_by: 'webhook'
    }),
    resource.plan_id || order.paypal_plan_id
  );
}

/**
 * 处理订阅过期事件
 */
async function handleSubscriptionExpired(resource: any) {
  const subscriptionId = resource.id;

  if (!subscriptionId) {
    return;
  }

  paypalLogger.info(PayPalOperation.WEBHOOK_PROCESS, '订阅已过期', {
    subscriptionId,
    status: resource.status
  });

  // 查找本地订单
  const order = await findOrderBySubscriptionId(subscriptionId);
  if (!order) {
    return;
  }

  // 更新订阅状态为过期
  await updateSubscriptionStatus(subscriptionId, SubscriptionStatus.EXPIRED);

  // 更新订单状态信息
  await updateOrderSubscriptionStatus(
    order.order_no,
    subscriptionId,
    'EXPIRED',
    undefined,
    JSON.stringify({
      subscription_expired: true,
      subscription_details: resource,
      processed_at: new Date().toISOString(),
      processed_by: 'webhook'
    }),
    resource.plan_id || order.paypal_plan_id
  );
}

/**
 * 处理支付完成事件（PAYMENT.SALE.COMPLETED）
 * 这是订阅付款完成的另一个重要事件
 */
async function handlePaymentSaleCompleted(resource: any) {
  const saleId = resource.id;
  const subscriptionId = resource.billing_agreement_id;

  if (!saleId) {
    return;
  }

  paypalLogger.info(PayPalOperation.WEBHOOK_PROCESS, '处理支付完成事件', {
    saleId,
    subscriptionId,
    amount: resource.amount?.total,
    currency: resource.amount?.currency
  });

  // 如果有订阅ID，说明这是订阅支付
  if (subscriptionId) {
    // 查找本地订单
    const order = await findOrderBySubscriptionId(subscriptionId);
    if (!order) {
      return;
    }

    // 检查是否为首次支付还是周期性支付
    const supabase = getSupabaseClient();
    const { data: recurringOrders, error: countError } = await supabase
      .from("orders")
      .select("billing_cycle_count")
      .eq("parent_order_no", order.order_no)
      .order("billing_cycle_count", { ascending: false })
      .limit(1);

    if (countError) {
      throw countError;
    }

    const hasRecurringOrders = recurringOrders && recurringOrders.length > 0;

    if (!hasRecurringOrders && order.status !== 'paid') {
      // 首次支付 - 激活订阅
      await updateOrderStatus(
        order.order_no,
        'paid',
        new Date().toISOString(),
        resource.payee?.email || '',
        JSON.stringify({
          sale_completed: true,
          sale_details: resource,
          event_type: 'PAYMENT.SALE.COMPLETED',
          processed_at: new Date().toISOString(),
          processed_by: 'webhook'
        })
      );

      // 增加用户积分
      if (order.user_uuid && order.credits > 0) {
        await increaseCredits({
          user_uuid: order.user_uuid,
          trans_type: CreditsTransType.OrderPay,
          credits: order.credits,
          expired_at: order.expired_at,
          order_no: order.order_no
        });
      }
    } else {
      // 周期性支付
      await handleSubscriptionRecurringPayment(subscriptionId, {
        event_type: 'PAYMENT.SALE.COMPLETED',
        sale_details: resource,
        processed_at: new Date().toISOString(),
        processed_by: 'webhook'
      });
    }
  }
}

/**
 * 处理支付退款事件
 */
async function handlePaymentSaleRefunded(resource: any) {
  const saleId = resource.id;
  const parentPaymentId = resource.parent_payment;

  if (!saleId) {
    return;
  }

  paypalLogger.info(PayPalOperation.WEBHOOK_PROCESS, '处理支付退款事件', {
    saleId,
    parentPaymentId,
    refundAmount: resource.amount?.total,
    currency: resource.amount?.currency
  });

  // 这里可以根据需要实现退款逻辑
  // 例如：减少用户积分、更新订单状态等
  // 由于退款逻辑比较复杂，这里只记录事件
}

/**
 * 处理支付撤销事件
 */
async function handlePaymentSaleReversed(resource: any) {
  const saleId = resource.id;
  const parentPaymentId = resource.parent_payment;

  if (!saleId) {
    return;
  }

  paypalLogger.info(PayPalOperation.WEBHOOK_PROCESS, '处理支付撤销事件', {
    saleId,
    parentPaymentId,
    reversalAmount: resource.amount?.total,
    currency: resource.amount?.currency
  });

  // 这里可以根据需要实现撤销逻辑
  // 例如：减少用户积分、暂停订阅等
  // 由于撤销逻辑比较复杂，这里只记录事件
}

/**
 * 处理订阅取消事件
 */
async function handleSubscriptionCancelled(resource: any) {
  const subscriptionId = resource.id;
  if (!subscriptionId) {
    return;
  }

  // 查找本地订单
  const order = await findOrderBySubscriptionId(subscriptionId);
  if (!order) {
    return;
  }

  // 使用新的订阅管理服务更新状态
  await updateSubscriptionStatus(subscriptionId, SubscriptionStatus.CANCELLED);

  // 同时更新原有字段以保持兼容性
  await updateOrderSubscriptionStatus(
    order.order_no,
    subscriptionId,
    'CANCELLED',
    undefined,
    JSON.stringify({
      subscription_cancelled: true,
      subscription_details: resource,
      processed_at: new Date().toISOString(),
      processed_by: 'webhook'
    }),
    resource.plan_id || order.paypal_plan_id
  );
}

/**
 * 处理订阅暂停事件
 */
async function handleSubscriptionSuspended(resource: any) {
  const subscriptionId = resource.id;
  if (!subscriptionId) {
    return;
  }

  // 查找本地订单
  const order = await findOrderBySubscriptionId(subscriptionId);
  if (!order) {
    return;
  }

  // 使用新的订阅管理服务更新状态
  await updateSubscriptionStatus(subscriptionId, SubscriptionStatus.SUSPENDED);

  // 同时更新原有字段以保持兼容性
  await updateOrderSubscriptionStatus(
    order.order_no,
    subscriptionId,
    'SUSPENDED',
    undefined,
    JSON.stringify({
      subscription_suspended: true,
      subscription_details: resource,
      processed_at: new Date().toISOString(),
      processed_by: 'webhook'
    }),
    resource.plan_id || order.paypal_plan_id
  );
}

/**
 * 检查webhook事件是否已处理
 * 使用数据库存储机制，支持多实例部署和服务重启
 */
async function isEventProcessed(eventId: string): Promise<boolean> {
  try {
    const supabase = getSupabaseClient();

    // 查询是否存在该事件记录
    const { data, error } = await supabase
      .from('webhook_events')
      .select('id')
      .eq('event_id', eventId)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows found
      console.error('检查webhook事件处理状态失败:', error);
      return false; // 出错时允许处理，避免丢失事件
    }

    return !!data; // 如果找到记录，说明已处理
  } catch (error) {
    console.error('检查webhook事件处理状态异常:', error);
    return false; // 出错时允许处理，避免丢失事件
  }
}

async function markEventAsProcessed(eventId: string): Promise<void> {
  try {
    const supabase = getSupabaseClient();

    // 插入事件处理记录
    const { error } = await supabase
      .from('webhook_events')
      .insert({
        event_id: eventId,
        processed_at: new Date().toISOString(),
        created_at: new Date().toISOString()
      });

    if (error) {
      console.error('标记webhook事件已处理失败:', error);
      // 不抛出异常，避免影响主流程
    }
  } catch (error) {
    console.error('标记webhook事件已处理异常:', error);
    // 不抛出异常，避免影响主流程
  }
}
