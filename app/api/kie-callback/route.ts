import { NextRequest } from 'next/server';
import { respData, respErr } from '@/lib/resp';
import { getAsyncTask, updateAsyncTask, AsyncTaskStatus } from '@/models/async-task';
import { insertPhotos } from '@/models/photo';
import { newStorage } from '@/lib/storage';
import { getUuid } from '@/lib/hash';
import { getIsoTimestr } from '@/lib/time';
import { Photo } from '@/types/photo';
import { generateStorageKey } from '@/lib/file-naming';
import { decreaseCredits, CreditsTransType } from '@/services/credit';

export async function POST(req: NextRequest) {
  try {
    console.log('📥 Kie.ai callback received');
    
    // 获取任务UUID
    const url = new URL(req.url);
    const taskUuid = url.searchParams.get('taskUuid');
    
    if (!taskUuid) {
      console.error('❌ Missing taskUuid in callback URL');
      return respErr('Missing taskUuid parameter');
    }

    // 获取回调数据
    const callbackData = await req.json();
    console.log('📋 Kie.ai callback data:', {
      taskUuid,
      success: callbackData.success,
      hasData: !!callbackData.data,
      message: callbackData.message,
      fullCallbackData: JSON.stringify(callbackData, null, 2) // 完整的callback数据用于调试
    });

    // 获取异步任务
    const asyncTask = await getAsyncTask(taskUuid);
    if (!asyncTask) {
      console.error('❌ Async task not found:', taskUuid);
      return respErr('Task not found');
    }

    if (asyncTask.status === AsyncTaskStatus.Success) {
      console.log('ℹ️ Task already completed:', taskUuid);
      return respData({ message: 'Task already completed' });
    }

    try {
      // 防止重复处理：检查任务状态
      const latestTask = await getAsyncTask(taskUuid);
      if (!latestTask) {
        console.error('❌ Task not found during callback processing:', taskUuid);
        return respErr('Task not found');
      }

      if (latestTask.status === 'success' || latestTask.status === 'failed') {
        console.log('⚠️ Task already processed, ignoring callback:', taskUuid, latestTask.status);
        return respData({ message: 'Task already processed' });
      }

      // 详细检查callback数据结构
      console.log('🔍 Callback success check:', {
        'callbackData.code': callbackData.code,
        'callbackData.msg': callbackData.msg,
        'callbackData.data exists': !!callbackData.data,
        'data keys': callbackData.data ? Object.keys(callbackData.data) : 'no data',
        'has data.info.result_urls': !!(callbackData.data?.info?.result_urls),
        'has data.resultUrls': !!(callbackData.data?.resultUrls),
        'has data.images': !!(callbackData.data?.images),
        'direct resultUrls': !!callbackData.resultUrls,
        'direct images': !!callbackData.images
      });

      // 支持kie.ai的实际callback格式
      // 格式1: {"code": 200, "data": {"info": {"result_urls": [...]}}}
      // 格式2: {"success": true, "data": {"resultUrls": [...]}}
      // 格式3: {"resultUrls": [...]} (直接在顶层)

      const kieFormatData = callbackData.data?.info?.result_urls;
      const standardFormatData = callbackData.data?.resultUrls || callbackData.data?.images;
      const topLevelData = callbackData.resultUrls || callbackData.images;

      const hasValidData = kieFormatData || standardFormatData || topLevelData;

      // 判断是否成功：code=200 或 success=true 或有有效数据
      const isSuccess = callbackData.code === 200 ||
                       callbackData.success === true ||
                       callbackData.success === 'true' ||
                       (callbackData.success !== false && hasValidData);

      if (isSuccess && hasValidData) {
        // 处理成功的回调
        console.log('✅ Processing successful callback for task:', taskUuid);

        // 统一数据格式为 {resultUrls: [...]}
        let dataToProcess;
        if (kieFormatData) {
          // kie.ai格式：data.info.result_urls -> resultUrls
          dataToProcess = { resultUrls: kieFormatData };
        } else if (standardFormatData) {
          // 标准格式：data.resultUrls 或 data.images
          dataToProcess = callbackData.data;
        } else {
          // 顶层格式：直接的resultUrls或images
          dataToProcess = {
            resultUrls: callbackData.resultUrls,
            images: callbackData.images
          };
        }

        await handleSuccessCallback(latestTask, dataToProcess, callbackData);
        console.log('✅ Kie.ai callback processed successfully:', taskUuid);
      } else {
        // 处理失败的回调
        console.log('❌ Processing failed callback for task:', taskUuid, {
          reason: !isSuccess ? 'success check failed' : 'no valid data found',
          code: callbackData.code,
          success: callbackData.success,
          hasData: !!callbackData.data,
          hasValidData
        });
        await handleFailureCallback(latestTask, callbackData.msg || callbackData.message || callbackData.error || 'Generation failed', callbackData);
        console.log('❌ Kie.ai generation failed:', taskUuid, callbackData.msg || callbackData.message || callbackData.error);
      }

      return respData({ message: 'Callback processed successfully' });

    } catch (error) {
      console.error('❌ Error processing Kie.ai callback:', error);

      // 更新任务状态为失败
      await updateAsyncTask(taskUuid, {
        status: AsyncTaskStatus.Failed as any,
        error_message: error instanceof Error ? error.message : 'Callback processing failed',
        callback_data: {
          ...callbackData,
          processing_error: error instanceof Error ? error.message : String(error)
        },
        completed_at: getIsoTimestr()
      });

      return respErr('Failed to process callback');
    }

  } catch (error) {
    console.error('❌ Kie.ai callback error:', error);
    return respErr('Callback processing error');
  }
}

async function handleSuccessCallback(asyncTask: any, data: any, fullCallbackData?: any) {
  console.log('🎯 Processing successful Kie.ai callback:', {
    taskUuid: asyncTask.uuid,
    hasImages: !!data.images,
    imageCount: data.images?.length || 0
  });

  const storage = newStorage();
  const processedImages: Photo[] = [];

  // 处理生成的图片 - 支持新的 resultUrls 格式
  const imageUrls = data.resultUrls || data.images || [];

  if (Array.isArray(imageUrls) && imageUrls.length > 0) {
    for (let i = 0; i < imageUrls.length; i++) {
      const imageSource = imageUrls[i];

      try {
        let uploadResult;
        // 使用新的文件命名规则：${date}/kie_${uuid}_${index}.png
        const key = generateStorageKey('kie', asyncTask.uuid, i);

        // 处理不同的图片数据格式
        if (imageSource && imageSource.base64) {
          // base64格式 - 直接上传
          console.log(`📤 Uploading base64 image ${i + 1} from Kie.ai`);
          const body = Buffer.from(imageSource.base64, 'base64');

          uploadResult = await storage.uploadFile({
            body,
            key,
            contentType: 'image/png',
            disposition: 'inline'
          });
        } else {
          // URL格式 - 下载后上传
          let imageUrl = '';

          if (typeof imageSource === 'string') {
            // 直接是URL字符串（新格式 resultUrls）
            imageUrl = imageSource;
          } else if (imageSource && imageSource.url) {
            // 对象格式，包含url字段（旧格式 images）
            imageUrl = imageSource.url;
          }

          if (imageUrl && imageUrl.startsWith('http')) {
            console.log(`📥 Downloading and uploading image ${i + 1} from Kie.ai:`, imageUrl);

            uploadResult = await storage.downloadAndUpload({
              url: imageUrl,
              key,
              contentType: 'image/png',
              disposition: 'inline'
            });
          }
        }

        // 处理上传结果
        if (uploadResult && uploadResult.url) {
          console.log(`✅ Image ${i + 1} uploaded successfully:`, uploadResult.url);

          const photo: Photo = {
            uuid: getUuid(),
            user_uuid: asyncTask.user_uuid,
            created_at: getIsoTimestr(),
            img_description: asyncTask.prompt || 'Generated by Kie.ai',
            img_url: uploadResult.url,
            status: 'created',
            generation_type: asyncTask.task_type === 'image-to-image' ? 'image' : 'text'
          };

          processedImages.push(photo);
        } else {
          console.warn(`⚠️ Failed to upload image ${i + 1}: No upload result or URL`);
        }

      } catch (error) {
        console.error(`❌ Failed to process image ${i + 1}:`, error);
      }
    }
  }

  // 检查是否有成功处理的图片
  if (processedImages.length === 0) {
    console.error('❌ No images were successfully processed from Kie.ai callback');
    throw new Error('No images were successfully processed');
  }

  // 保存图片到数据库
  try {
    await insertPhotos(processedImages);
    console.log(`💾 Saved ${processedImages.length} photos to database`);
  } catch (error) {
    console.error('❌ Failed to save photos to database:', error);
    throw error;
  }

  // 生图成功后扣除积分
  if (asyncTask.user_uuid) {
    try {
      // 检查是否是开发环境测试模式
      const isTestingMode = process.env.NODE_ENV === 'development' && process.env.UNLIMITED_CREDITS_FOR_TESTING === 'true';

      if (!isTestingMode) {
        const creditsToDeduct = 3; // 涂色页生成的标准消耗
        await decreaseCredits({
          user_uuid: asyncTask.user_uuid,
          trans_type: CreditsTransType.ColoringGenerate,
          credits: creditsToDeduct,
        });
        console.log(`💰 Credits deducted after successful generation: ${creditsToDeduct} credits from user ${asyncTask.user_uuid}`);
      } else {
        console.log('🔧 Development mode: Skipping credits deduction for testing');
      }
    } catch (creditError) {
      console.error('❌ Failed to deduct credits after successful generation:', creditError);
      // 积分扣除失败不影响任务完成状态，但需要记录
    }
  }

  // 计算任务完成时间（秒）
  const completedAt = getIsoTimestr();
  const createdAt = new Date(asyncTask.created_at);
  const completedAtDate = new Date(completedAt);
  const durationSeconds = Math.round((completedAtDate.getTime() - createdAt.getTime()) / 1000);

  // 更新异步任务状态
  await updateAsyncTask(asyncTask.uuid, {
    status: AsyncTaskStatus.Success as any,
    result_images: processedImages.map(p => ({ url: p.img_url })),
    callback_data: fullCallbackData,
    completed_at: completedAt,
    duration_seconds: durationSeconds
  });

  console.log('✅ Kie.ai task completed successfully:', {
    taskUuid: asyncTask.uuid,
    imageCount: processedImages.length
  });
}

async function handleFailureCallback(asyncTask: any, errorMessage: string, fullCallbackData?: any) {
  console.log('❌ Processing failed Kie.ai callback:', {
    taskUuid: asyncTask.uuid,
    error: errorMessage,
    hasFullData: !!fullCallbackData
  });

  // 生成用户友好的错误消息
  let userFriendlyMessage = errorMessage;

  // 检查是否是内容违规相关的错误
  if (errorMessage && (
    errorMessage.toLowerCase().includes('content') ||
    errorMessage.toLowerCase().includes('policy') ||
    errorMessage.toLowerCase().includes('violation') ||
    errorMessage.toLowerCase().includes('inappropriate') ||
    errorMessage.toLowerCase().includes('unsafe') ||
    errorMessage.toLowerCase().includes('blocked') ||
    errorMessage.toLowerCase().includes('filtered')
  )) {
    userFriendlyMessage = 'Your input may violate content policies. Please adjust your input and try again.';
  } else if (errorMessage && (
    errorMessage.toLowerCase().includes('timeout') ||
    errorMessage.toLowerCase().includes('failed') ||
    errorMessage.toLowerCase().includes('error')
  )) {
    userFriendlyMessage = 'Generation failed due to technical issues. Please try again with a different input.';
  } else {
    userFriendlyMessage = 'Generation failed. Please adjust your input and try again.';
  }

  // 计算任务完成时间（秒）
  const completedAt = getIsoTimestr();
  const createdAt = new Date(asyncTask.created_at);
  const completedAtDate = new Date(completedAt);
  const durationSeconds = Math.round((completedAtDate.getTime() - createdAt.getTime()) / 1000);

  await updateAsyncTask(asyncTask.uuid, {
    status: AsyncTaskStatus.Failed as any,
    error_message: userFriendlyMessage, // 使用用户友好的错误消息
    callback_data: {
      ...fullCallbackData,
      original_error: errorMessage, // 保留原始错误消息用于调试
      error_timestamp: new Date().toISOString(),
      credits_refunded: true
    },
    completed_at: completedAt,
    duration_seconds: durationSeconds
  });
}

// 支持GET请求用于健康检查
export async function GET() {
  return respData({ 
    message: 'Kie.ai callback endpoint is ready',
    timestamp: new Date().toISOString()
  });
}
