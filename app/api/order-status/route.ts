import { NextRequest, NextResponse } from 'next/server';
import { findOrderByPayPalId, findOrderByOrderNo } from "@/models/order";
import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";

/**
 * 订单状态查询API
 * 用于查询订单的当前状态，特别是支付处理状态
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const paypal_order_id = searchParams.get('paypal_order_id');
    const order_no = searchParams.get('order_no');

    if (!paypal_order_id && !order_no) {
      return respErr('缺少订单ID参数');
    }

    // 获取当前用户UUID进行权限验证
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr('用户未登录');
    }

    // 根据参数查找订单
    let order;
    if (paypal_order_id) {
      order = await findOrderByPayPalId(paypal_order_id);
    } else if (order_no) {
      order = await findOrderByOrderNo(order_no);
    }

    if (!order) {
      return respErr('订单不存在');
    }

    // 验证订单所有权
    if (order.user_uuid !== user_uuid) {
      return respErr('无权限访问此订单');
    }

    // 返回订单状态信息
    return respData({
      order_no: order.order_no,
      status: order.status,
      paypal_order_id: order.paypal_order_id,
      amount: order.amount,
      credits: order.credits,
      created_at: order.created_at,
      paid_at: order.paid_at,
      paid_email: order.paid_email,
      product_name: order.product_name,
      is_processed: order.status === 'paid',
      processing_time: order.paid_at ? 
        new Date(order.paid_at).getTime() - new Date(order.created_at).getTime() : null
    });

  } catch (error: any) {
    console.error('❌ 查询订单状态失败:', error);
    return respErr('查询订单状态失败');
  }
}
