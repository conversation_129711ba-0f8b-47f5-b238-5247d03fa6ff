import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { ColoringPagesService } from '@/lib/services/coloring-pages-service';
import { executeWithRetry } from '@/lib/database/connection';
import { ColoringPage } from '@/types/coloring-category';
import { v4 as uuidv4 } from 'uuid';

// 获取涂色页面列表（管理员）
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;
    
    const status = searchParams.get('status') as 'draft' | 'published' | 'archived' | undefined;
    const categoryId = searchParams.get('category');
    const source = searchParams.get('source') as 'static' | 'user_generated' | 'ai_generated' | undefined;
    
    const filters = {
      ...(status && { status }),
      ...(categoryId && { categoryId }),
      ...(source && { source })
    };
    
    const result = await ColoringPagesService.getColoringPages(filters, {
      limit,
      offset,
      orderBy: 'created_at',
      orderDirection: 'DESC'
    });
    
    return NextResponse.json({
      pages: result.pages,
      total: result.total,
      page,
      totalPages: Math.ceil(result.total / limit),
      hasMore: offset + limit < result.total
    });
    
  } catch (error) {
    console.error('Error fetching coloring pages for admin:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// 创建新的涂色页面
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      title,
      description,
      image_url,
      thumbnail_url,
      alt_text,
      category_id,
      subcategory_id,
      seo_slug,
      tags,
      difficulty_level,
      age_group,
      is_featured,
      is_premium,
      status,
      source,
      meta_title,
      meta_description
    } = body;

    // 验证必填字段
    if (!title || !image_url || !category_id || !seo_slug) {
      return NextResponse.json(
        { error: 'Missing required fields: title, image_url, category_id, seo_slug' },
        { status: 400 }
      );
    }

    // 创建涂色页面数据
    const pageData: Partial<ColoringPage> = {
      title,
      description,
      image_url,
      thumbnail_url,
      alt_text,
      category_id,
      subcategory_id,
      seo_slug: seo_slug.startsWith('/') ? seo_slug : `/${seo_slug}`,
      tags,
      difficulty_level,
      age_group,
      is_featured: is_featured || false,
      is_premium: is_premium || false,
      status: status || 'published',
      source: source || 'static',
      meta_title,
      meta_description,
      created_at: new Date().toISOString()
    };

    const pageId = await ColoringPagesService.createColoringPage(pageData);
    
    if (!pageId) {
      return NextResponse.json(
        { error: 'Failed to create coloring page' },
        { status: 500 }
      );
    }

    // 获取创建的页面
    const createdPage = await ColoringPagesService.getColoringPageById(pageId);
    
    return NextResponse.json({
      message: 'Coloring page created successfully',
      page: createdPage
    }, { status: 201 });
    
  } catch (error) {
    console.error('Error creating coloring page:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
