import { NextRequest } from 'next/server';
import { getSupabaseClient } from "@/models/db";
import { respData, respErr } from "@/lib/resp";

/**
 * 支付监控API
 * 用于管理员监控支付处理状态和潜在问题
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const timeRange = parseInt(searchParams.get('hours') || '24'); // 默认查看24小时内的数据

    const supabase = getSupabaseClient();
    const cutoffTime = new Date(Date.now() - timeRange * 60 * 60 * 1000).toISOString();

    // 1. 获取最近创建但未支付的订单（可能的webhook延迟）
    const { data: pendingOrders, error: pendingError } = await supabase
      .from('orders')
      .select('*')
      .eq('status', 'created')
      .gte('created_at', cutoffTime)
      .order('created_at', { ascending: false });

    if (pendingError) {
      console.error('获取待处理订单失败:', pendingError);
      return respErr('获取监控数据失败');
    }

    // 2. 获取最近支付成功的订单统计
    const { data: paidOrders, error: paidError } = await supabase
      .from('orders')
      .select('*')
      .eq('status', 'paid')
      .gte('created_at', cutoffTime)
      .order('created_at', { ascending: false });

    if (paidError) {
      console.error('获取已支付订单失败:', paidError);
      return respErr('获取监控数据失败');
    }

    // 3. 分析处理时间
    const processingTimes = paidOrders?.map(order => {
      if (order.created_at && order.paid_at) {
        return {
          order_no: order.order_no,
          processing_time: new Date(order.paid_at).getTime() - new Date(order.created_at).getTime(),
          created_at: order.created_at,
          paid_at: order.paid_at
        };
      }
      return null;
    }).filter(Boolean) || [];

    // 4. 识别可能的问题订单（创建超过5分钟但未支付）
    const problemOrders = pendingOrders?.filter(order => {
      const createdTime = new Date(order.created_at).getTime();
      const now = Date.now();
      return (now - createdTime) > 5 * 60 * 1000; // 5分钟
    }) || [];

    // 5. 计算统计数据
    const stats = {
      total_orders: (pendingOrders?.length || 0) + (paidOrders?.length || 0),
      pending_orders: pendingOrders?.length || 0,
      paid_orders: paidOrders?.length || 0,
      problem_orders: problemOrders.length,
      success_rate: paidOrders?.length ? 
        ((paidOrders.length / ((pendingOrders?.length || 0) + paidOrders.length)) * 100).toFixed(2) : '0',
      avg_processing_time: processingTimes.length ? 
        Math.round(processingTimes.reduce((sum, item) => sum + (item?.processing_time || 0), 0) / processingTimes.length / 1000) : 0,
      max_processing_time: processingTimes.length ? 
        Math.round(Math.max(...processingTimes.map(item => item?.processing_time || 0)) / 1000) : 0
    };

    return respData({
      stats,
      pending_orders: pendingOrders?.slice(0, 10) || [], // 最近10个待处理订单
      problem_orders: problemOrders.slice(0, 10) || [], // 最近10个问题订单
      recent_paid: paidOrders?.slice(0, 10) || [], // 最近10个已支付订单
      processing_times: processingTimes.slice(0, 20) || [], // 最近20个处理时间记录
      time_range_hours: timeRange
    });

  } catch (error: any) {
    console.error('❌ 支付监控数据获取失败:', error);
    return respErr('获取监控数据失败');
  }
}
