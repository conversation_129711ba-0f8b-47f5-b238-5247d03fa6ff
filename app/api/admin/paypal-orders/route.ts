import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { getSupabaseClient } from '@/models/db';

/**
 * 获取PayPal订单列表API
 * 仅限管理员使用
 */
export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    const session = await auth();
    const adminEmails = process.env.ADMIN_EMAILS?.split(",") || [];
    
    if (!session?.user?.email || !adminEmails.includes(session.user.email)) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const status = searchParams.get('status'); // 可选的状态过滤

    const offset = (page - 1) * limit;

    const supabase = getSupabaseClient();
    
    // 构建查询（现在所有订单都是PayPal订单）
    let query = supabase
      .from('orders')
      .select('*')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // 如果指定了状态，添加状态过滤
    if (status) {
      query = query.eq('status', status);
    }

    const { data: orders, error } = await query;

    if (error) {
      console.error('获取PayPal订单失败:', error);
      return NextResponse.json({ 
        error: '获取订单失败',
        message: error.message 
      }, { status: 500 });
    }

    // 获取总数
    let countQuery = supabase
      .from('orders')
      .select('*', { count: 'exact', head: true });

    if (status) {
      countQuery = countQuery.eq('status', status);
    }

    const { count, error: countError } = await countQuery;

    if (countError) {
      console.error('获取订单总数失败:', countError);
    }

    return NextResponse.json({
      success: true,
      orders: orders || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });

  } catch (error: any) {
    console.error('获取PayPal订单失败:', error);
    return NextResponse.json({ 
      error: '获取订单失败',
      message: error.message 
    }, { status: 500 });
  }
}
