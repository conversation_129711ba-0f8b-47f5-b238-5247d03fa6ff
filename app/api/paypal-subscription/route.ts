import { NextRequest, NextResponse } from 'next/server';
import { respData, respErr } from "@/lib/resp";
import { getUserEmail, getUserUuid } from "@/services/user";
import { insertOrder, updateOrderSubscriptionId } from "@/models/order";
import { Order } from "@/types/order";
import { findUserByUuid } from "@/models/user";
import { getSnowId } from "@/lib/hash";
import { paypalClient } from "@/lib/paypal-client";
import { getPayPalConfig, PAYPAL_SUBSCRIPTION_CONTEXT, buildPayPalUrls } from "@/lib/paypal-config";
import { getPayPalPlanId, isPayPalSubscriptionSupported } from "@/lib/paypal-plans";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // 验证请求参数
    const {
      product_id,        // 本地产品ID
      plan_id,           // PayPal Plan ID (可选，优先使用配置中的)
      product_name,
      credits,
      amount,
      currency,
      valid_months,
      returnUrl,
      cancelUrl
    } = body;

    // 支持两种方式：
    // 1. 传入product_id，从配置中获取PayPal Plan ID
    // 2. 直接传入plan_id（向后兼容）
    let finalPlanId;

    if (product_id) {
      // 方式1：使用product_id查找配置
      if (!isPayPalSubscriptionSupported(product_id)) {
        return respErr(`产品 ${product_id} 不支持PayPal订阅`);
      }

      finalPlanId = getPayPalPlanId(product_id);
      if (!finalPlanId) {
        return respErr(`未找到产品 ${product_id} 的PayPal订阅配置`);
      }

    } else if (plan_id) {
      // 方式2：直接使用传入的plan_id（向后兼容）
      finalPlanId = plan_id;
    } else {
      return respErr("缺少必需参数: product_id 或 plan_id");
    }

    if (!finalPlanId) {
      return respErr("无法确定PayPal Plan ID");
    }

    // 1. 验证用户登录
    const user_uuid = await getUserUuid();
    const user_email = await getUserEmail();

    if (!user_uuid || !user_email) {
      return NextResponse.json({ error: '请先登录' }, { status: 401 });
    }

    // 2. 验证用户存在
    const user = await findUserByUuid(user_uuid);
    if (!user) {
      return respErr("用户不存在");
    }

    // 3. 生成订单号
    const order_no = getSnowId();

    // 4. 创建本地订单（订阅类型）
    // 直接使用传入的参数（产品信息现在从前端配置获取）
    const orderCredits = credits;
    const orderAmount = amount || 0;
    const orderCurrency = currency || "usd";
    const orderValidMonths = valid_months || 12;
    const orderProductName = product_name;


    const order: Order = {
      order_no: order_no.toString(),
      created_at: new Date().toISOString(),
      user_uuid: user_uuid,
      user_email: user_email,
      amount: orderAmount,
      expired_at: new Date(Date.now() + orderValidMonths * 30 * 24 * 60 * 60 * 1000).toISOString(),
      status: "created",
      credits: orderCredits,
      currency: orderCurrency.toLowerCase(),
      product_id: product_id || finalPlanId,
      product_name: orderProductName,
      valid_months: orderValidMonths,
      order_type: 'subscription',
      paypal_plan_id: finalPlanId
    };

    await insertOrder(order);

    // 5. 构建PayPal订阅数据
    const config = getPayPalConfig();
    const { successUrl, cancelUrl: cancelUrlFinal } = buildPayPalUrls(returnUrl, cancelUrl);

    // 为订阅支付添加特殊标识，标记这是订阅支付
    const subscriptionSuccessUrl = successUrl.includes('?')
      ? `${successUrl}&payment_type=subscription&order_no=${order_no}`
      : `${successUrl}?payment_type=subscription&order_no=${order_no}`;

    const subscriptionData = {
      plan_id: finalPlanId,
      start_time: new Date(Date.now() + 60000).toISOString(), // 1分钟后开始
      subscriber: {
        email_address: user_email
      },
      application_context: {
        brand_name: config.projectName,
        locale: 'en-US',
        return_url: subscriptionSuccessUrl, // 使用带订阅标识的URL
        cancel_url: cancelUrlFinal,
        ...PAYPAL_SUBSCRIPTION_CONTEXT
      },
      custom_id: order_no.toString() // 关联本地订单
    };

    // 6. 创建PayPal订阅
    const subscriptionResult = await paypalClient.createSubscription(subscriptionData);

    // 7. 更新订单的PayPal订阅ID
    await updateOrderSubscriptionId(order_no.toString(), subscriptionResult.id);

    // 8. 返回订阅链接
    const approvalUrl = subscriptionResult.links?.find((link: any) => link.rel === 'approve')?.href;

    if (!approvalUrl) {
      throw new Error('PayPal未返回订阅链接');
    }

    return respData({
      success: true,
      subscription_id: subscriptionResult.id,
      orderNo: order_no.toString(),
      approvalUrl,
      subscription_status: subscriptionResult.status
    });

  } catch (error: any) {
    console.error('❌ PayPal订阅创建失败:', error);
    return respErr(`创建订阅失败: ${error.message}`);
  }
}


