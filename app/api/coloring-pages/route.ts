import { NextRequest } from "next/server";
import { respData, respErr } from "@/lib/resp";
import { ColoringPagesService } from '@/lib/services/coloring-pages-service';
import { getCategoryBySlug } from "@/lib/coloring-categories";

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const category = searchParams.get("category") || "latest";
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "12");

    // 验证分页参数
    if (page < 1 || limit < 1 || limit > 50) {
      return respErr("Invalid pagination parameters");
    }

    // 验证分类是否存在（除了latest）
    if (category !== "latest") {
      const categoryData = getCategoryBySlug(category);
      if (!categoryData) {
        return respErr("Category not found");
      }
    }

    const offset = (page - 1) * limit;

    // 构建筛选条件
    const filters = category === "latest" ? {} : { categoryId: category };

    // 从数据库获取数据
    const result = await ColoringPagesService.getColoringPages(filters, {
      limit,
      offset,
      orderBy: 'created_at',
      orderDirection: 'DESC'
    });

    return respData({
      success: true,
      pages: result.pages,
      total: result.total,
      current_page: page,
      total_pages: Math.ceil(result.total / limit),
      has_next: offset + limit < result.total,
      has_prev: page > 1
    });
  } catch (error) {
    console.error("Error fetching coloring pages:", error);
    return respErr("Failed to fetch coloring pages");
  }
}
