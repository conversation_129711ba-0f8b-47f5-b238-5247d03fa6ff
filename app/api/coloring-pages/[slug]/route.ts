import { NextRequest, NextResponse } from 'next/server';
import { ColoringPagesService } from '@/lib/services/coloring-pages-service';

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params;

    // 获取涂色页面
    let coloringPage = await ColoringPagesService.getColoringPageBySlug(`/${slug}`);

    if (!coloringPage) {
      return NextResponse.json(
        { error: 'Coloring page not found' },
        { status: 404 }
      );
    }

    // 获取相关页面
    const relatedPages = await ColoringPagesService.getRelatedPages(
      coloringPage.id,
      coloringPage.category_id || '',
      coloringPage.subcategory_id,
      4
    );

    return NextResponse.json({
      page: coloringPage,
      relatedPages
    });

  } catch (error) {
    console.error('Error fetching coloring page:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
