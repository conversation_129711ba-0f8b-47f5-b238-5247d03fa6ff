import { NextRequest, NextResponse } from 'next/server';
import { ColoringPagesService } from '@/lib/services/coloring-pages-service';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const query = searchParams.get('q');
    if (!query || query.trim().length === 0) {
      return NextResponse.json(
        { error: 'Search query is required' },
        { status: 400 }
      );
    }
    
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;
    
    const categoryId = searchParams.get('category');
    const subcategoryId = searchParams.get('subcategory');
    const difficulty = searchParams.get('difficulty') as 'easy' | 'medium' | 'hard' | undefined;
    const ageGroup = searchParams.get('ageGroup') as 'toddler' | 'preschool' | 'school' | 'teen' | 'adult' | 'all' | undefined;
    
    // 构建筛选条件
    const filters = {
      ...(categoryId && { categoryId }),
      ...(subcategoryId && { subcategoryId }),
      ...(difficulty && { difficulty }),
      ...(ageGroup && { ageGroup })
    };
    
    // 搜索数据库
    const result = await ColoringPagesService.searchColoringPages(
      query.trim(),
      filters,
      { limit, offset }
    );
    
    return NextResponse.json({
      pages: result.pages,
      total: result.total,
      page,
      totalPages: Math.ceil(result.total / limit),
      hasMore: offset + limit < result.total,
      query: query.trim()
    });
    
  } catch (error) {
    console.error('Error searching coloring pages:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
