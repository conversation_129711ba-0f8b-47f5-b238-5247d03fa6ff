import { NextRequest, NextResponse } from 'next/server';
import { ColoringPagesService } from '@/lib/services/coloring-pages-service';

export async function GET(
  request: NextRequest,
  { params }: { params: { categoryId: string } }
) {
  try {
    const { categoryId } = params;
    const { searchParams } = new URL(request.url);
    
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;
    
    const subcategoryId = searchParams.get('subcategory');
    const difficulty = searchParams.get('difficulty') as 'easy' | 'medium' | 'hard' | undefined;
    const ageGroup = searchParams.get('ageGroup') as 'toddler' | 'preschool' | 'school' | 'teen' | 'adult' | 'all' | undefined;
    const featured = searchParams.get('featured') === 'true';
    
    // 构建筛选条件
    const filters = {
      categoryId,
      ...(subcategoryId && { subcategoryId }),
      ...(difficulty && { difficulty }),
      ...(ageGroup && { ageGroup }),
      ...(featured && { isFeatured: true })
    };
    
    // 从数据库获取数据
    const result = await ColoringPagesService.getColoringPages(filters, {
      limit,
      offset,
      orderBy: 'created_at',
      orderDirection: 'DESC'
    });

    return NextResponse.json({
      pages: result.pages,
      total: result.total,
      page,
      totalPages: Math.ceil(result.total / limit),
      hasMore: offset + limit < result.total
    });
    
  } catch (error) {
    console.error('Error fetching coloring pages by category:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
