import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { findOrderByOrderNo, updateOrderStatus } from '@/models/order';
import { decreaseCredits, CreditsTransType } from '@/services/credit';
import { getPayPalOrderDetails, processPayPalRefund, extractCaptureId } from '@/lib/paypal-webhook-verify';

/**
 * PayPal退款管理API
 * 仅限管理员使用
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🔄 管理员退款请求开始');

    // 1. 验证管理员权限
    const session = await auth();
    const adminEmails = process.env.ADMIN_EMAILS?.split(",") || [];
    
    if (!session?.user?.email || !adminEmails.includes(session.user.email)) {
      console.log('❌ 权限验证失败:', session?.user?.email);
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    console.log('✅ 管理员权限验证通过:', session.user.email);

    const { orderNo, refundAmount, refundReason } = await request.json();

    // 2. 验证请求参数
    if (!orderNo || !refundAmount) {
      return NextResponse.json({ error: '缺少必需参数' }, { status: 400 });
    }

    // 3. 查找订单
    const order = await findOrderByOrderNo(orderNo);
    if (!order) {
      return NextResponse.json({ error: '订单不存在' }, { status: 400 });
    }

    if (order.status === 'refunded') {
      return NextResponse.json({ error: '订单已退款' }, { status: 400 });
    }

    if (order.status !== 'paid') {
      return NextResponse.json({ error: '只能退款已支付的订单' }, { status: 400 });
    }

    // PayPal-only系统，所有订单都是PayPal订单，无需检查支付提供商

    console.log('✅ 订单验证通过:', orderNo);

    // 4. 提取Capture ID
    const orderDetails = JSON.parse(order.paid_detail || '{}');
    let captureId = extractCaptureId(orderDetails);

    if (!captureId) {
      // 尝试从PayPal API获取
      const paypalOrderId = order.paypal_order_id;
      if (paypalOrderId) {
        console.log('🔄 从PayPal API获取订单详情');
        const orderData = await getPayPalOrderDetails(paypalOrderId);
        captureId = orderData.purchase_units?.[0]?.payments?.captures?.[0]?.id;
      }
    }

    if (!captureId) {
      return NextResponse.json({ 
        error: '无法获取PayPal捕获ID，无法处理退款' 
      }, { status: 400 });
    }

    console.log('✅ 获取到Capture ID:', captureId);

    // 5. 执行PayPal退款
    console.log('🔄 执行PayPal退款');
    const refundResult = await processPayPalRefund(captureId, refundAmount);
    console.log('✅ PayPal退款成功:', refundResult.id);

    // 6. 更新订单状态
    const isFullRefund = parseFloat(refundAmount) >= (order.amount / 100);
    const newStatus = isFullRefund ? 'refunded' : 'partial_refunded';
    
    const refundDetail = {
      ...orderDetails,
      refund_info: {
        refund_id: refundResult.id,
        refund_amount: refundAmount,
        refund_reason: refundReason || '管理员处理退款',
        refund_time: new Date().toISOString(),
        refunded_by: session.user.email,
        is_full_refund: isFullRefund,
        original_amount: order.amount / 100
      }
    };

    await updateOrderStatus(
      orderNo, 
      newStatus, 
      new Date().toISOString(), 
      order.paid_email || '', 
      JSON.stringify(refundDetail)
    );

    console.log('✅ 订单状态更新成功:', newStatus);

    // 7. 扣除积分（如果是全额退款）
    if (isFullRefund && order.credits > 0 && order.user_uuid) {
      try {
        await decreaseCredits({
          user_uuid: order.user_uuid,
          trans_type: CreditsTransType.OrderPay, // 使用相同的类型但负数
          credits: order.credits
        });
        console.log(`✅ 已扣除用户积分: ${order.credits}`);
      } catch (creditError) {
        console.error('⚠️ 扣除积分失败:', creditError);
        // 积分扣除失败不影响退款流程
      }
    }

    console.log('✅ 退款处理完成');

    return NextResponse.json({
      success: true,
      refundId: refundResult.id,
      newStatus,
      refundAmount: parseFloat(refundAmount),
      isFullRefund,
      message: '退款处理成功'
    });

  } catch (error: any) {
    console.error('❌ 退款处理失败:', error);
    return NextResponse.json({ 
      error: '退款处理失败',
      message: error.message 
    }, { status: 500 });
  }
}

/**
 * 获取订单退款信息
 */
export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    const session = await auth();
    const adminEmails = process.env.ADMIN_EMAILS?.split(",") || [];
    
    if (!session?.user?.email || !adminEmails.includes(session.user.email)) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const orderNo = searchParams.get('orderNo');

    if (!orderNo) {
      return NextResponse.json({ error: '缺少订单号' }, { status: 400 });
    }

    const order = await findOrderByOrderNo(orderNo);
    if (!order) {
      return NextResponse.json({ error: '订单不存在' }, { status: 404 });
    }

    // 解析退款信息
    let refundInfo = null;
    if (order.paid_detail) {
      try {
        const details = JSON.parse(order.paid_detail);
        refundInfo = details.refund_info || null;
      } catch (e) {
        console.error('解析订单详情失败:', e);
      }
    }

    return NextResponse.json({
      success: true,
      order: {
        order_no: order.order_no,
        status: order.status,
        amount: order.amount,
        credits: order.credits,
        order_type: order.order_type,
        paid_at: order.paid_at,
        paid_email: order.paid_email
      },
      refundInfo
    });

  } catch (error: any) {
    console.error('获取退款信息失败:', error);
    return NextResponse.json({ 
      error: '获取退款信息失败',
      message: error.message 
    }, { status: 500 });
  }
}
