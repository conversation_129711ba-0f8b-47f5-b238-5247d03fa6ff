import { NextRequest, NextResponse } from 'next/server';
import { respData, respErr } from "@/lib/resp";
import { getUserEmail, getUserUuid } from "@/services/user";
import { insertOrder, updateOrderPayPalId } from "@/models/order";
import { Order } from "@/types/order";
import { findUserByUuid } from "@/models/user";
import { getSnowId } from "@/lib/hash";
import { paypalClient } from "@/lib/paypal-client";
import { getPayPalConfig, PAYPAL_APPLICATION_CONTEXT, buildPayPalUrls } from "@/lib/paypal-config";
import { paypalLogger, PayPalOperation } from "@/lib/paypal-logger";

/**
 * PayPal订单创建API
 * 基于PayPal Checkout API实现
 */
export async function POST(request: NextRequest) {
  const context = paypalLogger.createContext({ requestId: crypto.randomUUID() });

  try {
    const body = await request.json();
    paypalLogger.info(PayPalOperation.ORDER_CREATE, 'PayPal checkout request received', body, context);

    // 验证请求参数
    const {
      product_id,
      product_name,
      credits,
      amount,
      currency = "USD",
      valid_months = 12,
      returnUrl,
      cancelUrl
    } = body;

    if (!product_id || !product_name || !amount || credits === undefined) {
      return respErr("缺少必需参数");
    }

    // 1. 验证用户登录状态
    const user_uuid = await getUserUuid();
    const user_email = await getUserEmail();

    if (!user_uuid) {
      return NextResponse.json({ error: '请先登录' }, { status: 401 });
    }

    // 2. 验证用户存在
    const user = await findUserByUuid(user_uuid);
    if (!user) {
      return respErr("用户不存在");
    }

    // 3. 生成订单号
    const order_no = getSnowId();

    // 4. 创建本地订单
    const order: Order = {
      order_no: order_no.toString(),
      created_at: new Date().toISOString(),
      user_uuid: user_uuid,
      user_email: user_email,
      amount: Math.round(amount), // PayPal金额已经是分为单位
      expired_at: new Date(Date.now() + valid_months * 30 * 24 * 60 * 60 * 1000).toISOString(),
      status: "created",
      credits: credits,
      currency: currency.toLowerCase(),
      product_id: product_id,
      product_name: product_name,
      valid_months: valid_months,
      order_type: 'one_time'
    };

    await insertOrder(order);

    // 5. 构建PayPal订单数据
    const config = getPayPalConfig();
    const { successUrl, cancelUrl: cancelUrlFinal } = buildPayPalUrls(returnUrl, cancelUrl);

    const paypalOrder = {
      intent: 'CAPTURE',
      application_context: {
        return_url: successUrl,
        cancel_url: cancelUrlFinal,
        brand_name: config.projectName,
        ...PAYPAL_APPLICATION_CONTEXT
      },
      purchase_units: [{
        reference_id: order_no.toString(),
        amount: {
          currency_code: currency.toUpperCase(),
          value: (amount / 100).toFixed(2) // 转换为元
        },
        description: product_name,
        custom_id: user_uuid
      }]
    };

    // 6. 创建PayPal订单
    const orderData = await paypalClient.createOrder(paypalOrder);

    // 7. 更新订单的PayPal ID
    await updateOrderPayPalId(order_no.toString(), orderData.id);

    // 8. 返回支付链接
    const approvalUrl = orderData.links?.find((link: any) => link.rel === 'approve')?.href;

    if (!approvalUrl) {
      throw new Error('PayPal未返回支付链接');
    }

    return respData({
      success: true,
      orderId: orderData.id,
      orderNo: order_no.toString(),
      approvalUrl
    });

  } catch (error: any) {
    paypalLogger.error(PayPalOperation.ORDER_CREATE, 'PayPal订单创建失败', error, undefined, context);
    return respErr(`创建支付订单失败: ${error.message}`);
  }
}
