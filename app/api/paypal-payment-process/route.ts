import { NextRequest } from 'next/server';
import { paypalClient } from "@/lib/paypal-client";
import { findOrderByPayPalId, updateOrderStatus } from "@/models/order";
import { increaseCredits, CreditsTransType } from "@/services/credit";
import { respData, respErr } from "@/lib/resp";

/**
 * PayPal支付处理API
 * 用于处理PayPal支付成功后的订单验证和状态更新
 * 支持开发和生产环境，提供即时的支付结果反馈
 * 
 * 工作原理：
 * 1. 用户支付成功后，PayPal跳转到成功页面
 * 2. 成功页面调用此API进行支付验证
 * 3. API验证PayPal订单状态并更新本地订单
 * 4. 同时webhook也会异步处理，但此API确保用户能立即看到结果
 */
export async function POST(request: NextRequest) {
  try {
    const { paypal_order_id, payer_id } = await request.json();

    if (!paypal_order_id) {
      console.error('PayPal支付处理失败: 缺少PayPal订单ID');
      return respErr('Missing PayPal order ID');
    }

    console.log('开始处理PayPal支付验证:', { paypal_order_id, payer_id });

    // 1. 查找本地订单
    const order = await findOrderByPayPalId(paypal_order_id);
    if (!order) {
      console.error('PayPal支付处理失败: 订单不存在', { paypal_order_id });
      return respErr('Order not found');
    }

    console.log('找到订单:', { order_no: order.order_no, status: order.status, amount: order.amount });

    // 2. 检查订单状态
    if (order.status === 'paid') {
      return respData({
        success: true,
        message: 'Order already paid',
        order_no: order.order_no,
        credits: order.credits,
        already_processed: true
      });
    }

    // 检查订单状态，允许已支付订单返回成功（幂等性）
    if (order.status !== 'created' && order.status !== 'paid') {
      return respErr(`Invalid order status: ${order.status}`);
    }

    // 3. 获取PayPal订单详情进行验证
    const paypalOrderDetails = await paypalClient.getOrderDetails(paypal_order_id);

    // 4. 验证订单状态
    if (paypalOrderDetails.status !== 'APPROVED' && paypalOrderDetails.status !== 'COMPLETED') {
      return respErr(`Invalid PayPal order status: ${paypalOrderDetails.status}`);
    }

    // 5. 验证订单金额（防止篡改）
    const paypalAmount = paypalOrderDetails.purchase_units?.[0]?.amount?.value;
    const expectedAmount = (order.amount / 100).toFixed(2);
    if (paypalAmount !== expectedAmount) {
      return respErr('Order amount verification failed');
    }

    // 6. 如果订单已批准但未捕获，需要捕获支付
    let captureResult = null;
    if (paypalOrderDetails.status === 'APPROVED') {
      try {
        captureResult = await paypalClient.captureOrder(paypal_order_id);
      } catch (captureError) {
        return respErr('Payment capture failed, please contact support');
      }
    }

    // 7. 更新订单状态为已支付
    const paidDetail = {
      paypal_order_details: paypalOrderDetails,
      capture_result: captureResult,
      processed_at: new Date().toISOString(),
      processed_by: 'payment_process_api',
      payer_id: payer_id
    };

    await updateOrderStatus(
      order.order_no,
      'paid',
      new Date().toISOString(),
      paypalOrderDetails.payer?.email_address || '',
      JSON.stringify(paidDetail)
    );

    // 8. 增加用户积分
    if (order.user_uuid && order.credits > 0) {
      await increaseCredits({
        user_uuid: order.user_uuid,
        trans_type: CreditsTransType.OrderPay,
        credits: order.credits,
        expired_at: order.expired_at,
        order_no: order.order_no
      });
    }

    // 9. 返回成功结果
    return respData({
      success: true,
      message: 'Payment processed successfully',
      order_no: order.order_no,
      credits: order.credits,
      paid_at: new Date().toISOString()
    });

  } catch (error: any) {
    
    // 根据错误类型返回不同的错误信息
    if (error.message?.includes('401') || error.message?.includes('Unauthorized')) {
      return respErr('PayPal authentication failed, please contact support');
    }

    if (error.message?.includes('404') || error.message?.includes('Not Found')) {
      return respErr('PayPal order not found');
    }

    if (error.message?.includes('timeout')) {
      return respErr('PayPal service timeout, please try again later');
    }

    return respErr('Payment processing failed, please contact support');
  }
}
