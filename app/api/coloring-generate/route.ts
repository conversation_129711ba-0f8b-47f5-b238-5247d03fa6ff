import { respData, respErr } from "@/lib/resp";
import { getUuid } from "@/lib/hash";
import { Photo } from "@/types/photo";
import { insertPhotos } from "@/models/photo";
import { getIsoTimestr } from "@/lib/time";
import { Storage } from "@/lib/storage";
import { decreaseCredits, getUserCredits, CreditsTransType } from "@/services/credit";
import { getUserUuid } from "@/services/user";
import { unifiedImageService } from "@/aisdk/provider/unified-image-service";
import { ProviderType } from "@/aisdk/provider/simple-config";
import { generateStorageKey } from "@/lib/file-naming";

// 涂色页样式提示词 - 精简版
const COLORING_STYLE_PROMPTS = {
  'default': 'coloring page',
  'sci-fi': 'sci-fi theme',
  'pixel': 'pixel art style',
  'chibi': 'chibi anime style',
  'graffiti': 'graffiti style',
  'minimalist': 'minimalist design',
  'anime': 'anime style',
  'lego': 'LEGO style',
  'intricate': 'detailed patterns',
  'kawaii': 'kawaii style',
  'cartoon': 'cartoon style',
  'snoopy': 'cute cartoon',
  'sketch': 'sketch style',
  'simple-lines': 'simple lines',
  'detailed-lines': 'detailed lines',
  'cartoon-style': 'cartoon',
  'mandala-style': 'mandala',
  'nature-style': 'nature theme',
  'animal-style': 'animal theme',
  'fantasy-style': 'fantasy theme',
  'geometric-style': 'geometric'
};

// 涂色页基础提示词 - 精简版，明确白底要求
const COLORING_BASE_REQUIREMENTS = [
  'line art coloring page',
  'white background',
  'black outlines',
  'clear outlines'
];

// 功能选项提示词 - 精简版
const FEATURE_PROMPTS = {
  'remove_background': 'isolated subject on white background',
  'keep_face': 'preserve facial features',
  'image_enhancer': 'enhanced clarity'
};

// 图片转换专用基础提示词 - 优化版，避免OpenAI内容过滤器敏感词汇，确保白底
const IMAGE_TO_COLORING_BASE = 'Turn this image into line art coloring page with white background and black outlines';

// 获取域名用于水印
function getDomainForWatermark(): string {
  // 从环境变量获取域名，或使用默认值
  const webUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://makecoloring.com';
  const domain = webUrl.replace(/^https?:\/\//, '').replace(/^www\./, '');
  return domain === 'localhost:3000' ? 'makecoloring.com' : domain;
}

// 构建域名水印提示词 - 优化版（细边框，底部显示域名）
function buildWatermarkPrompt(domain: string): string {
  return `thin border frame with "${domain}" text at bottom corner`;
}

// 构建涂色页专用提示词 - 精简版
function buildColoringPrompt(
  description: string,
  style: string,
  options: {
    mode?: 'text-to-image' | 'image-to-image';
    private?: boolean;
    removeBackground?: boolean;
    keepFace?: boolean;
    imageEnhancer?: boolean;
  } = {}
): string {
  let prompt: string;

  if (options.mode === 'image-to-image') {
    // 图像转换模式：使用简化的转换提示词
    prompt = IMAGE_TO_COLORING_BASE;
  } else {
    // 文本转图像模式：使用用户输入 + 基础要求
    prompt = description.trim();
    const baseRequirements = COLORING_BASE_REQUIREMENTS.join(', ');
    prompt = `${prompt} as a ${baseRequirements}`;
  }

  // 添加样式特定的提示词
  if (style !== 'no-style') {
    if (COLORING_STYLE_PROMPTS[style as keyof typeof COLORING_STYLE_PROMPTS]) {
      const stylePrompt = COLORING_STYLE_PROMPTS[style as keyof typeof COLORING_STYLE_PROMPTS];
      prompt = `${prompt}, ${stylePrompt}`;
    }
  }

  // 添加功能选项提示词（主要用于图片转涂色页）
  if (options.mode === 'image-to-image') {
    if (options.keepFace) {
      prompt = `${prompt}, ${FEATURE_PROMPTS['keep_face']}`;
    }
    if (options.removeBackground) {
      prompt = `${prompt}, ${FEATURE_PROMPTS['remove_background']}`;
    }
    if (options.imageEnhancer) {
      prompt = `${prompt}, ${FEATURE_PROMPTS['image_enhancer']}`;
    }
  }

  // 添加域名边框（除非是private模式）
  if (!options.private) {
    const domain = getDomainForWatermark();
    const watermarkPrompt = buildWatermarkPrompt(domain);
    prompt = `${prompt}, ${watermarkPrompt}`;
  }

  return prompt;
}

// 统一图像生成函数 - 使用新的统一服务
async function generateUnifiedColoringImage(
  prompt: string,
  mode: 'text-to-image' | 'image-to-image',
  size: string = '2:3', // 改为size参数
  inputImage?: string,
  preferredProvider?: ProviderType,
  userUuid?: string
): Promise<{ images: any[]; warnings: any[]; provider: string; model: string; cost: number; taskId?: string }> {

  console.log(`🎨 Starting ${mode} coloring generation with preferred provider: ${preferredProvider || 'auto'}`);
  console.log(`📐 Using size: ${size}`);

  try {
    // 使用统一的图像生成服务
    const result = await unifiedImageService.generateImage({
      prompt,
      mode,
      size, // APICore 使用 size 参数
      aspectRatio: size, // Replicate 使用 aspectRatio 参数，传递相同的值
      n: 1,
      inputImage,
      preferredProvider,
      userUuid // 传递用户UUID给kie.ai异步任务
    });



    return {
      images: result.images || [],
      warnings: result.warnings || [],
      provider: result.provider,
      model: result.model,
      cost: result.cost,
      taskId: result.taskId // 传递异步任务ID
    };

  } catch (error) {
    throw new Error(`Image generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function POST(req: Request) {
  try {
    const {
      description,
      mode = 'text-to-image',
      style = 'simple-lines',
      size = '2:3', // 默认2:3比例
      image: inputImage,
      removeBackground = false,
      imageEnhancer = false,
      keepFace = false,
      private: isPrivate = false, // 默认非私有模式
      provider // 不设置默认值，由配置决定
    } = await req.json();



    // 1. 参数验证
    // 对于文本转图像模式，description是必需的
    if (mode === 'text-to-image') {
      if (!description || description.trim().length === 0) {
        return respErr('Description is required for text-to-image mode');
      }
      if (description.trim().length > 1000) {
        return respErr('Description must be less than 1000 characters');
      }
    }

    if (mode === 'image-to-image' && !inputImage) {
      return respErr('Image is required for image-to-image mode');
    }

    // 验证尺寸参数
    const allowedSizes = ['1:1', '2:3', '3:2'];
    if (!allowedSizes.includes(size)) {
      return respErr(`Invalid size. Allowed sizes: ${allowedSizes.join(', ')}`);
    }

    // 2. 用户认证
    let user_uuid = await getUserUuid();

    // 开发模式：如果没有用户UUID，使用测试UUID
    if (!user_uuid && process.env.NODE_ENV === 'development') {
      user_uuid = 'test-user-uuid-for-development';
      console.log('🔧 Development mode: Using test user UUID');
    }

    if (!user_uuid) {
      return respErr('Please log in to generate coloring pages');
    }

    // 3. 检查可用的providers
    const availableProviders = unifiedImageService.getAvailableProviders();
    if (availableProviders.length === 0) {
      return respErr('No image generation providers are configured. Please check your API keys.');
    }

    // 4. 积分检查
    const credits = await getUserCredits(user_uuid);
    const cost_credits = 3; // 默认积分消耗

    // 开发环境测试模式：跳过积分检查
    const isTestingMode = process.env.NODE_ENV === 'development' && process.env.UNLIMITED_CREDITS_FOR_TESTING === 'true';

    if (!isTestingMode && credits.left_credits < cost_credits) {
      return respErr(`Insufficient credits. You need ${cost_credits} credits but only have ${credits.left_credits}. Please purchase more credits.`);
    }



    // 5. 构建涂色页专用提示词
    const promptStr = buildColoringPrompt(description || '', style, {
      mode: mode as 'text-to-image' | 'image-to-image',
      private: isPrivate,
      removeBackground,
      imageEnhancer,
      keepFace
    });

    // 开发模式：打印最终的prompt和参数
    if (process.env.NODE_ENV === 'development') {
      console.log('🎨 [DEV] Final generation parameters:', {
        mode,
        style,
        size,
        finalPrompt: promptStr,
        options: {
          private: isPrivate,
          removeBackground,
          imageEnhancer,
          keepFace,
          watermark: !isPrivate,
          enhance: false, // 从前端传递的enhance参数
          high_quality: false // 从前端传递的high_quality参数
        },
        provider: provider || 'auto',
        user_uuid,
        cost_credits
      });
    }

    // 6. 生成涂色页图像（统一处理）
    let images: any[] = [];
    let actualProvider: string = '';
    let actualModel: string = '';
    let actualCost: number = cost_credits;

    try {
      const result = await generateUnifiedColoringImage(
        promptStr,
        mode as 'text-to-image' | 'image-to-image',
        size,
        inputImage,
        provider as ProviderType | undefined,
        user_uuid // 传递用户UUID给异步任务
      );

      // 检查是否是异步任务（kie.ai）
      if (result.provider === 'kie' && result.images.length === 0 && (result as any).taskId) {
        console.log(`🔄 Kie.ai async task created: ${(result as any).taskId}`);

        // 异步任务：不立即扣除积分，等待生图成功后在callback中扣除
        console.log('💰 Credits will be deducted after successful generation via callback');

        // 获取当前积分（未扣除）
        const currentCredits = await getUserCredits(user_uuid);

        // 返回异步任务信息
        return respData({
          success: true,
          mode: mode,
          prompt: promptStr,
          async_task: {
            uuid: (result as any).taskId,
            status: 'generating',
            provider: 'kie'
          },
          left_credits: currentCredits.left_credits,
          cost_credits: result.cost,
          is_async: true,
          message: 'Image generation started. Please wait for completion.'
        });
      }

      // 同步结果处理
      images = result.images;
      actualProvider = result.provider;
      actualModel = result.model;
      actualCost = result.cost;
    } catch (error) {
      return respErr(error instanceof Error ? `Failed to generate coloring page: ${error.message}` : 'Failed to generate coloring page. Please try again.');
    }

    // 验证生成结果
    if (!images?.length) {
      return respErr('No coloring pages were generated. Please try again.');
    }



    // 7. 图片存储处理
    const batch = getUuid();
    const storage = new Storage();
    const photos = await Promise.all(
      images.map(async (image, index) => {
        // 使用新的文件命名规则：${date}/${provider}_${uuid}_${index}.png
        const key = generateStorageKey(actualProvider, batch, index);

        try {
          let res;

          if (image.base64) {
            // 处理base64格式的图片（Replicate等）
            const body = Buffer.from(image.base64, "base64");
            res = await storage.uploadFile({
              body,
              key,
              contentType: "image/png",
              disposition: "inline",
            });
          } else if (image.url) {
            // 处理URL格式的图片（APICore等）
            console.log(`📥 Processing image URL from ${actualProvider}: ${image.url}`);
            res = await storage.downloadAndUpload({
              url: image.url,
              key,
              contentType: "image/png",
              disposition: "inline",
            });
          } else {
            console.warn(`⚠️ Image ${index + 1} has no base64 or url data`);
            return null;
          }

          return {
            uuid: getUuid(),
            user_uuid: user_uuid,
            created_at: getIsoTimestr(),
            img_description: description,
            img_url: res.url,
            status: "created",
            generation_type: mode === 'image-to-image' ? 'image' : 'text'
          } as Photo;
        } catch (err) {
          return null;
        }
      })
    ).then((results) => results.filter((photo): photo is Photo => photo !== null));

    if (!photos.length) {
      return respErr('Failed to upload generated coloring pages. Please try again.');
    }

    // 8. 保存到数据库
    try {
      await insertPhotos(photos);
    } catch (dbError) {
      return respErr('Failed to save generated coloring pages to database. Please try again.');
    }

    // 9. 扣除积分
    if (!isTestingMode) {
      try {
        await decreaseCredits({
          user_uuid,
          trans_type: CreditsTransType.ColoringGenerate,
          credits: actualCost,
        });
        console.log(`💰 Credits deducted: ${actualCost} for ${actualProvider} coloring generation`);
      } catch (creditError) {
        console.error("Credit deduction failed:", creditError);
        return respErr('Failed to deduct credits. Please try again.');
      }
    } else {
      console.log('🔧 Development mode: Skipping credits deduction for testing');
    }

    // 10. 返回成功结果
    const finalCredits = await getUserCredits(user_uuid);

    return respData({
      success: true,
      mode: mode,
      prompt: promptStr,
      photos: photos,
      left_credits: finalCredits.left_credits,
      generated_count: photos.length,
      model_used: actualModel,
      cost_credits: actualCost,
      is_coloring_page: true,
      style_used: style,
      provider: actualProvider,
      processing_options: {
        removeBackground,
        imageEnhancer,
        keepFace,
        private: isPrivate
      },
      message: 'Coloring page generated successfully!'
    });

  } catch (error) {
    return respErr('An unexpected error occurred. Please try again.');
  }
}
