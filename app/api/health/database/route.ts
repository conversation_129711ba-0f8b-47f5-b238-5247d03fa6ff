import { NextResponse } from 'next/server';
import { checkDatabaseHealth, dbConnection } from '@/lib/database/connection';

export async function GET() {
  try {
    const healthCheck = await checkDatabaseHealth();
    const connectionStatus = dbConnection.getConnectionStatus();

    return NextResponse.json({
      database: healthCheck,
      connection: connectionStatus,
      timestamp: new Date().toISOString()
    }, {
      status: healthCheck.status === 'healthy' ? 200 : 503
    });
  } catch (error) {
    return NextResponse.json({
      database: {
        status: 'unhealthy',
        message: `健康检查失败: ${error}`,
        timestamp: new Date().toISOString()
      },
      connection: {
        isConnected: false,
        error: String(error)
      }
    }, {
      status: 503
    });
  }
}
