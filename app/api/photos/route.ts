import { respData, respErr } from "@/lib/resp";
import { getPhotos, getPhotosByUser } from "@/models/photo";
import { getUserUuid } from "@/services/user";

export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "6");
    const userOnly = url.searchParams.get("userOnly") === "true";

    let photos;

    if (userOnly) {
      // 获取当前用户的photos
      const userUuid = await getUserUuid();
      if (!userUuid) {
        return respErr("User not authenticated");
      }
      photos = await getPhotosByUser(userUuid, page, limit);
    } else {
      // 获取所有photos（用于gallery展示）
      photos = await getPhotos(page, limit);
    }

    return respData({
      success: true,
      photos: photos || [],
    });
  } catch (error) {
    console.error("Error fetching photos:", error);
    return respErr("Failed to fetch photos");
  }
}
