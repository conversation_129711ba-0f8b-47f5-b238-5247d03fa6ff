import { respData, respErr } from "@/lib/resp";
import { getImageGenerationConfig } from "@/aisdk/provider/unified-image-service";

export async function GET() {
  try {
    const config = getImageGenerationConfig();
    
    return respData({
      activeProvider: config.activeProvider,
      availableProviders: config.availableProviders,
      summary: config.summary
    });
  } catch (error) {
    console.error('Failed to get provider config:', error);
    return respErr('Failed to get provider configuration');
  }
}
