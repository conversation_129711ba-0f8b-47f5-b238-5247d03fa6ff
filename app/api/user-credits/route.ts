import { respData, respErr } from "@/lib/resp";
import { getUserCredits } from "@/services/credit";
import { getUserUuid } from "@/services/user";

export async function GET(req: Request) {
  try {
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr('Please log in to view credits');
    }

    const credits = await getUserCredits(user_uuid);
    
    return respData({
      success: true,
      credits: credits
    });
  } catch (error) {
    console.error("Error fetching user credits:", error);
    return respErr('Failed to fetch user credits');
  }
}
