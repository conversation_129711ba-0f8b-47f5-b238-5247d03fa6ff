import { MetadataRoute } from 'next'
import { locales } from '@/i18n/locale'
import { coloringCategories } from '@/lib/coloring-categories'
import { ColoringPagesService } from '@/lib/services/coloring-pages-service'

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://makecoloring.com'

  // 基础页面路径 - 只包含已开发的页面
  const routes = [
    '', // 首页
    '/pricing', // 定价页面
    '/privacy-policy', // 隐私政策
    '/terms-of-service', // 服务条款
    '/printable-coloring-pages', // 新的涂色页面主页
    '/text-to-coloring-page', // 文字转涂色页面
    '/image-to-coloring-page', // 图片转涂色页面
  ]

  // 添加新的分类页面路径
  const categoryRoutes = coloringCategories.map(category => `/printable-coloring-pages/${category.seo_slug.replace(/^\//, '')}`)

  // 获取数据库中的所有已发布页面
  let dbPages: any[] = [];
  try {
    dbPages = await ColoringPagesService.getAllPublishedPages();
  } catch (error) {
    console.error('获取数据库页面失败:', error);
    dbPages = [];
  }

  // 添加数据库中的单页详情路径
  const dbPageRoutes = dbPages
    .filter(page => page.seo_slug)
    .map(page => `/${page.seo_slug.replace(/^\//, '')}`);

  // 合并所有路径
  const allRoutes = [...routes, ...categoryRoutes, ...dbPageRoutes]

  // 为每个语言和路径生成URL
  const sitemapEntries: MetadataRoute.Sitemap = []

  allRoutes.forEach(route => {
    locales.forEach(locale => {
      const url = locale === 'en'
        ? `${baseUrl}${route}`
        : `${baseUrl}/${locale}${route}`

      // 设置不同页面的优先级和更新频率
      let priority = 0.8
      let changeFrequency: 'daily' | 'weekly' | 'monthly' = 'weekly'

      if (route === '') {
        // 首页最高优先级
        priority = 1
        changeFrequency = 'daily'
      } else if (route === '/printable-coloring-pages' || route.startsWith('/printable-coloring-pages/')) {
        // 涂色页面高优先级
        priority = 0.9
        changeFrequency = 'weekly'
      } else if (route.endsWith('-coloring-pages')) {
        // SEO友好的分类和子分类页面
        priority = 0.9
        changeFrequency = 'weekly'
      } else if (route.includes('-coloring-page-')) {
        // 单页详情页面
        priority = 0.8
        changeFrequency = 'monthly'
      } else if (route === '/text-to-coloring-page' || route === '/image-to-coloring-page') {
        // 主要功能页面高优先级
        priority = 0.9
        changeFrequency = 'weekly'
      } else if (route === '/pricing') {
        // 定价页面高优先级
        priority = 0.9
        changeFrequency = 'monthly'
      } else if (route === '/privacy-policy' || route === '/terms-of-service') {
        // 法律页面低优先级
        priority = 0.5
        changeFrequency = 'monthly'
      }

      sitemapEntries.push({
        url,
        lastModified: new Date(),
        changeFrequency,
        priority,
      })
    })
  })

  return sitemapEntries
}
