@tailwind base;
@tailwind components;
@tailwind utilities;

@import "theme.css";

html {
  scroll-behavior: smooth;
}

/* 性能优化 */
* {
  box-sizing: border-box;
}

/* 图片优化 */
img {
  max-width: 100%;
  height: auto;
}

/* 减少重绘和回流 */
.transition-transform {
  will-change: transform;
}

.transition-opacity {
  will-change: opacity;
}

/* 优化动画性能 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  :root {
    --sidebar-background: var(--background);
    --sidebar-foreground: var(--foreground);
    --sidebar-primary: var(--primary);
    --sidebar-primary-foreground: var(--primary-foreground);
    --sidebar-accent: var(--background);
    --sidebar-accent-foreground: var(--primary);
    --sidebar-border: var(--border);
    --sidebar-ring: var(--ring);
  }
  .dark {
    --sidebar-background: var(--background);
    --sidebar-foreground: var(--foreground);
    --sidebar-primary: var(--primary);
    --sidebar-primary-foreground: var(--primary-foreground);
    --sidebar-accent: var(--accent);
    --sidebar-accent-foreground: var(--accent-foreground);
    --sidebar-border: var(--border);
    --sidebar-ring: var(--ring);
  }
}

@layer components {
  /* Fix navigation menu alignment */
  [data-radix-navigation-menu-viewport] {
    transform-origin: var(--radix-navigation-menu-content-transform-origin);
    left: var(--radix-navigation-menu-viewport-position-x, 0) !important;
  }
}
