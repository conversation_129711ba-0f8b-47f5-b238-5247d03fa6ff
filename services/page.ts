import { LandingPage } from "@/types/pages/landing";

export async function getLandingPage(locale: string): Promise<LandingPage> {
  try {
    // 只支持英文，其他语言都回退到英文
    if (locale == "de") {
      locale = "de";
    }else if (locale == "es") {
      locale = "es";
    }else if (locale == "fr") {
      locale = "fr";
    }else if (locale == "it") {
      locale = "it";
    }else if (locale == "pt") {
      locale = "pt";
    }else if (locale == "ru") {
      locale = "ru";
    }else if (locale == "vi") {
      locale = "vi";
    }else if (locale == "hi") {
      locale = "hi";
    }else {
      locale = "en";
    }
    return await import(
      `@/i18n/pages/landing/${locale.toLowerCase()}.json`
    ).then((module) => module.default);
  } catch (error) {
    console.warn(`Failed to load ${locale}.json, falling back to en.json`);
    return await import("@/i18n/pages/landing/en.json").then(
      (module) => module.default as LandingPage
    );
  }
}
