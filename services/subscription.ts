import { Order } from "@/types/order";
import { getSupabaseClient } from "@/models/db";
import { increaseCredits, CreditsTransType } from "./credit";
import { getSnowId } from "@/lib/hash";

/**
 * 订阅管理服务
 * 统一处理所有支付渠道的订阅逻辑
 */

export enum SubscriptionStatus {
  PENDING = "PENDING",
  ACTIVE = "ACTIVE", 
  SUSPENDED = "SUSPENDED",
  CANCELLED = "CANCELLED",
  EXPIRED = "EXPIRED"
}

export enum OrderType {
  ONE_TIME = "one_time",
  SUBSCRIPTION = "subscription"
}

/**
 * 创建周期性扣费订单
 * 当订阅发生周期性扣费时，创建新的订单记录
 */
export async function createRecurringOrder(
  originalOrder: Order,
  cycleCount: number,
  paymentDetails?: any
): Promise<Order> {
  const recurringOrder: Order = {
    order_no: getSnowId().toString(),
    created_at: new Date().toISOString(),
    user_uuid: originalOrder.user_uuid,
    user_email: originalOrder.user_email,
    amount: originalOrder.amount,
    expired_at: originalOrder.expired_at,
    status: "paid", // 周期性扣费直接标记为已支付
    credits: originalOrder.credits,
    currency: originalOrder.currency,
    product_id: originalOrder.product_id,
    product_name: originalOrder.product_name,
    valid_months: originalOrder.valid_months,
    order_type: 'subscription',
    parent_order_no: originalOrder.order_no, // 关联到原始订单
    billing_cycle_count: cycleCount,
    paypal_subscription_id: originalOrder.paypal_subscription_id,
    paid_at: new Date().toISOString(),
    paid_detail: paymentDetails ? JSON.stringify(paymentDetails) : undefined
  };

  const supabase = getSupabaseClient();
  const { error } = await supabase.from("orders").insert(recurringOrder);

  if (error) {
    throw error;
  }

  return recurringOrder;
}

/**
 * 处理订阅周期性扣费
 * 1. 创建新的周期性订单记录
 * 2. 为用户增加积分
 */
export async function handleSubscriptionRecurringPayment(
  subscriptionId: string,
  paymentDetails?: any
): Promise<void> {
  // 查找原始订阅订单 - 兼容所有支付平台
  const supabase = getSupabaseClient();
  const { data: originalOrder, error } = await supabase
    .from("orders")
    .select("*")
    .or(`paypal_subscription_id.eq.${subscriptionId},subscription_id.eq.${subscriptionId},sub_id.eq.${subscriptionId}`)
    .eq("status", "paid") // 确保是已支付的订单
    .is("parent_order_no", null) // 确保是原始订单，不是周期性订单
    .single();

  if (error || !originalOrder) {
    return;
  }

  // 查询已有的周期性订单数量，确定当前周期
  const { data: recurringOrders, error: countError } = await supabase
    .from("orders")
    .select("billing_cycle_count")
    .eq("parent_order_no", originalOrder.order_no)
    .order("billing_cycle_count", { ascending: false })
    .limit(1);

  if (countError) {
    return;
  }

  const nextCycleCount = recurringOrders && recurringOrders.length > 0 
    ? (recurringOrders[0].billing_cycle_count || 0) + 1 
    : 1;

  // 创建周期性订单
  const recurringOrder = await createRecurringOrder(
    originalOrder,
    nextCycleCount,
    paymentDetails
  );

  // 为用户增加积分
  if (originalOrder.user_uuid && originalOrder.credits > 0) {
    await increaseCredits({
      user_uuid: originalOrder.user_uuid,
      trans_type: CreditsTransType.OrderPay,
      credits: originalOrder.credits,
      expired_at: originalOrder.expired_at,
      order_no: recurringOrder.order_no // 使用新创建的周期性订单号
    });
  }
}

/**
 * 获取用户的活跃PayPal订阅
 * 只查询PayPal订阅数据
 */
export async function getUserActiveSubscriptions(userUuid: string): Promise<Order[]> {
  const supabase = getSupabaseClient();

  // 查询PayPal已支付的订阅订单
  const { data, error } = await supabase
    .from("orders")
    .select("*")
    .eq("user_uuid", userUuid)
    .eq("status", "paid")
    .is("parent_order_no", null) // 只返回原始订阅订单
    .not("paypal_subscription_id", "is", null) // PayPal订阅ID不为空
    .order("created_at", { ascending: false });

  if (error) {
    console.error('❌ 查询用户活跃PayPal订阅失败:', error);
    return [];
  }

  // 过滤出未过期的订阅
  const now = new Date();
  const activeSubscriptions = (data || []).filter(order => {
    if (order.expired_at) {
      const expiredAt = new Date(order.expired_at);
      return expiredAt > now;
    }
    return true; // 如果没有过期时间，认为是活跃的
  });

  return activeSubscriptions;
}

/**
 * 获取订阅的所有周期性订单
 */
export async function getSubscriptionRecurringOrders(parentOrderNo: string): Promise<Order[]> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("orders")
    .select("*")
    .eq("parent_order_no", parentOrderNo)
    .order("billing_cycle_count", { ascending: true });

  if (error) {
    console.error('❌ 查询订阅周期性订单失败:', error);
    return [];
  }

  return data || [];
}

/**
 * 更新订阅状态
 */
export async function updateSubscriptionStatus(
  subscriptionId: string,
  status: SubscriptionStatus,
  nextBillingTime?: string
): Promise<void> {
  const supabase = getSupabaseClient();
  
  const updateData: any = {
    subscription_status: status
  };

  if (nextBillingTime) {
    updateData.next_billing_time = nextBillingTime;
  }

  const { error } = await supabase
    .from("orders")
    .update(updateData)
    .or(`paypal_subscription_id.eq.${subscriptionId},subscription_id.eq.${subscriptionId}`)
    .eq("order_type", OrderType.SUBSCRIPTION);

  if (error) {
    throw error;
  }
}
