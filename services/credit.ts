import { getUserValidCredits, insertCredit } from "@/models/credit";

import { Credit } from "@/types/credit";
import { UserCredits } from "@/types/user";
import { getFirstPaidOrderByUserUuid, getOrdersByUserUuid } from "@/models/order";
import { getIsoTimestr } from "@/lib/time";
import { getSnowId } from "@/lib/hash";

export enum CreditsTransType {
  NewUser = "new_user", // initial credits for new user
  OrderPay = "order_pay", // user pay for credits
  SystemAdd = "system_add", // system add credits
  Ping = "ping", // cost for ping api
  ColoringGenerate = "coloring_generate", // cost for coloring page generation
}

export enum CreditsAmount {
  NewUserGet = 10,
  PingCost = 1,
  ColoringGenerateCost = 3, // 涂色页生成积分消耗
}

// 检查用户是否有活跃的订阅
export async function hasActiveSubscription(user_uuid: string): Promise<boolean> {
  try {
    const orders = await getOrdersByUserUuid(user_uuid);
    if (!orders || orders.length === 0) {
      return false;
    }

    const now = new Date();

    // 检查是否有未过期的订阅
    for (const order of orders) {
      if (order.status === 'paid') {
        // 对于订阅类型的订单，检查订阅状态
        if (order.order_type === 'subscription') {
          // PayPal订阅：检查订阅状态是否为ACTIVE
          if (order.paypal_subscription_id && order.subscription_status === 'ACTIVE') {
            return true;
          }
        }

        // 对于有过期时间的订单（一次性购买或有明确过期时间的订阅）
        if (order.expired_at) {
          const expiredAt = new Date(order.expired_at);
          if (expiredAt > now) {
            return true;
          }
        }
      }
    }

    return false;
  } catch (error) {
    console.error('Error checking active subscription:', error);
    return false;
  }
}

export async function getUserCredits(user_uuid: string): Promise<UserCredits> {
  let user_credits: UserCredits = {
    left_credits: 0,
  };

  try {
    const first_paid_order = await getFirstPaidOrderByUserUuid(user_uuid);
    if (first_paid_order) {
      user_credits.is_recharged = true;
    }

    const credits = await getUserValidCredits(user_uuid);
    if (credits) {
      credits.forEach((v: Credit) => {
        user_credits.left_credits += v.credits;
      });
    }

    if (user_credits.left_credits < 0) {
      user_credits.left_credits = 0;
    }

    // 检查是否有活跃的订阅来确定会员状态
    const hasActiveSub = await hasActiveSubscription(user_uuid);
    user_credits.is_pro = hasActiveSub;

    // 开发环境调试：如果环境变量设置为强制非会员状态，则覆盖会员状态
    if (process.env.NODE_ENV === 'development' && process.env.FORCE_NON_PREMIUM === 'true') {
      console.log('🔧 Development mode: Forcing non-premium status for testing');
      user_credits.is_pro = false;
    }

    // 开发环境调试：如果环境变量设置为无限积分模式，则提供大量积分用于测试
    if (process.env.NODE_ENV === 'development' && process.env.UNLIMITED_CREDITS_FOR_TESTING === 'true') {
      console.log('🔧 Development mode: Providing unlimited credits for testing');
      user_credits.left_credits = 999999; // 提供大量积分用于测试
    }

    return user_credits;
  } catch (e) {
    console.log("get user credits failed: ", e);
    return user_credits;
  }
}

export async function decreaseCredits({
  user_uuid,
  trans_type,
  credits,
}: {
  user_uuid: string;
  trans_type: CreditsTransType;
  credits: number;
}) {
  try {
    let order_no = "";
    let expired_at = "";
    let left_credits = 0;

    const userCredits = await getUserValidCredits(user_uuid);
    if (userCredits) {
      for (let i = 0, l = userCredits.length; i < l; i++) {
        const credit = userCredits[i];
        left_credits += credit.credits;

        // credit enough for cost
        if (left_credits >= credits) {
          order_no = credit.order_no;
          expired_at = credit.expired_at || "";
          break;
        }

        // look for next credit
      }
    }

    const new_credit: Credit = {
      trans_no: getSnowId(),
      created_at: getIsoTimestr(),
      user_uuid: user_uuid,
      trans_type: trans_type,
      credits: 0 - credits,
      order_no: order_no,
      expired_at: expired_at,
    };
    await insertCredit(new_credit);
  } catch (e) {
    console.log("decrease credits failed: ", e);
    throw e;
  }
}

export async function increaseCredits({
  user_uuid,
  trans_type,
  credits,
  expired_at,
  order_no,
}: {
  user_uuid: string;
  trans_type: string;
  credits: number;
  expired_at?: string;
  order_no?: string;
}) {
  try {
    const new_credit: Credit = {
      trans_no: getSnowId(),
      created_at: getIsoTimestr(),
      user_uuid: user_uuid,
      trans_type: trans_type,
      credits: credits,
      order_no: order_no || "",
      expired_at: expired_at || "",
    };
    await insertCredit(new_credit);
  } catch (e) {
    console.log("increase credits failed: ", e);
    throw e;
  }
}
