import { CreditsTransType, increaseCredits } from "./credit";
import { findOrderByOrderNo, updateOrderStatus } from "@/models/order";
import { getIsoTimestr } from "@/lib/time";

/**
 * 处理PayPal订单支付成功
 */
export async function handlePayPalOrderSuccess(orderData: {
  order_no: string;
  user_uuid: string;
  credits: number;
  paypal_order_id: string;
  paid_email?: string;
}) {
  try {
    const { order_no, paypal_order_id, paid_email } = orderData;

    const order = await findOrderByOrderNo(order_no);
    if (!order || order.status !== "created") {
      throw new Error("PayPal订单无效或已处理");
    }

    const paid_at = getIsoTimestr();
    const paid_detail = JSON.stringify({ paypal_order_id, processed_at: paid_at });

    await updateOrderStatus(order_no, "paid", paid_at, paid_email || "", paid_detail);

    if (order.user_uuid && order.credits > 0) {
      await increaseCredits({
        user_uuid: order.user_uuid,
        trans_type: CreditsTransType.OrderPay,
        credits: order.credits,
        expired_at: order.expired_at,
        order_no: order_no,
      });
    }

  } catch (e) {
    throw e;
  }
}

/**
 * 处理PayPal订阅支付成功
 */
export async function handlePayPalSubscriptionSuccess(subscriptionData: {
  subscription_id: string;
  plan_id: string;
  user_uuid: string;
  order_no: string;
  credits: number;
}) {
  try {
    const { subscription_id, plan_id, order_no } = subscriptionData;

    const order = await findOrderByOrderNo(order_no);
    if (!order || order.status !== "created") {
      throw new Error("PayPal订阅订单无效或已处理");
    }

    const paid_at = getIsoTimestr();
    const paid_detail = JSON.stringify({
      paypal_subscription_id: subscription_id,
      paypal_plan_id: plan_id,
      processed_at: paid_at
    });

    await updateOrderStatus(order_no, "paid", paid_at, "", paid_detail);

    if (order.user_uuid && order.credits > 0) {
      await increaseCredits({
        user_uuid: order.user_uuid,
        trans_type: CreditsTransType.OrderPay,
        credits: order.credits,
        expired_at: order.expired_at,
        order_no: order_no,
      });
    }

  } catch (e) {
    throw e;
  }
}
