#!/usr/bin/env node

/**
 * 数据库初始化脚本
 * 用于创建表结构和插入初始数据
 */

const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '.env.local' });

// 支持多种数据库类型
async function initDatabase() {
  const databaseType = process.env.DATABASE_TYPE || 'supabase';
  
  console.log(`🚀 开始初始化数据库 (${databaseType})...`);
  
  try {
    switch (databaseType) {
      case 'supabase':
        await initSupabase();
        break;
      case 'mysql':
        await initMySQL();
        break;
      case 'postgresql':
        await initPostgreSQL();
        break;
      default:
        throw new Error(`不支持的数据库类型: ${databaseType}`);
    }
    
    console.log('✅ 数据库初始化完成！');
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error.message);
    process.exit(1);
  }
}

// Supabase 初始化
async function initSupabase() {
  const { createClient } = require('@supabase/supabase-js');
  
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  if (!supabaseUrl || !supabaseKey) {
    throw new Error('请设置 SUPABASE_URL 和 SUPABASE_SERVICE_ROLE_KEY 环境变量');
  }
  
  const supabase = createClient(supabaseUrl, supabaseKey);
  
  console.log('📝 创建数据库表...');

  // 创建表结构
  const { error: createError } = await supabase.rpc('exec_sql', {
    sql: `
      -- 创建分类表
      CREATE TABLE IF NOT EXISTS categories (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        slug TEXT NOT NULL UNIQUE,
        seo_slug TEXT NOT NULL UNIQUE,
        description TEXT,
        icon TEXT,
        meta_title TEXT,
        meta_description TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        sort_order INTEGER DEFAULT 0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      -- 创建涂色页面表
      CREATE TABLE IF NOT EXISTS coloring_pages (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT,
        image_url TEXT NOT NULL,
        thumbnail_url TEXT,
        alt_text TEXT,
        category_id TEXT NOT NULL,
        seo_slug TEXT UNIQUE NOT NULL,
        tags JSONB,
        difficulty_level TEXT DEFAULT 'easy' CHECK (difficulty_level IN ('easy', 'medium', 'hard')),
        age_group TEXT DEFAULT 'all' CHECK (age_group IN ('toddler', 'preschool', 'school', 'teen', 'adult', 'all')),
        is_featured BOOLEAN DEFAULT FALSE,
        download_count INTEGER DEFAULT 0,
        view_count INTEGER DEFAULT 0,
        status TEXT DEFAULT 'published' CHECK (status IN ('draft', 'published', 'archived')),
        meta_title TEXT,
        meta_description TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

        FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
      );

      -- 创建索引
      CREATE INDEX IF NOT EXISTS idx_categories_slug ON categories(slug);
      CREATE INDEX IF NOT EXISTS idx_categories_seo_slug ON categories(seo_slug);
      CREATE INDEX IF NOT EXISTS idx_categories_active ON categories(is_active);
      CREATE INDEX IF NOT EXISTS idx_coloring_pages_category ON coloring_pages(category_id);
      CREATE INDEX IF NOT EXISTS idx_coloring_pages_featured ON coloring_pages(is_featured);
      CREATE INDEX IF NOT EXISTS idx_coloring_pages_status ON coloring_pages(status);
      CREATE INDEX IF NOT EXISTS idx_coloring_pages_seo_slug ON coloring_pages(seo_slug);
    `
  });
  
  if (createError) {
    throw new Error(`创建表失败: ${createError.message}`);
  }
  
  console.log('📊 插入示例数据...');
  await insertSampleData(supabase);
}

// MySQL 初始化
async function initMySQL() {
  const mysql = require('mysql2/promise');
  
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'makecoloring'
  });
  
  console.log('📝 执行 SQL 脚本...');
  
  const sqlScript = fs.readFileSync(
    path.join(__dirname, '../database/init-database.sql'), 
    'utf8'
  );
  
  // 分割并执行SQL语句
  const statements = sqlScript.split(';').filter(stmt => stmt.trim());
  
  for (const statement of statements) {
    if (statement.trim()) {
      await connection.execute(statement);
    }
  }
  
  await connection.end();
}

// PostgreSQL 初始化
async function initPostgreSQL() {
  const { Client } = require('pg');
  
  const client = new Client({
    connectionString: process.env.DATABASE_URL
  });
  
  await client.connect();
  
  console.log('📝 创建表结构...');
  
  await client.query(`
    CREATE TABLE IF NOT EXISTS coloring_pages (
      id VARCHAR(50) PRIMARY KEY,
      title VARCHAR(255) NOT NULL,
      description TEXT,
      image_url VARCHAR(500) NOT NULL,
      thumbnail_url VARCHAR(500),
      alt_text VARCHAR(255),
      category_id VARCHAR(50) NOT NULL,
      subcategory_id VARCHAR(50),
      seo_slug VARCHAR(255) UNIQUE NOT NULL,
      tags JSONB,
      difficulty_level VARCHAR(10) DEFAULT 'easy' CHECK (difficulty_level IN ('easy', 'medium', 'hard')),
      age_group VARCHAR(20) DEFAULT 'all' CHECK (age_group IN ('toddler', 'preschool', 'school', 'teen', 'adult', 'all')),
      is_featured BOOLEAN DEFAULT FALSE,
      is_premium BOOLEAN DEFAULT FALSE,
      download_count INTEGER DEFAULT 0,
      view_count INTEGER DEFAULT 0,
      status VARCHAR(20) DEFAULT 'published' CHECK (status IN ('draft', 'published', 'archived')),
      source VARCHAR(20) DEFAULT 'static' CHECK (source IN ('static', 'user_generated', 'ai_generated')),
      created_by VARCHAR(50),
      print_size VARCHAR(20) DEFAULT 'A4',
      coloring_tips JSONB,
      color_suggestions JSONB,
      educational_value TEXT,
      usage_scenarios JSONB,
      meta_title VARCHAR(255),
      meta_description TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    
    CREATE INDEX IF NOT EXISTS idx_coloring_pages_category ON coloring_pages(category_id);
    CREATE INDEX IF NOT EXISTS idx_coloring_pages_featured ON coloring_pages(is_featured);
    CREATE INDEX IF NOT EXISTS idx_coloring_pages_status ON coloring_pages(status);
  `);
  
  console.log('📊 插入示例数据...');
  await insertSampleDataPG(client);
  
  await client.end();
}

// 插入示例数据 (Supabase)
async function insertSampleData(supabase) {
  const { categories, pages } = generateSampleData();

  // 先插入分类数据
  const { error: categoryError } = await supabase
    .from('categories')
    .upsert(categories);

  if (categoryError) {
    throw new Error(`插入分类数据失败: ${categoryError.message}`);
  }

  // 再插入页面数据
  const { error: pageError } = await supabase
    .from('coloring_pages')
    .upsert(pages);

  if (pageError) {
    throw new Error(`插入页面数据失败: ${pageError.message}`);
  }

  console.log(`✅ 成功插入 ${categories.length} 个分类和 ${pages.length} 个页面`);
}

// 插入示例数据 (PostgreSQL)
async function insertSampleDataPG(client) {
  const samplePages = generateSamplePages();
  
  for (const page of samplePages) {
    await client.query(`
      INSERT INTO coloring_pages (
        id, title, description, image_url, thumbnail_url, alt_text,
        category_id, seo_slug, tags, difficulty_level, age_group,
        is_featured, coloring_tips, color_suggestions, educational_value,
        usage_scenarios, meta_title, meta_description
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18)
      ON CONFLICT (id) DO UPDATE SET
        title = EXCLUDED.title,
        updated_at = CURRENT_TIMESTAMP
    `, [
      page.id, page.title, page.description, page.image_url, page.thumbnail_url,
      page.alt_text, page.category_id, page.seo_slug, JSON.stringify(page.tags),
      page.difficulty_level, page.age_group, page.is_featured,
      JSON.stringify(page.coloring_tips), JSON.stringify(page.color_suggestions),
      page.educational_value, JSON.stringify(page.usage_scenarios),
      page.meta_title, page.meta_description
    ]);
  }
  
  console.log(`✅ 成功插入 ${samplePages.length} 条记录`);
}

// 生成示例数据
function generateSampleData() {
  // 分类数据
  const categories = [
    {
      id: 'animal',
      name: 'Animals',
      slug: 'animals',
      seo_slug: 'animals-coloring-pages',
      description: 'Fun animal coloring pages for kids featuring cats, dogs, lions, elephants and more wildlife creatures.',
      icon: '🐾',
      meta_title: 'Free Animal Coloring Pages - Printable Animal Coloring Sheets',
      meta_description: 'Download free animal coloring pages for kids. Features cats, dogs, lions, elephants and more wildlife creatures. Perfect for creative learning.',
      sort_order: 1
    },
    {
      id: 'cartoon',
      name: 'Cartoon',
      slug: 'cartoon',
      seo_slug: 'cartoon-coloring-pages',
      description: 'Popular cartoon character coloring pages from beloved animated shows and movies.',
      icon: '🎭',
      meta_title: 'Free Cartoon Coloring Pages - Printable Cartoon Character Sheets',
      meta_description: 'Download free cartoon coloring pages featuring popular animated characters from movies and TV shows. Perfect for kids who love cartoons.',
      sort_order: 2
    },
    {
      id: 'superheroes-villains',
      name: 'Superheroes',
      slug: 'superheroes',
      seo_slug: 'superheroes-coloring-pages',
      description: 'Action-packed superhero and villain coloring pages featuring your favorite comic book characters.',
      icon: '🦸',
      meta_title: 'Free Superhero Coloring Pages - Printable Comic Character Sheets',
      meta_description: 'Download free superhero coloring pages featuring popular comic book heroes and villains. Perfect for young superhero fans.',
      sort_order: 3
    },
    {
      id: 'prince-princess',
      name: 'Princess',
      slug: 'princess',
      seo_slug: 'princess-coloring-pages',
      description: 'Beautiful princess and fairy tale coloring pages with castles, gowns, and magical themes.',
      icon: '👸',
      meta_title: 'Free Princess Coloring Pages - Printable Fairy Tale Sheets',
      meta_description: 'Download free princess coloring pages featuring beautiful princesses, castles, and fairy tale themes. Perfect for imaginative play.',
      sort_order: 4
    },
    {
      id: 'flowers-plants',
      name: 'Flowers & Plants',
      slug: 'flowers-plants',
      seo_slug: 'flowers-plants-coloring-pages',
      description: 'Beautiful flower and plant coloring pages featuring roses, sunflowers, trees, and garden scenes.',
      icon: '🌸',
      meta_title: 'Free Flower Coloring Pages - Printable Plant & Garden Sheets',
      meta_description: 'Download free flower and plant coloring pages featuring roses, sunflowers, trees and beautiful garden scenes. Perfect for nature lovers.',
      sort_order: 5
    },
    {
      id: 'transportation',
      name: 'Transportation',
      slug: 'transportation',
      seo_slug: 'transportation-coloring-pages',
      description: 'Vehicle coloring pages including cars, trucks, airplanes, trains, and boats for transportation enthusiasts.',
      icon: '🚗',
      meta_title: 'Free Transportation Coloring Pages - Printable Vehicle Sheets',
      meta_description: 'Download free transportation coloring pages featuring cars, trucks, airplanes, trains and boats. Perfect for vehicle enthusiasts.',
      sort_order: 6
    }
  ];

  // 页面数据
  const pages = [
    // 动物分类页面
    {
      id: 'animal-1',
      title: 'Cute Lion Coloring Page',
      description: 'A friendly cartoon lion perfect for kids to color. Features simple lines and fun details.',
      image_url: 'https://picsum.photos/400/400?random=animal-1',
      thumbnail_url: 'https://picsum.photos/200/200?random=animal-1',
      alt_text: 'Cute lion coloring page for kids',
      category_id: 'animal',
      seo_slug: 'cute-lion-coloring-page',
      tags: ['lion', 'animal', 'kids', 'easy', 'cartoon'],
      difficulty_level: 'easy',
      age_group: 'preschool',
      is_featured: true,
      meta_title: 'Cute Lion Coloring Page - Free Printable Animal Coloring Sheet',
      meta_description: 'Download this adorable lion coloring page for kids. Perfect for preschoolers learning about animals and developing creativity.'
    },
    {
      id: 'animal-2',
      title: 'Realistic Elephant Coloring Page',
      description: 'Detailed elephant coloring page with intricate patterns, perfect for older kids and adults.',
      image_url: 'https://picsum.photos/400/400?random=animal-2',
      thumbnail_url: 'https://picsum.photos/200/200?random=animal-2',
      alt_text: 'Realistic elephant coloring page',
      category_id: 'animal',
      seo_slug: 'realistic-elephant-coloring-page',
      tags: ['elephant', 'animal', 'realistic', 'detailed', 'adults'],
      difficulty_level: 'hard',
      age_group: 'adult',
      is_featured: true,
      meta_title: 'Realistic Elephant Coloring Page - Detailed Animal Coloring Sheet',
      meta_description: 'Download this detailed elephant coloring page perfect for adults and advanced colorists. Features realistic details and intricate patterns.'
    },
    // 卡通分类页面
    {
      id: 'cartoon-1',
      title: 'Happy Robot Coloring Page',
      description: 'Friendly robot character with simple geometric shapes, perfect for young children.',
      image_url: 'https://picsum.photos/400/400?random=cartoon-1',
      thumbnail_url: 'https://picsum.photos/200/200?random=cartoon-1',
      alt_text: 'Happy robot cartoon coloring page',
      category_id: 'cartoon',
      seo_slug: 'happy-robot-coloring-page',
      tags: ['robot', 'cartoon', 'kids', 'geometric', 'technology'],
      difficulty_level: 'easy',
      age_group: 'preschool',
      is_featured: true,
      meta_title: 'Happy Robot Coloring Page - Fun Cartoon Coloring Sheet',
      meta_description: 'Download this cheerful robot coloring page perfect for kids interested in technology and robots. Simple and fun design.'
    },
    // 超级英雄分类页面
    {
      id: 'superheroes-1',
      title: 'Super Hero Flying Coloring Page',
      description: 'Dynamic superhero in action pose, perfect for young superhero fans.',
      image_url: 'https://picsum.photos/400/400?random=superheroes-1',
      thumbnail_url: 'https://picsum.photos/200/200?random=superheroes-1',
      alt_text: 'Super hero flying coloring page',
      category_id: 'superheroes-villains',
      seo_slug: 'super-hero-flying-coloring-page',
      tags: ['superhero', 'flying', 'action', 'kids', 'comic'],
      difficulty_level: 'medium',
      age_group: 'school',
      is_featured: true,
      meta_title: 'Super Hero Flying Coloring Page - Action Superhero Sheet',
      meta_description: 'Download this exciting superhero coloring page featuring a hero in flight. Perfect for kids who love action and adventure.'
    }
  ];

  return { categories, pages };
}

// 执行初始化
if (require.main === module) {
  initDatabase();
}

module.exports = { initDatabase };
