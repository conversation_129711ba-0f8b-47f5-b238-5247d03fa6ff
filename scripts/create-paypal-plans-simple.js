#!/usr/bin/env node

/**
 * 简化版PayPal订阅计划创建脚本
 * 使用axios库来处理HTTP请求，可能有更好的网络兼容性
 */

const fs = require('fs');
const path = require('path');

// 尝试使用axios，如果没有安装则回退到原生http
let axios;
try {
  axios = require('axios');
} catch (e) {
  console.log('⚠️ axios未安装，使用原生http模块');
  axios = null;
}

// 加载环境变量
function loadEnvFile(envFile = '.env.production') {
  const envPath = path.join(process.cwd(), envFile);
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split('=');
        if (key && valueParts.length > 0) {
          let value = valueParts.join('=').trim();
          value = value.replace(/^["'\s]+|["'\s]+$/g, '');
          if (!process.env[key.trim()]) {
            process.env[key.trim()] = value;
          }
        }
      }
    }
    console.log(`✅ 已加载 ${envFile} 文件`);
  }
}

// 从前端配置加载定价信息
function loadPricingConfig() {
  try {
    const pricingPath = path.join(process.cwd(), 'i18n/pages/landing/en.json');
    const pricingContent = fs.readFileSync(pricingPath, 'utf8');
    const pricingData = JSON.parse(pricingContent);
    return pricingData.pricing.items || [];
  } catch (error) {
    console.error('❌ 无法加载定价配置:', error.message);
    return [];
  }
}

// 生成订阅计划配置
function generateSubscriptionPlans() {
  const pricingItems = loadPricingConfig();
  const subscriptionPlans = [];
  
  const PRODUCT_ID_TO_ENV_KEY = {
    'basic_monthly': 'PAYPAL_BASIC_MONTHLY_PLAN_ID',
    'pro_monthly': 'PAYPAL_PRO_MONTHLY_PLAN_ID',
    'premium_monthly': 'PAYPAL_PREMIUM_MONTHLY_PLAN_ID',
    'basic_yearly': 'PAYPAL_BASIC_YEARLY_PLAN_ID',
    'pro_yearly': 'PAYPAL_PRO_YEARLY_PLAN_ID',
    'premium_yearly': 'PAYPAL_PREMIUM_YEARLY_PLAN_ID'
  };

  for (const item of pricingItems) {
    if (item.interval === 'month' || item.interval === 'year') {
      const envKey = PRODUCT_ID_TO_ENV_KEY[item.product_id];
      if (!envKey) {
        console.warn(`⚠️ 未找到产品 ${item.product_id} 的环境变量映射，跳过`);
        continue;
      }

      const plan = {
        name: item.product_name || `${item.title} Plan`,
        description: `${item.title} subscription with ${item.credits} credits`,
        interval: item.interval === 'month' ? 'MONTH' : 'YEAR',
        interval_count: 1,
        price: (item.amount / 100).toFixed(2),
        credits: item.credits,
        env_key: envKey,
        product_id: item.product_id
      };

      subscriptionPlans.push(plan);
      console.log(`✅ 生成订阅计划: ${plan.name} - $${plan.price}`);
    }
  }

  return subscriptionPlans;
}

// 使用curl命令作为备选方案
async function curlRequest(url, options, data = null) {
  return new Promise((resolve, reject) => {
    const { spawn } = require('child_process');
    
    let curlArgs = ['-s', '-X', options.method || 'GET'];
    
    // 添加headers
    if (options.headers) {
      for (const [key, value] of Object.entries(options.headers)) {
        curlArgs.push('-H', `${key}: ${value}`);
      }
    }
    
    // 添加数据
    if (data) {
      curlArgs.push('-d', typeof data === 'string' ? data : JSON.stringify(data));
    }
    
    curlArgs.push(url);
    
    const curl = spawn('curl', curlArgs);
    let stdout = '';
    let stderr = '';
    
    curl.stdout.on('data', (data) => {
      stdout += data;
    });
    
    curl.stderr.on('data', (data) => {
      stderr += data;
    });
    
    curl.on('close', (code) => {
      if (code === 0) {
        try {
          const parsedData = JSON.parse(stdout);
          resolve({
            statusCode: 200,
            data: parsedData
          });
        } catch (error) {
          resolve({
            statusCode: 200,
            data: stdout
          });
        }
      } else {
        reject(new Error(`curl failed with code ${code}: ${stderr}`));
      }
    });
  });
}

// 获取PayPal访问令牌
async function getAccessToken() {
  console.log('🔑 获取PayPal访问令牌...');

  const config = {
    clientId: process.env.PAYPAL_CLIENT_ID,
    clientSecret: process.env.PAYPAL_CLIENT_SECRET,
    environment: (process.env.PAYPAL_ENVIRONMENT || 'sandbox').replace(/["'\s#].*$/, '').trim(),
  };

  const PAYPAL_BASE_URL = config.environment === 'production'
    ? 'https://api-m.paypal.com'
    : 'https://api-m.sandbox.paypal.com';

  const auth = Buffer.from(`${config.clientId}:${config.clientSecret}`).toString('base64');

  try {
    const response = await curlRequest(`${PAYPAL_BASE_URL}/v1/oauth2/token`, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${auth}`,
        'Accept': 'application/json',
        'Accept-Language': 'en_US',
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }, 'grant_type=client_credentials');

    if (response.data && response.data.access_token) {
      console.log('✅ 访问令牌获取成功');
      return { token: response.data.access_token, baseUrl: PAYPAL_BASE_URL };
    } else {
      throw new Error(`获取访问令牌失败: ${JSON.stringify(response.data)}`);
    }
  } catch (error) {
    throw new Error(`获取访问令牌失败: ${error.message}`);
  }
}

// 创建产品
async function createProduct(accessToken, baseUrl) {
  console.log('📦 创建PayPal产品...');

  const productData = {
    name: 'MakeColoring Subscription',
    description: 'MakeColoring subscription service for AI-powered coloring page generation',
    type: 'SERVICE',
    category: 'SOFTWARE'
  };

  try {
    const response = await curlRequest(`${baseUrl}/v1/catalogs/products`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    }, productData);

    if (response.data && response.data.id) {
      console.log('✅ 产品创建成功:', response.data.id);
      return response.data.id;
    } else {
      throw new Error(`创建产品失败: ${JSON.stringify(response.data)}`);
    }
  } catch (error) {
    throw new Error(`创建产品失败: ${error.message}`);
  }
}

// 创建订阅计划
async function createSubscriptionPlan(accessToken, baseUrl, productId, planConfig) {
  console.log(`📋 创建订阅计划: ${planConfig.name}...`);

  const planData = {
    product_id: productId,
    name: planConfig.name,
    description: planConfig.description,
    billing_cycles: [{
      frequency: {
        interval_unit: planConfig.interval,
        interval_count: planConfig.interval_count
      },
      tenure_type: 'REGULAR',
      sequence: 1,
      total_cycles: 0, // 无限循环
      pricing_scheme: {
        fixed_price: {
          value: planConfig.price,
          currency_code: 'USD'
        }
      }
    }],
    payment_preferences: {
      auto_bill_outstanding: true,
      setup_fee: {
        value: '0',
        currency_code: 'USD'
      },
      setup_fee_failure_action: 'CONTINUE',
      payment_failure_threshold: 3
    },
    taxes: {
      percentage: '0',
      inclusive: false
    }
  };

  try {
    const response = await curlRequest(`${baseUrl}/v1/billing/plans`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    }, planData);

    if (response.data && response.data.id) {
      console.log(`✅ 订阅计划创建成功: ${planConfig.name} -> ${response.data.id}`);
      return {
        planId: response.data.id,
        envKey: planConfig.env_key,
        name: planConfig.name
      };
    } else {
      throw new Error(`创建订阅计划失败: ${JSON.stringify(response.data)}`);
    }
  } catch (error) {
    throw new Error(`创建订阅计划失败 (${planConfig.name}): ${error.message}`);
  }
}

// 主函数
async function main() {
  console.log('🚀 开始创建PayPal订阅计划...');
  
  // 加载环境变量
  loadEnvFile('.env.production');
  
  const config = {
    clientId: process.env.PAYPAL_CLIENT_ID,
    clientSecret: process.env.PAYPAL_CLIENT_SECRET,
    environment: (process.env.PAYPAL_ENVIRONMENT || 'sandbox').replace(/["'\s#].*$/, '').trim(),
  };
  
  console.log(`环境: ${config.environment}`);
  
  if (!config.clientId || !config.clientSecret) {
    console.error('❌ 缺少PayPal配置信息');
    console.error('请确保在.env.production文件中设置了:');
    console.error('- PAYPAL_CLIENT_ID');
    console.error('- PAYPAL_CLIENT_SECRET');
    console.error('- PAYPAL_ENVIRONMENT');
    process.exit(1);
  }

  try {
    // 1. 获取访问令牌
    const { token: accessToken, baseUrl } = await getAccessToken();

    // 2. 创建产品
    const productId = await createProduct(accessToken, baseUrl);

    // 3. 生成订阅计划配置
    console.log('\n📋 从前端配置生成订阅计划...');
    const subscriptionPlans = generateSubscriptionPlans();

    if (subscriptionPlans.length === 0) {
      console.error('❌ 未找到任何订阅产品配置');
      process.exit(1);
    }

    // 4. 创建所有订阅计划
    const createdPlans = [
      // 已创建的计划
      { planId: 'P-1T382382832177747NBWSXRQ', envKey: 'PAYPAL_BASIC_MONTHLY_PLAN_ID', name: 'Make Coloring Basic (Monthly)' },
      { planId: 'P-83W50449D22861524NBWSXTQ', envKey: 'PAYPAL_PRO_MONTHLY_PLAN_ID', name: 'Make Coloring Pro (Monthly)' }
    ];

    // 需要创建的剩余计划
    const remainingPlans = subscriptionPlans.filter(plan =>
      !createdPlans.some(created => created.envKey === plan.env_key)
    );

    console.log(`\n📋 需要创建剩余 ${remainingPlans.length} 个计划...`);

    for (const planConfig of remainingPlans) {
      try {
        const result = await createSubscriptionPlan(accessToken, baseUrl, productId, planConfig);
        createdPlans.push(result);

        // 添加延迟避免API限制
        await new Promise(resolve => setTimeout(resolve, 3000));
      } catch (error) {
        console.error(`❌ 创建计划失败: ${planConfig.name} - ${error.message}`);
        console.log('⏳ 继续创建下一个计划...');
      }
    }

    // 5. 输出结果
    console.log('\n🎉 所有订阅计划创建完成！');
    console.log('\n📋 请将以下环境变量添加到您的.env.production文件中:');
    console.log('# PayPal订阅计划ID (生产环境)');
    console.log('# 月度计划');

    const monthlyPlans = createdPlans.filter(p => p.envKey.includes('MONTHLY'));
    monthlyPlans.forEach(plan => {
      console.log(`${plan.envKey}="${plan.planId}"`);
    });

    console.log('# 年度计划');
    const yearlyPlans = createdPlans.filter(p => p.envKey.includes('YEARLY'));
    yearlyPlans.forEach(plan => {
      console.log(`${plan.envKey}="${plan.planId}"`);
    });

    console.log('\n✅ 请复制上述环境变量到.env.production文件中，替换现有的沙盒计划ID');

  } catch (error) {
    console.error('❌ 创建失败:', error.message);
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main();
}
