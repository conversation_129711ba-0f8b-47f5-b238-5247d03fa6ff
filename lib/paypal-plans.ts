// PayPal订阅计划配置
// 只存储PayPal特有的配置，产品信息从前端配置中获取

export interface PayPalPlanMapping {
  product_id: string;
  paypal_plan_id: string;
}

// PayPal Plan ID映射表
// 只存储product_id到PayPal Plan ID的映射关系
// 注意：这些Plan ID需要通过API创建，运行 scripts/create-paypal-plans.js 来创建
export const PAYPAL_PLAN_MAPPINGS: PayPalPlanMapping[] = [
  // 月度计划
  { product_id: 'basic_monthly', paypal_plan_id: process.env.PAYPAL_BASIC_MONTHLY_PLAN_ID || '' },
  { product_id: 'pro_monthly', paypal_plan_id: process.env.PAYPAL_PRO_MONTHLY_PLAN_ID || '' },
  { product_id: 'premium_monthly', paypal_plan_id: process.env.PAYPAL_PREMIUM_MONTHLY_PLAN_ID || '' },

  // 年度计划
  { product_id: 'basic_yearly', paypal_plan_id: process.env.PAYPAL_BASIC_YEARLY_PLAN_ID || '' },
  { product_id: 'pro_yearly', paypal_plan_id: process.env.PAYPAL_PRO_YEARLY_PLAN_ID || '' },
  { product_id: 'premium_yearly', paypal_plan_id: process.env.PAYPAL_PREMIUM_YEARLY_PLAN_ID || '' }
];

// 兼容性接口，保持与原有代码的兼容
export interface PayPalPlan {
  product_id: string;
  paypal_plan_id: string;
  product_name: string;
  interval: 'month' | 'year';
  credits: number;
  amount: number; // 以分为单位
  currency: string;
  valid_months: number;
  description?: string;
}

/**
 * 根据product_id获取PayPal Plan ID
 */
export function getPayPalPlanId(product_id: string): string | undefined {
  const mapping = PAYPAL_PLAN_MAPPINGS.find(mapping => mapping.product_id === product_id);
  if (mapping && !mapping.paypal_plan_id) {
    throw new Error(`PayPal Plan ID未配置: ${product_id}。请运行 'node scripts/create-paypal-plans.js' 创建订阅计划。`);
  }
  return mapping?.paypal_plan_id;
}

/**
 * 根据PayPal Plan ID获取product_id
 */
export function getProductIdByPlanId(paypal_plan_id: string): string | undefined {
  const mapping = PAYPAL_PLAN_MAPPINGS.find(mapping => mapping.paypal_plan_id === paypal_plan_id);
  return mapping?.product_id;
}

/**
 * 验证product_id是否支持PayPal订阅
 */
export function isPayPalSubscriptionSupported(product_id: string): boolean {
  return PAYPAL_PLAN_MAPPINGS.some(mapping => mapping.product_id === product_id);
}

/**
 * 获取所有支持PayPal订阅的product_id列表
 */
export function getSupportedProductIds(): string[] {
  return PAYPAL_PLAN_MAPPINGS.map(mapping => mapping.product_id);
}

// 兼容性函数：根据product_id获取PayPal计划配置（已废弃，建议使用getPayPalPlanId）
export function getPayPalPlanByProductId(product_id: string): PayPalPlan | undefined {
  console.warn('getPayPalPlanByProductId is deprecated. Use getPayPalPlanId instead.');
  const paypal_plan_id = getPayPalPlanId(product_id);
  if (!paypal_plan_id) return undefined;

  // 返回最小化的兼容对象，实际产品信息应该从前端配置获取
  return {
    product_id,
    paypal_plan_id,
    product_name: '', // 应该从前端配置获取
    interval: product_id.includes('monthly') ? 'month' : 'year',
    credits: 0, // 应该从前端配置获取
    amount: 0, // 应该从前端配置获取
    currency: 'USD',
    valid_months: product_id.includes('monthly') ? 1 : 12,
  };
}
