/**
 * 统一的文件命名工具
 * 用于生成R2存储的文件key和下载文件名
 */

/**
 * 获取当前日期字符串 (YYYY-MM-DD格式)
 */
export function getDateString(): string {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

/**
 * 获取域名（用于文件命名）
 */
export function getDomainForNaming(): string {
  const webUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://makecoloring.com';
  const domain = webUrl.replace(/^https?:\/\//, '').replace(/^www\./, '');
  return domain === 'localhost:3000' ? 'makecoloring.com' : domain;
}

/**
 * 生成R2存储的文件key
 * 格式: ${date}/${provider}_${uuid}_${index}.png
 * 
 * @param provider - 提供商名称 (replicate, apicore, kie等)
 * @param uuid - 唯一标识符
 * @param index - 图片索引
 * @param extension - 文件扩展名，默认为png
 * @returns 文件key
 */
export function generateStorageKey(
  provider: string,
  uuid: string,
  index: number,
  extension: string = 'png'
): string {
  const date = getDateString();
  return `${date}/${provider}_${uuid}_${index}.${extension}`;
}

/**
 * 生成下载文件名
 * 格式: ${domain}_${uuid}_${index}.${extension}
 * 
 * @param uuid - 唯一标识符
 * @param index - 图片索引
 * @param extension - 文件扩展名，默认为png
 * @param domain - 域名，可选，默认从环境变量获取
 * @returns 下载文件名
 */
export function generateDownloadFilename(
  uuid: string,
  index: number,
  extension: string = 'png',
  domain?: string
): string {
  const domainName = domain || getDomainForNaming();
  return `${domainName}_${uuid}_${index}.${extension}`;
}

/**
 * 生成PDF下载文件名
 * 格式: ${domain}_${uuid}.pdf
 * 
 * @param uuid - 唯一标识符
 * @param domain - 域名，可选，默认从环境变量获取
 * @returns PDF文件名
 */
export function generatePDFFilename(
  uuid: string,
  domain?: string
): string {
  const domainName = domain || getDomainForNaming();
  return `${domainName}_${uuid}.pdf`;
}

/**
 * 从图片URL中提取UUID（用于下载时的文件命名）
 * 支持多种URL格式的UUID提取
 * 
 * @param imageUrl - 图片URL
 * @returns UUID或null
 */
export function extractUuidFromUrl(imageUrl: string): string | null {
  try {
    // 尝试从URL路径中提取UUID
    const urlParts = imageUrl.split('/');
    const filename = urlParts[urlParts.length - 1];
    
    // 移除文件扩展名
    const nameWithoutExt = filename.split('.')[0];
    
    // 尝试匹配不同的命名格式
    // 格式1: provider_uuid_index
    const match1 = nameWithoutExt.match(/^[^_]+_([a-f0-9-]{36})_\d+$/);
    if (match1) {
      return match1[1];
    }
    
    // 格式2: domain_uuid_index
    const match2 = nameWithoutExt.match(/^[^_]+_([a-f0-9-]{36})_\d+$/);
    if (match2) {
      return match2[1];
    }
    
    // 格式3: 直接是UUID
    const uuidRegex = /[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}/;
    const match3 = nameWithoutExt.match(uuidRegex);
    if (match3) {
      return match3[0];
    }
    
    return null;
  } catch (error) {
    console.warn('Failed to extract UUID from URL:', imageUrl, error);
    return null;
  }
}

/**
 * 生成兼容旧格式的文件key（用于向后兼容）
 * 
 * @param domain - 域名
 * @param uuid - 唯一标识符
 * @param index - 图片索引
 * @param extension - 文件扩展名
 * @returns 旧格式的文件key
 */
export function generateLegacyKey(
  domain: string,
  uuid: string,
  index: number,
  extension: string = 'png'
): string {
  return `${domain}_${uuid}_${index}.${extension}`;
}
