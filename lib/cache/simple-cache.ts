/**
 * 简单内存缓存系统
 * 不依赖外部Redis，适用于开发和小规模部署
 */

interface CacheOptions {
  ttl?: number; // 生存时间（秒）
  tags?: string[]; // 缓存标签，用于批量清除
}

interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number;
  tags: string[];
}

class SimpleCacheManager {
  private static instance: SimpleCacheManager;
  private cache = new Map<string, CacheItem<any>>();
  private tagIndex = new Map<string, Set<string>>(); // tag -> keys

  private constructor() {
    this.startCleanupInterval();
  }

  public static getInstance(): SimpleCacheManager {
    if (!SimpleCacheManager.instance) {
      SimpleCacheManager.instance = new SimpleCacheManager();
    }
    return SimpleCacheManager.instance;
  }

  /**
   * 设置缓存
   */
  async set<T>(key: string, data: T, options: CacheOptions = {}): Promise<void> {
    const ttl = (options.ttl || 300) * 1000; // 转换为毫秒
    const tags = options.tags || [];
    
    const cacheItem: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      ttl,
      tags
    };

    this.cache.set(key, cacheItem);

    // 更新标签索引
    for (const tag of tags) {
      if (!this.tagIndex.has(tag)) {
        this.tagIndex.set(tag, new Set());
      }
      this.tagIndex.get(tag)!.add(key);
    }
  }

  /**
   * 获取缓存
   */
  async get<T>(key: string): Promise<T | null> {
    const cached = this.cache.get(key);
    if (!cached) {
      return null;
    }

    // 检查是否过期
    if (Date.now() - cached.timestamp >= cached.ttl) {
      this.delete(key);
      return null;
    }

    return cached.data as T;
  }

  /**
   * 删除缓存
   */
  async delete(key: string): Promise<void> {
    const cached = this.cache.get(key);
    if (cached) {
      // 从标签索引中移除
      for (const tag of cached.tags) {
        const tagKeys = this.tagIndex.get(tag);
        if (tagKeys) {
          tagKeys.delete(key);
          if (tagKeys.size === 0) {
            this.tagIndex.delete(tag);
          }
        }
      }
    }
    
    this.cache.delete(key);
  }

  /**
   * 根据标签清除缓存
   */
  async deleteByTag(tag: string): Promise<void> {
    const keys = this.tagIndex.get(tag);
    if (keys) {
      for (const key of keys) {
        await this.delete(key);
      }
    }
  }

  /**
   * 根据前缀清除缓存
   */
  async deleteByPrefix(prefix: string): Promise<void> {
    const keysToDelete: string[] = [];
    this.cache.forEach((_, key) => {
      if (key.startsWith(prefix)) {
        keysToDelete.push(key);
      }
    });
    
    for (const key of keysToDelete) {
      await this.delete(key);
    }
  }

  /**
   * 清除所有缓存
   */
  async clear(): Promise<void> {
    this.cache.clear();
    this.tagIndex.clear();
  }

  /**
   * 获取缓存统计信息
   */
  async getStats(): Promise<{
    type: 'memory';
    keys: number;
    tags: number;
    memoryUsage: string;
  }> {
    const memoryUsage = process.memoryUsage();
    return {
      type: 'memory',
      keys: this.cache.size,
      tags: this.tagIndex.size,
      memoryUsage: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`
    };
  }

  /**
   * 清理过期的缓存
   */
  private cleanupExpired(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];
    
    this.cache.forEach((item, key) => {
      if (now - item.timestamp >= item.ttl) {
        keysToDelete.push(key);
      }
    });
    
    keysToDelete.forEach(key => {
      this.delete(key);
    });

    if (keysToDelete.length > 0) {
      console.log(`🧹 清理了 ${keysToDelete.length} 个过期缓存项`);
    }
  }

  /**
   * 启动定期清理
   */
  private startCleanupInterval(): void {
    // 每5分钟清理一次过期缓存
    setInterval(() => {
      this.cleanupExpired();
    }, 5 * 60 * 1000);

    console.log('📝 内存缓存系统已启动');
  }

  /**
   * 获取缓存详情（调试用）
   */
  getCacheDetails(): {
    totalKeys: number;
    totalTags: number;
    cacheKeys: string[];
    tagIndex: Record<string, string[]>;
  } {
    const tagIndex: Record<string, string[]> = {};
    this.tagIndex.forEach((keys, tag) => {
      tagIndex[tag] = Array.from(keys);
    });

    return {
      totalKeys: this.cache.size,
      totalTags: this.tagIndex.size,
      cacheKeys: Array.from(this.cache.keys()),
      tagIndex
    };
  }
}

// 导出单例实例
export const cacheManager = SimpleCacheManager.getInstance();

// 便捷函数
export async function setCache<T>(key: string, data: T, options?: CacheOptions): Promise<void> {
  return await cacheManager.set(key, data, options);
}

export async function getCache<T>(key: string): Promise<T | null> {
  return await cacheManager.get<T>(key);
}

export async function deleteCache(key: string): Promise<void> {
  return await cacheManager.delete(key);
}

export async function deleteCacheByTag(tag: string): Promise<void> {
  return await cacheManager.deleteByTag(tag);
}

export async function deleteCacheByPrefix(prefix: string): Promise<void> {
  return await cacheManager.deleteByPrefix(prefix);
}

export async function clearAllCache(): Promise<void> {
  return await cacheManager.clear();
}

export async function getCacheStats() {
  return await cacheManager.getStats();
}

// 优雅关闭处理
process.on('SIGINT', async () => {
  console.log('🛑 正在清理缓存...');
  await clearAllCache();
});

process.on('SIGTERM', async () => {
  console.log('🛑 正在清理缓存...');
  await clearAllCache();
});
