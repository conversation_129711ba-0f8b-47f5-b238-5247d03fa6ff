/**
 * Redis缓存管理（可选）
 * 如果没有Redis，会回退到内存缓存
 */

interface CacheOptions {
  ttl?: number; // 生存时间（秒）
  tags?: string[]; // 缓存标签，用于批量清除
}

interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number;
  tags: string[];
}

class CacheManager {
  private static instance: CacheManager;
  private memoryCache = new Map<string, CacheItem<any>>();
  private redisClient: any = null;
  private useRedis = false;

  private constructor() {
    this.initializeRedis();
  }

  public static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  private async initializeRedis() {
    try {
      // 尝试连接Redis（如果配置了的话）
      if (process.env.REDIS_URL) {
        try {
          const redis = await import('redis');
          this.redisClient = redis.createClient({
            url: process.env.REDIS_URL
          });

          await this.redisClient.connect();
          this.useRedis = true;
          console.log('✅ Redis缓存已启用');
        } catch (importError) {
          console.warn('⚠️ Redis模块未安装，使用内存缓存。如需Redis支持，请运行: npm install redis');
          this.useRedis = false;
        }
      } else {
        console.log('📝 使用内存缓存（未配置Redis）');
      }
    } catch (error) {
      console.warn('⚠️ Redis初始化失败，回退到内存缓存:', error);
      this.useRedis = false;
    }
  }

  /**
   * 设置缓存
   */
  async set<T>(key: string, data: T, options: CacheOptions = {}): Promise<void> {
    const ttl = options.ttl || 300; // 默认5分钟
    const tags = options.tags || [];
    
    const cacheItem: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl * 1000, // 转换为毫秒
      tags
    };

    if (this.useRedis && this.redisClient) {
      try {
        await this.redisClient.setEx(key, ttl, JSON.stringify(cacheItem));
        
        // 为每个标签创建索引
        for (const tag of tags) {
          await this.redisClient.sAdd(`tag:${tag}`, key);
          await this.redisClient.expire(`tag:${tag}`, ttl);
        }
      } catch (error) {
        console.error('Redis设置缓存失败:', error);
        // 回退到内存缓存
        this.memoryCache.set(key, cacheItem);
      }
    } else {
      this.memoryCache.set(key, cacheItem);
    }
  }

  /**
   * 获取缓存
   */
  async get<T>(key: string): Promise<T | null> {
    if (this.useRedis && this.redisClient) {
      try {
        const cached = await this.redisClient.get(key);
        if (cached) {
          const cacheItem: CacheItem<T> = JSON.parse(cached);
          return cacheItem.data;
        }
      } catch (error) {
        console.error('Redis获取缓存失败:', error);
      }
    }

    // 内存缓存
    const cached = this.memoryCache.get(key);
    if (cached) {
      // 检查是否过期
      if (Date.now() - cached.timestamp < cached.ttl) {
        return cached.data;
      } else {
        this.memoryCache.delete(key);
      }
    }

    return null;
  }

  /**
   * 删除缓存
   */
  async delete(key: string): Promise<void> {
    if (this.useRedis && this.redisClient) {
      try {
        await this.redisClient.del(key);
      } catch (error) {
        console.error('Redis删除缓存失败:', error);
      }
    }
    
    this.memoryCache.delete(key);
  }

  /**
   * 根据标签清除缓存
   */
  async deleteByTag(tag: string): Promise<void> {
    if (this.useRedis && this.redisClient) {
      try {
        const keys = await this.redisClient.sMembers(`tag:${tag}`);
        if (keys.length > 0) {
          await this.redisClient.del(keys);
          await this.redisClient.del(`tag:${tag}`);
        }
      } catch (error) {
        console.error('Redis按标签删除缓存失败:', error);
      }
    }

    // 内存缓存按标签删除
    const keysToDelete: string[] = [];
    this.memoryCache.forEach((item, key) => {
      if (item.tags.includes(tag)) {
        keysToDelete.push(key);
      }
    });
    keysToDelete.forEach(key => this.memoryCache.delete(key));
  }

  /**
   * 根据前缀清除缓存
   */
  async deleteByPrefix(prefix: string): Promise<void> {
    if (this.useRedis && this.redisClient) {
      try {
        const keys = await this.redisClient.keys(`${prefix}*`);
        if (keys.length > 0) {
          await this.redisClient.del(keys);
        }
      } catch (error) {
        console.error('Redis按前缀删除缓存失败:', error);
      }
    }

    // 内存缓存按前缀删除
    const keysToDelete: string[] = [];
    this.memoryCache.forEach((_, key) => {
      if (key.startsWith(prefix)) {
        keysToDelete.push(key);
      }
    });
    keysToDelete.forEach(key => this.memoryCache.delete(key));
  }

  /**
   * 清除所有缓存
   */
  async clear(): Promise<void> {
    if (this.useRedis && this.redisClient) {
      try {
        await this.redisClient.flushDb();
      } catch (error) {
        console.error('Redis清除所有缓存失败:', error);
      }
    }
    
    this.memoryCache.clear();
  }

  /**
   * 获取缓存统计信息
   */
  async getStats(): Promise<{
    type: 'redis' | 'memory';
    keys: number;
    memoryUsage?: string;
  }> {
    if (this.useRedis && this.redisClient) {
      try {
        const info = await this.redisClient.info('memory');
        const keys = await this.redisClient.dbSize();
        return {
          type: 'redis',
          keys,
          memoryUsage: info
        };
      } catch (error) {
        console.error('获取Redis统计失败:', error);
      }
    }

    return {
      type: 'memory',
      keys: this.memoryCache.size
    };
  }

  /**
   * 清理过期的内存缓存
   */
  private cleanupExpiredMemoryCache(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];
    
    this.memoryCache.forEach((item, key) => {
      if (now - item.timestamp >= item.ttl) {
        keysToDelete.push(key);
      }
    });
    
    keysToDelete.forEach(key => this.memoryCache.delete(key));
  }

  /**
   * 启动定期清理
   */
  startCleanupInterval(): void {
    // 每5分钟清理一次过期的内存缓存
    setInterval(() => {
      this.cleanupExpiredMemoryCache();
    }, 5 * 60 * 1000);
  }
}

// 导出单例实例
export const cacheManager = CacheManager.getInstance();

// 启动清理定时器
cacheManager.startCleanupInterval();

// 便捷函数
export async function setCache<T>(key: string, data: T, options?: CacheOptions): Promise<void> {
  return await cacheManager.set(key, data, options);
}

export async function getCache<T>(key: string): Promise<T | null> {
  return await cacheManager.get<T>(key);
}

export async function deleteCache(key: string): Promise<void> {
  return await cacheManager.delete(key);
}

export async function deleteCacheByTag(tag: string): Promise<void> {
  return await cacheManager.deleteByTag(tag);
}

export async function deleteCacheByPrefix(prefix: string): Promise<void> {
  return await cacheManager.deleteByPrefix(prefix);
}

export async function clearAllCache(): Promise<void> {
  return await cacheManager.clear();
}
