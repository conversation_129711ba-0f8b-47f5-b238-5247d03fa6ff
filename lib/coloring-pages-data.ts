import { ColoringPage } from "@/types/coloring-category";

// 示例涂色页数据，用作静态数据备份
export const sampleColoringPages: ColoringPage[] = [
  // 动物分类
  {
    id: "animal-1",
    title: "Cute Lion Coloring Page",
    description: "A friendly cartoon lion perfect for kids to color. Features simple lines and fun details.",
    seo_slug: "cute-lion-coloring-page",
    image_url: "https://picsum.photos/400/400?random=animal-1",
    thumbnail_url: "https://picsum.photos/200/200?random=animal-1",
    alt_text: "Cute lion coloring page for kids",
    category_id: "animal",
    tags: ["lion", "animal", "kids", "easy", "cartoon"],
    difficulty_level: "easy",
    age_group: "preschool",
    is_featured: true,
    is_premium: false,
    download_count: 150,
    view_count: 500,
    status: "published",
    source: "static",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    meta_title: "Cute Lion Coloring Page - Free Printable Animal Coloring Sheet",
    meta_description: "Download this adorable lion coloring page for kids. Perfect for preschoolers learning about animals and developing creativity."
  },
  {
    id: "animal-2",
    title: "Realistic Elephant Coloring Page",
    description: "Detailed elephant coloring page with intricate patterns, perfect for older kids and adults.",
    seo_slug: "realistic-elephant-coloring-page",
    image_url: "https://picsum.photos/400/400?random=animal-2",
    thumbnail_url: "https://picsum.photos/200/200?random=animal-2",
    alt_text: "Realistic elephant coloring page",
    category_id: "animal",
    tags: ["elephant", "animal", "realistic", "detailed", "adults"],
    difficulty_level: "hard",
    age_group: "adult",
    is_featured: true,
    is_premium: false,
    download_count: 200,
    view_count: 750,
    status: "published",
    source: "static",
    created_at: "2024-01-02T00:00:00Z",
    updated_at: "2024-01-02T00:00:00Z",
    meta_title: "Realistic Elephant Coloring Page - Detailed Animal Coloring Sheet",
    meta_description: "Download this detailed elephant coloring page perfect for adults and advanced colorists. Features realistic details and intricate patterns."
  },
  {
    id: "animal-3",
    title: "Playful Monkey Coloring Page",
    description: "Fun monkey swinging from a tree branch, great for kids who love playful animals.",
    seo_slug: "playful-monkey-coloring-page",
    image_url: "https://picsum.photos/400/400?random=animal-3",
    thumbnail_url: "https://picsum.photos/200/200?random=animal-3",
    alt_text: "Playful monkey coloring page",
    category_id: "animal",
    tags: ["monkey", "animal", "playful", "tree", "kids"],
    difficulty_level: "medium",
    age_group: "school",
    is_featured: false,
    is_premium: false,
    download_count: 120,
    view_count: 400,
    status: "published",
    source: "static",
    created_at: "2024-01-03T00:00:00Z",
    updated_at: "2024-01-03T00:00:00Z",
    meta_title: "Playful Monkey Coloring Page - Fun Animal Coloring Sheet",
    meta_description: "Download this fun monkey coloring page featuring a playful primate in a tree. Perfect for kids learning about jungle animals."
  },
  // 卡通分类
  {
    id: "cartoon-1",
    title: "Happy Robot Coloring Page",
    description: "Friendly robot character with simple geometric shapes, perfect for young children.",
    seo_slug: "happy-robot-coloring-page",
    image_url: "https://picsum.photos/400/400?random=cartoon-1",
    thumbnail_url: "https://picsum.photos/200/200?random=cartoon-1",
    alt_text: "Happy robot cartoon coloring page",
    category_id: "cartoon",
    tags: ["robot", "cartoon", "kids", "geometric", "technology"],
    difficulty_level: "easy",
    age_group: "preschool",
    is_featured: true,
    is_premium: false,
    download_count: 180,
    view_count: 600,
    status: "published",
    source: "static",
    created_at: "2024-01-04T00:00:00Z",
    updated_at: "2024-01-04T00:00:00Z",
    meta_title: "Happy Robot Coloring Page - Fun Cartoon Coloring Sheet",
    meta_description: "Download this cheerful robot coloring page perfect for kids interested in technology and robots. Simple and fun design."
  },
  {
    id: "cartoon-2",
    title: "Funny Clown Coloring Page",
    description: "Cheerful clown with big smile and colorful outfit, great for circus themes.",
    seo_slug: "funny-clown-coloring-page",
    image_url: "https://picsum.photos/400/400?random=cartoon-2",
    thumbnail_url: "https://picsum.photos/200/200?random=cartoon-2",
    alt_text: "Funny clown coloring page",
    category_id: "cartoon",
    tags: ["clown", "circus", "funny", "colorful"],
    difficulty_level: "medium",
    age_group: "school",
    is_featured: false,
    is_premium: false,
    download_count: 95,
    view_count: 320,
    status: "published",
    source: "static",
    created_at: "2024-01-05T00:00:00Z",
    updated_at: "2024-01-05T00:00:00Z",
    meta_title: "Funny Clown Coloring Page - Circus Cartoon Sheet",
    meta_description: "Download this cheerful clown coloring page perfect for kids who love circus themes and funny characters."
  },
  // 公主分类
  {
    id: "princess-1",
    title: "Beautiful Princess Castle Coloring Page",
    description: "Elegant princess with a magical castle background.",
    seo_slug: "beautiful-princess-castle-coloring-page",
    image_url: "https://picsum.photos/400/400?random=princess-1",
    thumbnail_url: "https://picsum.photos/200/200?random=princess-1",
    alt_text: "Beautiful princess castle coloring page",
    category_id: "prince-princess",
    tags: ["princess", "castle", "fairy tale", "kids", "magic"],
    difficulty_level: "medium",
    age_group: "school",
    is_featured: true,
    is_premium: false,
    download_count: 250,
    view_count: 800,
    status: "published",
    source: "static",
    created_at: "2024-01-06T00:00:00Z",
    updated_at: "2024-01-06T00:00:00Z",
    meta_title: "Beautiful Princess Castle Coloring Page - Fairy Tale Sheet",
    meta_description: "Download this magical princess coloring page with castle background. Perfect for kids who love fairy tales and princesses."
  }
];

// 根据分类ID获取示例数据
export function getSamplePagesByCategory(categoryId: string): ColoringPage[] {
  return sampleColoringPages.filter(page => page.category_id === categoryId);
}

// 根据slug获取示例页面
export function getSamplePageBySlug(slug: string): ColoringPage | undefined {
  return sampleColoringPages.find(page => page.seo_slug === slug);
}

// 获取特色页面
export function getFeaturedSamplePages(limit: number = 9): ColoringPage[] {
  return sampleColoringPages.filter(page => page.is_featured).slice(0, limit);
}
