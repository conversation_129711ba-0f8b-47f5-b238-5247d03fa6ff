/**
 * PayPal API统一客户端
 * 提供所有PayPal API调用的统一接口
 */

import crypto from 'crypto';
import { getPayPalConfig, PAYPAL_ENDPOINTS, PayPalOrderStatus, PayPalSubscriptionStatus, PayPalConfig } from './paypal-config';
import { paypalLogger, PayPalOperation } from './paypal-logger';

export interface PayPalAccessTokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
}

export interface PayPalOrderResponse {
  id: string;
  status: PayPalOrderStatus;
  links: Array<{
    href: string;
    rel: string;
    method: string;
  }>;
}

export interface PayPalSubscriptionResponse {
  id: string;
  status: PayPalSubscriptionStatus;
  plan_id: string;
  billing_info?: {
    next_billing_time: string;
  };
  links: Array<{
    href: string;
    rel: string;
    method: string;
  }>;
}

/**
 * PayPal API客户端类
 */
export class PayPalClient {
  private config: PayPalConfig | null = null;
  private accessToken: string | null = null;
  private tokenExpiry: number = 0;

  /**
   * 获取PayPal配置（延迟初始化）
   */
  private getConfig(): PayPalConfig {
    if (!this.config) {
      this.config = getPayPalConfig();
    }
    return this.config;
  }

  /**
   * 获取访问令牌（带缓存）
   */
  async getAccessToken(): Promise<string> {
    // 检查缓存的token是否还有效（提前5分钟刷新）
    if (this.accessToken && Date.now() < this.tokenExpiry - 300000) {
      return this.accessToken;
    }

    const context = paypalLogger.createContext({ requestId: crypto.randomUUID() });
    
    try {
      paypalLogger.info(PayPalOperation.TOKEN_REQUEST, 'PayPal访问令牌请求开始', undefined, context);

      const config = this.getConfig();
      const auth = Buffer.from(`${config.clientId}:${config.clientSecret}`).toString('base64');

      const response = await fetch(`${config.baseUrl}${PAYPAL_ENDPOINTS.TOKEN}`, {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${auth}`,
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json',
          'Accept-Language': 'en_US',
        },
        body: 'grant_type=client_credentials',
        // 添加超时设置
        signal: AbortSignal.timeout(30000) // 30秒超时
      });

      if (!response.ok) {
        const errorText = await response.text();
        let errorDetails;
        try {
          errorDetails = JSON.parse(errorText);
        } catch {
          errorDetails = { error: errorText };
        }

        paypalLogger.error(PayPalOperation.TOKEN_REQUEST, 'PayPal令牌请求失败', new Error(`HTTP ${response.status}`), {
          status: response.status,
          statusText: response.statusText,
          errorDetails,
          url: `${config.baseUrl}${PAYPAL_ENDPOINTS.TOKEN}`
        }, context);

        throw new Error(`PayPal token request failed: ${response.status} ${response.statusText} - ${JSON.stringify(errorDetails)}`);
      }

      const data: PayPalAccessTokenResponse = await response.json();
      
      this.accessToken = data.access_token;
      this.tokenExpiry = Date.now() + (data.expires_in * 1000);

      paypalLogger.info(PayPalOperation.TOKEN_REQUEST, 'PayPal访问令牌获取成功', {
        expiresIn: data.expires_in
      }, context);

      return this.accessToken;

    } catch (error) {
      // 清除缓存的token
      this.accessToken = null;
      this.tokenExpiry = 0;

      const errorMessage = error instanceof Error ? error.message : String(error);

      // 检查是否是网络连接问题
      if (errorMessage.includes('fetch failed') || errorMessage.includes('ENOTFOUND') || errorMessage.includes('ECONNREFUSED')) {
        paypalLogger.error(PayPalOperation.TOKEN_REQUEST, 'PayPal网络连接失败', error, {
          suggestion: '请检查网络连接和PayPal服务状态',
          baseUrl: this.config?.baseUrl
        }, context);
        throw new Error('PayPal网络连接失败，请检查网络连接');
      }

      // 检查是否是认证问题
      if (errorMessage.includes('401') || errorMessage.includes('Unauthorized')) {
        paypalLogger.error(PayPalOperation.TOKEN_REQUEST, 'PayPal认证失败', error, {
          suggestion: '请检查PayPal客户端ID和密钥配置',
          clientIdPrefix: this.config?.clientId.substring(0, 10) + '...'
        }, context);
        throw new Error('PayPal认证失败，请检查客户端ID和密钥配置');
      }

      paypalLogger.error(PayPalOperation.TOKEN_REQUEST, 'PayPal访问令牌获取失败', error, {
        errorType: error instanceof Error ? error.constructor.name : 'Unknown'
      }, context);

      throw error;
    }
  }

  /**
   * 创建PayPal订单
   */
  async createOrder(orderData: any): Promise<PayPalOrderResponse> {
    const accessToken = await this.getAccessToken();
    const context = paypalLogger.createContext({ requestId: crypto.randomUUID() });

    try {
      paypalLogger.info(PayPalOperation.ORDER_CREATE, 'PayPal订单创建开始', orderData, context);

      const config = this.getConfig();
      const response = await fetch(`${config.baseUrl}${PAYPAL_ENDPOINTS.ORDERS}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData)
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`PayPal order creation failed: ${response.status} ${errorData}`);
      }

      const result: PayPalOrderResponse = await response.json();
      
      paypalLogger.info(PayPalOperation.ORDER_CREATE, 'PayPal订单创建成功', {
        orderId: result.id,
        status: result.status
      }, context);

      return result;

    } catch (error) {
      paypalLogger.error(PayPalOperation.ORDER_CREATE, 'PayPal订单创建失败', error, orderData, context);
      throw error;
    }
  }

  /**
   * 获取订单详情
   */
  async getOrderDetails(orderId: string): Promise<any> {
    const accessToken = await this.getAccessToken();
    const context = paypalLogger.createContext({ orderId, requestId: crypto.randomUUID() });

    try {
      paypalLogger.info(PayPalOperation.ORDER_DETAILS, 'PayPal订单详情查询开始', { orderId }, context);

      const config = this.getConfig();
      const response = await fetch(`${config.baseUrl}${PAYPAL_ENDPOINTS.ORDER_DETAILS(orderId)}`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`PayPal order details failed: ${response.status} ${errorData}`);
      }

      const result = await response.json();
      
      paypalLogger.info(PayPalOperation.ORDER_DETAILS, 'PayPal订单详情查询成功', {
        orderId,
        status: result.status
      }, context);

      return result;

    } catch (error) {
      paypalLogger.error(PayPalOperation.ORDER_DETAILS, 'PayPal订单详情查询失败', error, { orderId }, context);
      throw error;
    }
  }

  /**
   * 捕获订单支付
   */
  async captureOrder(orderId: string): Promise<any> {
    const accessToken = await this.getAccessToken();
    const context = paypalLogger.createContext({ orderId, requestId: crypto.randomUUID() });

    try {
      paypalLogger.info(PayPalOperation.ORDER_CAPTURE, 'PayPal订单捕获开始', { orderId }, context);

      const config = this.getConfig();
      const response = await fetch(`${config.baseUrl}${PAYPAL_ENDPOINTS.ORDER_CAPTURE(orderId)}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`PayPal order capture failed: ${response.status} ${errorData}`);
      }

      const result = await response.json();
      
      paypalLogger.info(PayPalOperation.ORDER_CAPTURE, 'PayPal订单捕获成功', {
        orderId,
        captureId: result.purchase_units?.[0]?.payments?.captures?.[0]?.id
      }, context);

      return result;

    } catch (error) {
      paypalLogger.error(PayPalOperation.ORDER_CAPTURE, 'PayPal订单捕获失败', error, { orderId }, context);
      throw error;
    }
  }

  /**
   * 创建订阅
   */
  async createSubscription(subscriptionData: any): Promise<PayPalSubscriptionResponse> {
    const accessToken = await this.getAccessToken();
    const context = paypalLogger.createContext({ requestId: crypto.randomUUID() });

    try {
      paypalLogger.info(PayPalOperation.SUBSCRIPTION_CREATE, 'PayPal订阅创建开始', subscriptionData, context);

      const config = this.getConfig();
      const response = await fetch(`${config.baseUrl}${PAYPAL_ENDPOINTS.SUBSCRIPTIONS}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(subscriptionData)
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`PayPal subscription creation failed: ${response.status} ${errorData}`);
      }

      const result: PayPalSubscriptionResponse = await response.json();
      
      paypalLogger.info(PayPalOperation.SUBSCRIPTION_CREATE, 'PayPal订阅创建成功', {
        subscriptionId: result.id,
        status: result.status,
        planId: result.plan_id
      }, context);

      return result;

    } catch (error) {
      paypalLogger.error(PayPalOperation.SUBSCRIPTION_CREATE, 'PayPal订阅创建失败', error, subscriptionData, context);
      throw error;
    }
  }

  /**
   * 获取订阅详情
   */
  async getSubscriptionDetails(subscriptionId: string): Promise<any> {
    const accessToken = await this.getAccessToken();
    const context = paypalLogger.createContext({ requestId: crypto.randomUUID() });

    try {
      paypalLogger.info(PayPalOperation.SUBSCRIPTION_DETAILS, 'PayPal订阅详情查询开始', { subscriptionId }, context);

      const config = this.getConfig();
      const response = await fetch(`${config.baseUrl}${PAYPAL_ENDPOINTS.SUBSCRIPTION_DETAILS(subscriptionId)}`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`PayPal subscription details failed: ${response.status} ${errorData}`);
      }

      const result = await response.json();
      
      paypalLogger.info(PayPalOperation.SUBSCRIPTION_DETAILS, 'PayPal订阅详情查询成功', {
        subscriptionId,
        status: result.status
      }, context);

      return result;

    } catch (error) {
      paypalLogger.error(PayPalOperation.SUBSCRIPTION_DETAILS, 'PayPal订阅详情查询失败', error, { subscriptionId }, context);
      throw error;
    }
  }
}

// 导出单例实例
export const paypalClient = new PayPalClient();
