/**
 * 数据库连接管理
 * 支持连接池、重试机制和健康检查
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';

interface DatabaseConfig {
  url: string;
  key: string;
  maxRetries: number;
  retryDelay: number;
  connectionTimeout: number;
}

class DatabaseConnection {
  private static instance: DatabaseConnection;
  private client: SupabaseClient | null = null;
  private config: DatabaseConfig;
  private isConnected: boolean = false;
  private connectionAttempts: number = 0;

  private constructor() {
    this.config = {
      url: process.env.SUPABASE_URL || '',
      key: process.env.SUPABASE_ANON_KEY || '',
      maxRetries: parseInt(process.env.DB_MAX_RETRIES || '3'),
      retryDelay: parseInt(process.env.DB_RETRY_DELAY || '1000'),
      connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || '10000'),
    };

    if (!this.config.url || !this.config.key) {
      throw new Error('数据库配置不完整：请设置 SUPABASE_URL 和 SUPABASE_ANON_KEY 环境变量');
    }
  }

  public static getInstance(): DatabaseConnection {
    if (!DatabaseConnection.instance) {
      DatabaseConnection.instance = new DatabaseConnection();
    }
    return DatabaseConnection.instance;
  }

  /**
   * 获取数据库客户端
   */
  public async getClient(): Promise<SupabaseClient> {
    if (this.client && this.isConnected) {
      return this.client;
    }

    return await this.connect();
  }

  /**
   * 建立数据库连接
   */
  private async connect(): Promise<SupabaseClient> {
    this.connectionAttempts++;

    try {
      console.log(`🔌 尝试连接数据库 (第 ${this.connectionAttempts} 次)...`);

      this.client = createClient(this.config.url, this.config.key, {
        auth: {
          persistSession: false,
        },
        db: {
          schema: 'public',
        },
        global: {
          headers: {
            'x-client-info': 'makecoloring-app',
          },
        },
      });

      // 测试连接
      await this.healthCheck();
      
      this.isConnected = true;
      this.connectionAttempts = 0;
      console.log('✅ 数据库连接成功');

      return this.client;
    } catch (error) {
      console.error(`❌ 数据库连接失败 (第 ${this.connectionAttempts} 次):`, error);

      if (this.connectionAttempts < this.config.maxRetries) {
        console.log(`⏳ ${this.config.retryDelay}ms 后重试...`);
        await this.delay(this.config.retryDelay);
        return await this.connect();
      } else {
        this.connectionAttempts = 0;
        throw new Error(`数据库连接失败，已重试 ${this.config.maxRetries} 次: ${error}`);
      }
    }
  }

  /**
   * 健康检查
   */
  private async healthCheck(): Promise<void> {
    if (!this.client) {
      throw new Error('数据库客户端未初始化');
    }

    const { data, error } = await this.client
      .from('coloring_pages')
      .select('id')
      .limit(1);

    if (error) {
      throw new Error(`数据库健康检查失败: ${error.message}`);
    }
  }

  /**
   * 执行数据库操作（带重试机制）
   */
  public async executeWithRetry<T>(
    operation: (client: SupabaseClient) => Promise<T>,
    operationName: string = '数据库操作'
  ): Promise<T> {
    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
      try {
        const client = await this.getClient();
        return await operation(client);
      } catch (error) {
        lastError = error as Error;
        console.error(`${operationName}失败 (第 ${attempt} 次):`, error);

        if (attempt < this.config.maxRetries) {
          // 如果是连接错误，重置连接状态
          if (this.isConnectionError(error)) {
            this.isConnected = false;
            this.client = null;
          }

          await this.delay(this.config.retryDelay * attempt);
        }
      }
    }

    throw new Error(`${operationName}最终失败: ${lastError?.message}`);
  }

  /**
   * 判断是否为连接错误
   */
  private isConnectionError(error: any): boolean {
    const connectionErrorMessages = [
      'connection',
      'network',
      'timeout',
      'ECONNREFUSED',
      'ENOTFOUND',
      'ETIMEDOUT'
    ];

    const errorMessage = error?.message?.toLowerCase() || '';
    return connectionErrorMessages.some(msg => errorMessage.includes(msg));
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 关闭连接
   */
  public async disconnect(): Promise<void> {
    if (this.client) {
      // Supabase 客户端不需要显式关闭
      this.client = null;
      this.isConnected = false;
      console.log('🔌 数据库连接已关闭');
    }
  }

  /**
   * 获取连接状态
   */
  public getConnectionStatus(): {
    isConnected: boolean;
    connectionAttempts: number;
    config: Omit<DatabaseConfig, 'key'>;
  } {
    return {
      isConnected: this.isConnected,
      connectionAttempts: this.connectionAttempts,
      config: {
        url: this.config.url,
        maxRetries: this.config.maxRetries,
        retryDelay: this.config.retryDelay,
        connectionTimeout: this.config.connectionTimeout,
      },
    };
  }
}

// 导出单例实例
export const dbConnection = DatabaseConnection.getInstance();

// 导出便捷函数
export async function getSupabaseClient(): Promise<SupabaseClient> {
  return await dbConnection.getClient();
}

export async function executeWithRetry<T>(
  operation: (client: SupabaseClient) => Promise<T>,
  operationName?: string
): Promise<T> {
  return await dbConnection.executeWithRetry(operation, operationName);
}

// 数据库健康检查中间件
export async function checkDatabaseHealth(): Promise<{
  status: 'healthy' | 'unhealthy';
  message: string;
  timestamp: string;
}> {
  try {
    const client = await getSupabaseClient();
    const { data, error } = await client
      .from('coloring_pages')
      .select('count(*)')
      .limit(1);

    if (error) {
      throw error;
    }

    return {
      status: 'healthy',
      message: '数据库连接正常',
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      message: `数据库连接异常: ${error}`,
      timestamp: new Date().toISOString(),
    };
  }
}

// 优雅关闭处理
process.on('SIGINT', async () => {
  console.log('🛑 收到关闭信号，正在关闭数据库连接...');
  await dbConnection.disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('🛑 收到终止信号，正在关闭数据库连接...');
  await dbConnection.disconnect();
  process.exit(0);
});
