/**
 * PayPal统一配置管理
 * 集中管理所有PayPal相关的配置和常量
 */

export interface PayPalConfig {
  baseUrl: string;
  clientId: string;
  clientSecret: string;
  webhookId: string;
  environment: 'sandbox' | 'production';
  projectName: string;
  webUrl: string;
}

/**
 * 获取PayPal配置
 * 验证必需的环境变量并返回配置对象
 */
export function getPayPalConfig(): PayPalConfig {
  const environment = process.env.PAYPAL_ENVIRONMENT as 'sandbox' | 'production' || 'sandbox';
  
  const config: PayPalConfig = {
    baseUrl: environment === 'production' 
      ? 'https://api-m.paypal.com' 
      : 'https://api.sandbox.paypal.com',
    clientId: process.env.PAYPAL_CLIENT_ID || '',
    clientSecret: process.env.PAYPAL_CLIENT_SECRET || '',
    webhookId: process.env.PAYPAL_WEBHOOK_ID || '',
    environment,
    projectName: process.env.NEXT_PUBLIC_PROJECT_NAME || 'MakeColoring',
    webUrl: process.env.NEXT_PUBLIC_SITE_URL || process.env.NEXT_PUBLIC_WEB_URL || 'http://localhost:3000'
  };

  // 验证必需的配置
  const requiredFields: (keyof PayPalConfig)[] = ['clientId', 'clientSecret'];
  const missingFields = requiredFields.filter(field => !config[field]);
  
  if (missingFields.length > 0) {
    throw new Error(`PayPal配置缺失: ${missingFields.join(', ')}`);
  }

  return config;
}

/**
 * PayPal API端点常量
 */
export const PAYPAL_ENDPOINTS = {
  // OAuth
  TOKEN: '/v1/oauth2/token',
  
  // Orders
  ORDERS: '/v2/checkout/orders',
  ORDER_CAPTURE: (orderId: string) => `/v2/checkout/orders/${orderId}/capture`,
  ORDER_DETAILS: (orderId: string) => `/v2/checkout/orders/${orderId}`,
  
  // Subscriptions
  SUBSCRIPTIONS: '/v1/billing/subscriptions',
  SUBSCRIPTION_DETAILS: (subscriptionId: string) => `/v1/billing/subscriptions/${subscriptionId}`,
  SUBSCRIPTION_ACTIVATE: (subscriptionId: string) => `/v1/billing/subscriptions/${subscriptionId}/activate`,
  SUBSCRIPTION_CANCEL: (subscriptionId: string) => `/v1/billing/subscriptions/${subscriptionId}/cancel`,
  
  // Plans
  PLANS: '/v1/billing/plans',
  PLAN_DETAILS: (planId: string) => `/v1/billing/plans/${planId}`,
  
  // Products
  PRODUCTS: '/v1/catalogs/products',
  
  // Webhooks
  WEBHOOK_VERIFY: '/v1/notifications/verify-webhook-signature'
} as const;

/**
 * PayPal订阅状态枚举
 */
export enum PayPalSubscriptionStatus {
  APPROVAL_PENDING = 'APPROVAL_PENDING',
  APPROVED = 'APPROVED',
  ACTIVE = 'ACTIVE',
  SUSPENDED = 'SUSPENDED',
  CANCELLED = 'CANCELLED',
  EXPIRED = 'EXPIRED'
}

/**
 * PayPal订单状态枚举
 */
export enum PayPalOrderStatus {
  CREATED = 'CREATED',
  SAVED = 'SAVED',
  APPROVED = 'APPROVED',
  VOIDED = 'VOIDED',
  COMPLETED = 'COMPLETED',
  PAYER_ACTION_REQUIRED = 'PAYER_ACTION_REQUIRED'
}

/**
 * PayPal webhook事件类型
 */
export enum PayPalWebhookEventType {
  // 订单事件
  ORDER_APPROVED = 'CHECKOUT.ORDER.APPROVED',
  ORDER_COMPLETED = 'CHECKOUT.ORDER.COMPLETED',

  // 支付事件
  PAYMENT_CAPTURE_COMPLETED = 'PAYMENT.CAPTURE.COMPLETED',
  PAYMENT_CAPTURE_DENIED = 'PAYMENT.CAPTURE.DENIED',
  PAYMENT_CAPTURE_FAILED = 'PAYMENT.CAPTURE.FAILED',

  // 订阅生命周期事件
  SUBSCRIPTION_CREATED = 'BILLING.SUBSCRIPTION.CREATED',
  SUBSCRIPTION_ACTIVATED = 'BILLING.SUBSCRIPTION.ACTIVATED',
  SUBSCRIPTION_UPDATED = 'BILLING.SUBSCRIPTION.UPDATED',
  SUBSCRIPTION_EXPIRED = 'BILLING.SUBSCRIPTION.EXPIRED',
  SUBSCRIPTION_CANCELLED = 'BILLING.SUBSCRIPTION.CANCELLED',
  SUBSCRIPTION_SUSPENDED = 'BILLING.SUBSCRIPTION.SUSPENDED',
  SUBSCRIPTION_RE_ACTIVATED = 'BILLING.SUBSCRIPTION.RE-ACTIVATED',
  SUBSCRIPTION_RENEWED = 'BILLING.SUBSCRIPTION.RENEWED',
  SUBSCRIPTION_PAYMENT_COMPLETED = 'BILLING.SUBSCRIPTION.PAYMENT.COMPLETED',
  SUBSCRIPTION_PAYMENT_FAILED = 'BILLING.SUBSCRIPTION.PAYMENT.FAILED',

  // 交易相关事件
  PAYMENT_SALE_COMPLETED = 'PAYMENT.SALE.COMPLETED',
  PAYMENT_SALE_REFUNDED = 'PAYMENT.SALE.REFUNDED',
  PAYMENT_SALE_REVERSED = 'PAYMENT.SALE.REVERSED'
}

/**
 * PayPal应用上下文配置
 */
export const PAYPAL_APPLICATION_CONTEXT = {
  shipping_preference: 'NO_SHIPPING',
  user_action: 'PAY_NOW',
  payment_method: {
    payer_selected: 'PAYPAL',
    payee_preferred: 'IMMEDIATE_PAYMENT_REQUIRED'
  }
} as const;

/**
 * PayPal订阅应用上下文配置
 * 使用CONTINUE而不是SUBSCRIBE_NOW来避免双重密码输入
 */
export const PAYPAL_SUBSCRIPTION_CONTEXT = {
  shipping_preference: 'NO_SHIPPING',
  user_action: 'SUBSCRIBE_NOW',
  payment_method: {
    payer_selected: 'PAYPAL',
    payee_preferred: 'IMMEDIATE_PAYMENT_REQUIRED'
  }
} as const;

/**
 * 构建PayPal成功/取消URL
 * 根据PayPal官方文档，return_url应该指向最终用户目标页面
 * 支付验证通过即时处理+webhook双重保障机制完成
 */
export function buildPayPalUrls(returnUrl?: string, cancelUrl?: string) {
  // 优先使用传入的URL，否则使用环境变量配置的URL
  // 默认成功页面直接指向订单页面，避免不必要的中间跳转
  const baseSuccessUrl = returnUrl || process.env.NEXT_PUBLIC_PAY_SUCCESS_URL || `${getPayPalConfig().webUrl}/my-orders`;
  const baseCancelUrl = cancelUrl || process.env.NEXT_PUBLIC_PAY_CANCEL_URL || `${getPayPalConfig().webUrl}/pricing`;

  // 为成功URL添加PayPal相关参数，用于即时处理支付结果
  // 根据PayPal官方文档，token和PayerID会自动添加到return_url
  const successUrl = baseSuccessUrl.includes('?')
    ? `${baseSuccessUrl}&paypal_return=true`
    : `${baseSuccessUrl}?paypal_return=true`;

  // 为取消URL添加取消参数
  const cancelUrlFinal = baseCancelUrl.includes('?')
    ? `${baseCancelUrl}&cancelled=payment`
    : `${baseCancelUrl}?cancelled=payment`;

  return {
    successUrl,
    cancelUrl: cancelUrlFinal
  };
}

/**
 * 验证PayPal环境配置
 */
export function validatePayPalEnvironment(): void {
  getPayPalConfig();
}
