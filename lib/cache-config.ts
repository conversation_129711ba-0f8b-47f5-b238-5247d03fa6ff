// 缓存配置
export const CACHE_CONFIG = {
  // 静态资源缓存时间（秒）
  STATIC_ASSETS: 31536000, // 1年
  IMAGES: 2592000, // 30天
  API_RESPONSES: 300, // 5分钟
  PAGE_CACHE: 3600, // 1小时
  
  // 缓存标签
  TAGS: {
    COLORING_PAGES: 'coloring-pages',
    CATEGORIES: 'categories',
    USER_DATA: 'user-data',
    STATIC_CONTENT: 'static-content',
  },
  
  // 缓存键前缀
  KEYS: {
    COLORING_PAGE: 'coloring-page:',
    CATEGORY: 'category:',
    USER: 'user:',
    API: 'api:',
  }
};

// 生成缓存键
export function generateCacheKey(prefix: string, ...parts: (string | number)[]): string {
  return `${prefix}${parts.join(':')}`;
}

// 缓存响应头
export function getCacheHeaders(maxAge: number, tags?: string[]) {
  const headers: Record<string, string> = {
    'Cache-Control': `public, max-age=${maxAge}, s-maxage=${maxAge}`,
  };
  
  if (tags && tags.length > 0) {
    headers['Cache-Tag'] = tags.join(',');
  }
  
  return headers;
}

// 设置静态资源缓存头
export function getStaticAssetHeaders() {
  return getCacheHeaders(CACHE_CONFIG.STATIC_ASSETS, [CACHE_CONFIG.TAGS.STATIC_CONTENT]);
}

// 设置图片缓存头
export function getImageCacheHeaders() {
  return getCacheHeaders(CACHE_CONFIG.IMAGES, [CACHE_CONFIG.TAGS.STATIC_CONTENT]);
}

// 设置API缓存头
export function getApiCacheHeaders(tags?: string[]) {
  return getCacheHeaders(CACHE_CONFIG.API_RESPONSES, tags);
}

// 设置页面缓存头
export function getPageCacheHeaders(tags?: string[]) {
  return getCacheHeaders(CACHE_CONFIG.PAGE_CACHE, tags);
}
