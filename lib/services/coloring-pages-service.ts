import { ColoringPage } from "@/types/coloring-category";
import { executeWithRetry } from "@/lib/database/connection";
import {
  getCache,
  setCache,
  deleteCacheByTag,
  deleteCacheByPrefix,
  clearAllCache
} from "@/lib/cache/simple-cache";


// 缓存配置
const CACHE_TTL = 5 * 60; // 5分钟缓存（秒）

export interface ColoringPageFilters {
  categoryId?: string;
  subcategoryId?: string;
  difficulty?: 'easy' | 'medium' | 'hard';
  ageGroup?: 'toddler' | 'preschool' | 'school' | 'teen' | 'adult' | 'all';
  isFeatured?: boolean;
  isPremium?: boolean;
  status?: 'draft' | 'published' | 'archived';
  source?: 'static' | 'user_generated' | 'ai_generated';
}

export interface PaginationOptions {
  limit: number;
  offset: number;
  orderBy?: string;
  orderDirection?: 'ASC' | 'DESC';
}

export interface ColoringPagesResult {
  pages: ColoringPage[];
  total: number;
}

// 缓存工具函数
function getCacheKey(prefix: string, params: any): string {
  return `coloring_pages:${prefix}:${JSON.stringify(params)}`;
}

export class ColoringPagesService {
  /**
   * 数据库行转换为ColoringPage对象
   */
  private static transformDbRowToColoringPage(row: any): ColoringPage {
    return {
      id: row.id,
      title: row.title,
      description: row.description,
      seo_slug: row.seo_slug,
      image_url: row.image_url,
      thumbnail_url: row.thumbnail_url,
      alt_text: row.alt_text,
      category_id: row.category_id,
      subcategory_id: row.subcategory_id || null,
      tags: Array.isArray(row.tags) ? row.tags : (row.tags ? JSON.parse(row.tags) : []),
      difficulty_level: row.difficulty_level as 'easy' | 'medium' | 'hard',
      age_group: row.age_group as any,
      is_featured: row.is_featured,
      is_premium: false, // 简化版本暂时不支持
      download_count: row.download_count || 0,
      view_count: row.view_count || 0,
      status: row.status as 'draft' | 'published' | 'archived',
      source: 'static' as const, // 简化版本固定为static
      created_by: undefined,
      print_size: 'A4',
      coloring_tips: [], // 简化版本暂时为空
      color_suggestions: [], // 简化版本暂时为空
      educational_value: undefined,
      usage_scenarios: [], // 简化版本暂时为空
      meta_title: row.meta_title,
      meta_description: row.meta_description,
      created_at: row.created_at,
      updated_at: row.updated_at,
    };
  }

  /**
   * 获取所有分类
   */
  static async getCategories(): Promise<any[]> {
    const cacheKey = getCacheKey('categories', {});
    const cached = await getCache<any[]>(cacheKey);
    if (cached) return cached;

    return await executeWithRetry(async (supabase) => {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .eq('is_active', true)
        .order('sort_order', { ascending: true });

      if (error) {
        throw new Error(`获取分类失败: ${error.message}`);
      }

      await setCache(cacheKey, data || [], {
        ttl: CACHE_TTL * 2, // 分类变化较少，缓存时间更长
        tags: ['categories']
      });

      return data || [];
    }, '获取分类列表');
  }

  /**
   * 根据SEO slug获取分类
   */
  static async getCategoryBySeoSlug(seoSlug: string): Promise<any | null> {
    const cacheKey = getCacheKey('category_by_seo_slug', { seoSlug });
    const cached = await getCache<any | null>(cacheKey);
    if (cached !== undefined) return cached;

    return await executeWithRetry(async (supabase) => {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .eq('seo_slug', seoSlug)
        .eq('is_active', true)
        .single();

      if (error || !data) {
        await setCache(cacheKey, null, { ttl: CACHE_TTL });
        return null;
      }

      await setCache(cacheKey, data, {
        ttl: CACHE_TTL * 2,
        tags: ['categories', `category_${data.id}`]
      });

      return data;
    }, '根据SEO slug获取分类');
  }

  /**
   * 获取涂色页面列表（支持筛选和分页）
   */
  static async getColoringPages(
    filters: ColoringPageFilters = {},
    pagination: PaginationOptions = { limit: 20, offset: 0 }
  ): Promise<ColoringPagesResult> {
    const cacheKey = getCacheKey('pages', { filters, pagination });
    const cached = await getCache<ColoringPagesResult>(cacheKey);
    if (cached) return cached;

    return await executeWithRetry(async (supabase) => {
      let query = supabase
        .from('coloring_pages')
        .select('*', { count: 'exact' });

      // 应用筛选条件
      if (filters.categoryId) {
        query = query.eq('category_id', filters.categoryId);
      }
      if (filters.subcategoryId) {
        query = query.eq('subcategory_id', filters.subcategoryId);
      }
      if (filters.difficulty) {
        query = query.eq('difficulty_level', filters.difficulty);
      }
      if (filters.ageGroup) {
        query = query.eq('age_group', filters.ageGroup);
      }
      if (filters.isFeatured !== undefined) {
        query = query.eq('is_featured', filters.isFeatured);
      }
      if (filters.isPremium !== undefined) {
        query = query.eq('is_premium', filters.isPremium);
      }
      if (filters.status) {
        query = query.eq('status', filters.status);
      } else {
        // 默认只返回已发布的页面
        query = query.eq('status', 'published');
      }
      if (filters.source) {
        query = query.eq('source', filters.source);
      }

      // 应用排序和分页
      const orderBy = pagination.orderBy || 'created_at';
      const orderDirection = pagination.orderDirection || 'DESC';
      query = query
        .order(orderBy, { ascending: orderDirection === 'ASC' })
        .range(pagination.offset, pagination.offset + pagination.limit - 1);

      const { data, error, count } = await query;

      if (error) {
        throw new Error(`数据库查询失败: ${error.message}`);
      }

      const result: ColoringPagesResult = {
        pages: (data || []).map((row: any) => this.transformDbRowToColoringPage(row)),
        total: count || 0
      };

      await setCache(cacheKey, result, { 
        ttl: CACHE_TTL, 
        tags: ['coloring_pages', 'pages_list', `category_${filters.categoryId || 'all'}`] 
      });
      return result;
    }, '获取涂色页面列表');
  }

  /**
   * 根据SEO slug获取单个涂色页面
   */
  static async getColoringPageBySlug(slug: string): Promise<ColoringPage | null> {
    const cacheKey = getCacheKey('page_by_slug', { slug });
    const cached = await getCache<ColoringPage | null>(cacheKey);
    if (cached !== undefined) return cached;

    try {
      return await executeWithRetry(async (supabase) => {
        const { data, error } = await supabase
          .from('coloring_pages')
          .select('*')
          .eq('seo_slug', slug)
          .eq('status', 'published')
          .single();

        if (error || !data) {
          await setCache(cacheKey, null, { ttl: CACHE_TTL });
          return null;
        }

        // 增加浏览次数
        await this.incrementViewCount(data.id);

        const page = this.transformDbRowToColoringPage(data);
        await setCache(cacheKey, page, { 
          ttl: CACHE_TTL, 
          tags: ['coloring_pages', 'single_page', `category_${page.category_id}`] 
        });
        return page;
      }, '根据slug获取涂色页面');
    } catch (error) {
      console.error('获取涂色页面失败:', error);
      await setCache(cacheKey, null, { ttl: CACHE_TTL });
      return null;
    }
  }

  /**
   * 获取分类的页面数量
   */
  static async getCategoryPageCount(categoryId: string): Promise<number> {
    const cacheKey = getCacheKey('category_count', { categoryId });
    const cached = await getCache<number>(cacheKey);
    if (cached !== null && cached !== undefined) return cached;

    return await executeWithRetry(async (supabase) => {
      const { count, error } = await supabase
        .from('coloring_pages')
        .select('*', { count: 'exact', head: true })
        .eq('category_id', categoryId)
        .eq('status', 'published');

      if (error) {
        throw new Error(`获取分类页面数量失败: ${error.message}`);
      }

      const result = count || 0;
      await setCache(cacheKey, result, { 
        ttl: CACHE_TTL * 2, // 数量变化较少，缓存时间更长
        tags: ['coloring_pages', 'category_counts', `category_${categoryId}`] 
      });
      return result;
    }, '获取分类页面数量');
  }

  /**
   * 根据分类获取页面（支持分页）
   */
  static async getPagesByCategory(
    categoryId: string, 
    page: number = 1, 
    limit: number = 20
  ): Promise<{ pages: ColoringPage[]; total_pages: number; total_count: number }> {
    const offset = (page - 1) * limit;
    const result = await this.getColoringPages(
      { categoryId },
      { limit, offset, orderBy: 'created_at', orderDirection: 'DESC' }
    );

    return {
      pages: result.pages,
      total_pages: Math.ceil(result.total / limit),
      total_count: result.total
    };
  }

  /**
   * 获取特色页面
   */
  static async getFeaturedPages(limit: number = 9): Promise<ColoringPage[]> {
    const result = await this.getColoringPages(
      { isFeatured: true },
      { limit, offset: 0, orderBy: 'created_at', orderDirection: 'DESC' }
    );

    return result.pages;
  }

  /**
   * 获取相关页面
   */
  static async getRelatedPages(
    pageId: string, 
    categoryId: string, 
    subcategoryId?: string,
    limit: number = 4
  ): Promise<ColoringPage[]> {
    const cacheKey = getCacheKey('related_pages', { pageId, categoryId, subcategoryId, limit });
    const cached = await getCache<ColoringPage[]>(cacheKey);
    if (cached) return cached;

    try {
      return await executeWithRetry(async (supabase) => {
        let query = supabase
          .from('coloring_pages')
          .select('*')
          .eq('category_id', categoryId)
          .eq('status', 'published')
          .neq('id', pageId)
          .limit(limit);

        // 如果有子分类，优先显示同子分类的页面
        if (subcategoryId) {
          query = query.eq('subcategory_id', subcategoryId);
        }

        const { data, error } = await query.order('created_at', { ascending: false });

        if (error) {
          throw new Error(`获取相关页面失败: ${error.message}`);
        }

        const result = (data || []).map((row: any) => this.transformDbRowToColoringPage(row));
        await setCache(cacheKey, result, { 
          ttl: CACHE_TTL, 
          tags: ['coloring_pages', 'related_pages', `category_${categoryId}`] 
        });
        return result;
      }, '获取相关页面');
    } catch (error) {
      console.error('获取相关页面失败:', error);
      return [];
    }
  }

  /**
   * 增加浏览次数
   */
  private static async incrementViewCount(pageId: string): Promise<void> {
    try {
      await executeWithRetry(async (supabase) => {
        await supabase.rpc('increment_view_count', { page_id: pageId });
      }, '增加浏览次数');
    } catch (error) {
      console.error('更新浏览次数失败:', error);
      // 不抛出错误，因为这不是关键功能
    }
  }

  /**
   * 获取所有已发布的页面（用于sitemap）
   */
  static async getAllPublishedPages(): Promise<ColoringPage[]> {
    const cacheKey = getCacheKey('all_published', {});
    const cached = await getCache<ColoringPage[]>(cacheKey);
    if (cached) return cached;

    try {
      const result = await this.getColoringPages(
        { status: 'published' },
        { limit: 10000, offset: 0, orderBy: 'created_at', orderDirection: 'DESC' }
      );

      await setCache(cacheKey, result.pages, { 
        ttl: CACHE_TTL * 4, // sitemap变化较少，缓存时间更长
        tags: ['coloring_pages', 'sitemap'] 
      });
      return result.pages;
    } catch (error) {
      console.error('获取所有已发布页面失败:', error);
      return [];
    }
  }

  /**
   * 清除所有缓存
   */
  static async clearCache(): Promise<void> {
    await clearAllCache();
  }

  /**
   * 根据标签清除缓存
   */
  static async clearCacheByTag(tag: string): Promise<void> {
    await deleteCacheByTag(tag);
  }

  /**
   * 根据前缀清除缓存
   */
  static async clearCacheByPrefix(prefix: string): Promise<void> {
    await deleteCacheByPrefix(prefix);
  }
}
