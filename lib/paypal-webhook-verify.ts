import crypto from 'crypto';
const crc32 = require('buffer-crc32');
import { paypalLogger, PayPalOperation } from './paypal-logger';

interface WebhookHeaders {
  'paypal-transmission-id': string;
  'paypal-transmission-time': string;
  'paypal-transmission-sig': string;
  'paypal-cert-url': string;
  'paypal-auth-algo': string;
}

// 证书缓存
const certCache = new Map<string, { cert: string; expiry: number }>();

/**
 * 验证PayPal Webhook签名
 * 实现企业级安全验证机制
 */
export async function verifyPayPalWebhook(
  headers: WebhookHeaders,
  body: string,
  webhookId: string
): Promise<boolean> {
  const context = paypalLogger.createContext({ requestId: headers['paypal-transmission-id'] });

  try {
    paypalLogger.info(PayPalOperation.WEBHOOK_VERIFY, 'PayPal webhook严格验证开始', {
      bodyLength: body.length,
      webhookId,
      transmissionId: headers['paypal-transmission-id']
    }, context);

    // 1. 提取必需的headers
    const {
      'paypal-transmission-id': transmissionId,
      'paypal-transmission-time': transmissionTime,
      'paypal-transmission-sig': transmissionSig,
      'paypal-cert-url': certUrl
    } = headers;

    // 2. 验证必需字段
    if (!transmissionId || !transmissionTime || !transmissionSig || !certUrl) {
      return false;
    }

    if (!webhookId) {
      return false;
    }

    // 3. 时间戳验证(5分钟窗口)
    const currentTime = Math.floor(Date.now() / 1000);
    const webhookTimestamp = Math.floor(new Date(transmissionTime).getTime() / 1000);
    const timeDiff = Math.abs(currentTime - webhookTimestamp);

    if (timeDiff > 300) { // 5分钟
      return false;
    }

    // 4. 下载并缓存证书
    let cert = certCache.get(certUrl)?.cert;
    if (!cert || certCache.get(certUrl)!.expiry < Date.now()) {
      const certResponse = await fetch(certUrl);
      if (!certResponse.ok) {
        return false;
      }
      cert = await certResponse.text();
      certCache.set(certUrl, { cert, expiry: Date.now() + 3600000 }); // 1小时缓存
    }

    // 5. CRC32计算
    const crc = crc32.unsigned(Buffer.from(body, 'utf8'));

    // 6. 构建验证消息
    const message = `${transmissionId}|${transmissionTime}|${webhookId}|${crc}`;

    // 7. RSA签名验证
    const verifier = crypto.createVerify('SHA256');
    verifier.update(message, 'utf8');

    const isValid = verifier.verify(cert, transmissionSig, 'base64');

    return isValid;

  } catch (error) {
    paypalLogger.error(PayPalOperation.WEBHOOK_VERIFY, 'PayPal webhook验证异常', error, {
      bodyLength: body.length,
      webhookId
    }, context);
    return false;
  }
}

/**
 * 获取PayPal访问令牌
 */
export async function getPayPalAccessToken(): Promise<string> {
  const PAYPAL_BASE_URL = process.env.PAYPAL_ENVIRONMENT === 'production' 
    ? 'https://api-m.paypal.com' 
    : 'https://api.sandbox.paypal.com';

  const auth = Buffer.from(`${process.env.PAYPAL_CLIENT_ID}:${process.env.PAYPAL_CLIENT_SECRET}`)
    .toString('base64');
  
  const response = await fetch(`${PAYPAL_BASE_URL}/v1/oauth2/token`, {
    method: 'POST',
    headers: {
      'Authorization': `Basic ${auth}`,
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: 'grant_type=client_credentials',
  });

  if (!response.ok) {
    throw new Error(`PayPal token request failed: ${response.status}`);
  }

  const data = await response.json();
  return data.access_token;
}

/**
 * 获取PayPal订单详情
 */
export async function getPayPalOrderDetails(orderId: string): Promise<any> {
  const PAYPAL_BASE_URL = process.env.PAYPAL_ENVIRONMENT === 'production' 
    ? 'https://api-m.paypal.com' 
    : 'https://api.sandbox.paypal.com';

  const accessToken = await getPayPalAccessToken();
  
  const response = await fetch(`${PAYPAL_BASE_URL}/v2/checkout/orders/${orderId}`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`PayPal order details request failed: ${response.status}`);
  }

  return await response.json();
}

/**
 * 处理PayPal退款
 */
export async function processPayPalRefund(captureId: string, amount: string): Promise<any> {
  const PAYPAL_BASE_URL = process.env.PAYPAL_ENVIRONMENT === 'production'
    ? 'https://api-m.paypal.com'
    : 'https://api.sandbox.paypal.com';

  const accessToken = await getPayPalAccessToken();

  const response = await fetch(`${PAYPAL_BASE_URL}/v2/payments/captures/${captureId}/refund`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      amount: {
        value: amount,
        currency_code: 'USD'
      }
    }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(`PayPal refund failed: ${JSON.stringify(errorData)}`);
  }

  return await response.json();
}

/**
 * 从订单详情中提取Capture ID
 */
export function extractCaptureId(orderDetails: any): string | null {
  try {
    // 从webhook事件中提取
    if (orderDetails.id) {
      return orderDetails.id;
    }

    // 从PayPal订单详情中提取
    if (orderDetails.purchase_units?.[0]?.payments?.captures?.[0]?.id) {
      return orderDetails.purchase_units[0].payments.captures[0].id;
    }

    // 从其他可能的位置提取
    if (orderDetails.capture_id) {
      return orderDetails.capture_id;
    }

    return null;
  } catch (error) {
    console.error('提取Capture ID失败:', error);
    return null;
  }
}


