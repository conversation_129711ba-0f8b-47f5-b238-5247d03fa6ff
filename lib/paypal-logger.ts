/**
 * PayPal集成专用日志工具
 * 提供结构化的日志记录和错误处理
 */

export enum PayPalLogLevel {
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
  DEBUG = 'DEBUG'
}

export enum PayPalOperation {
  ORDER_CREATE = 'ORDER_CREATE',
  ORDER_CAPTURE = 'ORDER_CAPTURE',
  ORDER_DETAILS = 'ORDER_DETAILS',
  SUBSCRIPTION_CREATE = 'SUBSCRIPTION_CREATE',
  SUBSCRIPTION_DETAILS = 'SUBSCRIPTION_DETAILS',
  WEBHOOK_VERIFY = 'WEBHOOK_VERIFY',
  WEBHOOK_PROCESS = 'WEBHOOK_PROCESS',
  REFUND_PROCESS = 'REFUND_PROCESS',
  TOKEN_REQUEST = 'TOKEN_REQUEST'
}

interface PayPalLogEntry {
  timestamp: string;
  level: PayPalLogLevel;
  operation: PayPalOperation;
  message: string;
  data?: any;
  error?: any;
  orderId?: string;
  orderNo?: string;
  userId?: string;
  requestId?: string;
}

class PayPalLogger {
  private isDevelopment = process.env.NODE_ENV === 'development';
  private enableDebug = process.env.PAYPAL_DEBUG === 'true';

  /**
   * 记录信息日志
   */
  info(operation: PayPalOperation, message: string, data?: any, context?: {
    orderId?: string;
    orderNo?: string;
    userId?: string;
    requestId?: string;
  }) {
    this.log(PayPalLogLevel.INFO, operation, message, data, undefined, context);
  }

  /**
   * 记录警告日志
   */
  warn(operation: PayPalOperation, message: string, data?: any, context?: {
    orderId?: string;
    orderNo?: string;
    userId?: string;
    requestId?: string;
  }) {
    this.log(PayPalLogLevel.WARN, operation, message, data, undefined, context);
  }

  /**
   * 记录错误日志
   */
  error(operation: PayPalOperation, message: string, error?: any, data?: any, context?: {
    orderId?: string;
    orderNo?: string;
    userId?: string;
    requestId?: string;
  }) {
    this.log(PayPalLogLevel.ERROR, operation, message, data, error, context);
  }

  /**
   * 记录调试日志
   */
  debug(operation: PayPalOperation, message: string, data?: any, context?: {
    orderId?: string;
    orderNo?: string;
    userId?: string;
    requestId?: string;
  }) {
    if (this.enableDebug || this.isDevelopment) {
      this.log(PayPalLogLevel.DEBUG, operation, message, data, undefined, context);
    }
  }

  /**
   * 核心日志记录方法
   */
  private log(
    level: PayPalLogLevel,
    operation: PayPalOperation,
    message: string,
    data?: any,
    error?: any,
    context?: {
      orderId?: string;
      orderNo?: string;
      userId?: string;
      requestId?: string;
    }
  ) {
    const logEntry: PayPalLogEntry = {
      timestamp: new Date().toISOString(),
      level,
      operation,
      message,
      ...context
    };

    // 添加数据（过滤敏感信息）
    if (data) {
      logEntry.data = this.sanitizeData(data);
    }

    // 添加错误信息
    if (error) {
      logEntry.error = {
        message: error.message || error,
        stack: error.stack,
        name: error.name,
        code: error.code
      };
    }

    // 格式化输出
    const prefix = this.getLogPrefix(level, operation);
    const contextStr = this.formatContext(context);
    
    if (level === PayPalLogLevel.ERROR) {
      console.error(`${prefix}${contextStr} ${message}`, logEntry);
    } else if (level === PayPalLogLevel.WARN) {
      console.warn(`${prefix}${contextStr} ${message}`, logEntry);
    } else {
      console.log(`${prefix}${contextStr} ${message}`, logEntry);
    }

    // 在生产环境中，可以将日志发送到外部服务
    if (!this.isDevelopment) {
      this.sendToExternalLogger(logEntry);
    }
  }

  /**
   * 获取日志前缀
   */
  private getLogPrefix(level: PayPalLogLevel, operation: PayPalOperation): string {
    const emoji = {
      [PayPalLogLevel.INFO]: '✅',
      [PayPalLogLevel.WARN]: '⚠️',
      [PayPalLogLevel.ERROR]: '❌',
      [PayPalLogLevel.DEBUG]: '🔍'
    };

    return `${emoji[level]} [PayPal:${operation}]`;
  }

  /**
   * 格式化上下文信息
   */
  private formatContext(context?: {
    orderId?: string;
    orderNo?: string;
    userId?: string;
    requestId?: string;
  }): string {
    if (!context) return '';

    const parts = [];
    if (context.orderNo) parts.push(`Order:${context.orderNo}`);
    if (context.orderId) parts.push(`PayPal:${context.orderId}`);
    if (context.userId) parts.push(`User:${context.userId}`);
    if (context.requestId) parts.push(`Req:${context.requestId}`);

    return parts.length > 0 ? ` [${parts.join('|')}]` : '';
  }

  /**
   * 清理敏感数据
   */
  private sanitizeData(data: any): any {
    if (!data) return data;

    const sensitiveKeys = [
      'access_token',
      'client_secret',
      'authorization',
      'password',
      'secret',
      'key',
      'token'
    ];

    if (typeof data === 'string') {
      return data.length > 1000 ? `${data.substring(0, 1000)}...` : data;
    }

    if (typeof data !== 'object') {
      return data;
    }

    const sanitized = { ...data };

    for (const key of Object.keys(sanitized)) {
      const lowerKey = key.toLowerCase();
      
      if (sensitiveKeys.some(sensitive => lowerKey.includes(sensitive))) {
        sanitized[key] = '[REDACTED]';
      } else if (typeof sanitized[key] === 'object') {
        sanitized[key] = this.sanitizeData(sanitized[key]);
      } else if (typeof sanitized[key] === 'string' && sanitized[key].length > 1000) {
        sanitized[key] = `${sanitized[key].substring(0, 1000)}...`;
      }
    }

    return sanitized;
  }

  /**
   * 发送日志到外部服务（生产环境）
   */
  private sendToExternalLogger(logEntry: PayPalLogEntry) {
    // 这里可以集成外部日志服务，如 Sentry、LogRocket 等
    // 暂时只在控制台输出
    if (logEntry.level === PayPalLogLevel.ERROR) {
      // 可以发送到错误监控服务
    }
  }

  /**
   * 创建操作上下文
   */
  createContext(params: {
    orderId?: string;
    orderNo?: string;
    userId?: string;
    requestId?: string;
  }) {
    return params;
  }
}

// 导出单例实例
export const paypalLogger = new PayPalLogger();

/**
 * PayPal错误类
 */
export class PayPalError extends Error {
  public readonly operation: PayPalOperation;
  public readonly code?: string;
  public readonly details?: any;
  public readonly context?: any;

  constructor(
    operation: PayPalOperation,
    message: string,
    code?: string,
    details?: any,
    context?: any
  ) {
    super(message);
    this.name = 'PayPalError';
    this.operation = operation;
    this.code = code;
    this.details = details;
    this.context = context;

    // 自动记录错误日志
    paypalLogger.error(operation, message, this, { details, context });
  }
}

/**
 * PayPal API错误处理工具
 */
export class PayPalErrorHandler {
  /**
   * 处理PayPal API响应错误
   */
  static handleApiError(operation: PayPalOperation, response: any, context?: any): PayPalError {
    const message = response.message || response.error_description || 'PayPal API error';
    const code = response.name || response.error || 'UNKNOWN_ERROR';
    
    return new PayPalError(operation, message, code, response, context);
  }

  /**
   * 处理网络错误
   */
  static handleNetworkError(operation: PayPalOperation, error: any, context?: any): PayPalError {
    const message = `Network error: ${error.message || 'Unknown network error'}`;
    return new PayPalError(operation, message, 'NETWORK_ERROR', error, context);
  }

  /**
   * 处理验证错误
   */
  static handleValidationError(operation: PayPalOperation, message: string, context?: any): PayPalError {
    return new PayPalError(operation, `Validation error: ${message}`, 'VALIDATION_ERROR', undefined, context);
  }
}
